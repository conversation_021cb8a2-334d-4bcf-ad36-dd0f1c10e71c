package com.jinghang.capital.batch.domain.cybk;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKConfig {

    @Value("${cybk.sftp.username}")
    private String sftpUsername;
    @Value("${cybk.sftp.password}")
    private String sftpPassword;
    @Value("${cybk.sftp.host}")
    private String sftpHost;
    @Value("${cybk.sftp.port}")
    private Integer sftpPort;
    @Value("${cybk.sftp.upload.dir}")
    private String uploadDir;

    @Value("${cybk.sftp.download.dir}")
    private String downloadDir;

    @Value("${cybk.loanType}")
    private String loanType;

    @Value("${mayi.sftp.username}")
    private String antSftpUsername;
    @Value("${mayi.sftp.password}")
    private String antSftpPassword;
    @Value("${mayi.sftp.host}")
    private String antSftpHost;
    @Value("${mayi.sftp.port}")
    private Integer antSftpPort;
    @Value("${mayi.sftp.upload.dir}")
    private String antUploadDir;

    public String getLoanType() {
        return loanType;
    }

    public String getSftpUsername() {
        return sftpUsername;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getDownloadDir() {
        return downloadDir;
    }

    public String getUploadDir() {
        return uploadDir;
    }

    public String getAntSftpUsername() {
        return antSftpUsername;
    }

    public String getAntSftpPassword() {
        return antSftpPassword;
    }

    public String getAntSftpHost() {
        return antSftpHost;
    }

    public Integer getAntSftpPort() {
        return antSftpPort;
    }

    public String getAntUploadDir() {
        return antUploadDir;
    }
}

package com.jinghang.capital.batch.domain.cybk;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class CYBKHandlerFactory {
    private Map<CYBKReccFileTypeEnum, CYBKReconFileHandler> cybkReccHandlerMap = new HashMap<>();

    public CYBKReconFileHandler get(String key) {
        CYBKReccFileTypeEnum typeEnum = CYBKReccFileTypeEnum.valueOf(key);
        return cybkReccHandlerMap.get(typeEnum);
    }

    @Autowired
    public void setHandlers(List<CYBKReconFileHandler> handles) {
        for (CYBKReconFileHandler handle : handles) {
            cybkReccHandlerMap.put(handle.getReccType(), handle);
        }
    }
}

package com.jinghang.capital.batch.domain.cybk;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileMode;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.domain.product.LoanReccDto;
import com.jinghang.capital.batch.entity.*;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;
import com.jinghang.capital.batch.mapper.LoanMapper;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.WarningService;
import com.jinghang.capital.batch.service.cybk.CYBKReccLoanService;
import com.jinghang.capital.batch.service.tc.ILoanService;
import com.jinghang.capital.batch.service.tc.impl.CreditServiceImpl;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.util.SftpUtil;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 放款对账
 * <p>
 * 流水号|放款编号|放款完成日期|放款期数|放款成功金额|放款结果|资产池编号
 * RN230825154141780679103841725418|LA1HXXR1KIMF400|20230825|12|15000.00|00|APF20Q65EY1M
 */
@Component
public class CYBKLoanFileHandler extends AbstractCYBKReconFileHandler<CYBKReccLoan> {

    private static final Logger logger = LoggerFactory.getLogger(CYBKLoanFileHandler.class);

    private static final int LOAN_ID_IDX = 0;
    private static final int CUST_ID_IDX = 1;
    private static final int LOAN_APPLY_TIME_IDX = 2;
    private static final int LOAN_APPLY_ID_IDX = 3;
    private static final int LOAN_CONTRACT_NO_IDX = 4;
    private static final int LOAN_NO_IDX = 5;
    private static final int AMOUNT_IDX = 6;
    private static final int PERIOD_IDX = 7;
    private static final int RATE_IDX = 8;
    private static final int LOAN_TIME_IDX = 9;
    private static final int DUE_DATE_IDX = 10;
    private static final int NAME_IDX = 11;
    private static final int BANK_IDX = 12;
    private static final int ACCT_IDX = 13;


    //  /upload/cyxf/{产品编码}/in/files/{YYYYMMDD}/loan_${yyyyMMdd}.csv
    // 长银借据明细文件
    private static final String RECC_FILE_NAME = "%s/loan_%s.csv";
    private static final Integer LINE_LENGTH = 14;

    @Autowired
    private CreditServiceImpl creditService;
    @Autowired
    private CYBKReccLoanService reccLoanService;
    @Autowired
    private FileService fileService;
    @Autowired
    private CYBKConfig config;
    @Autowired
    private ILoanService loanService;
    @Autowired
    private WarningService warningService;


    @Override
    protected String ossFilePath(LocalDate data) {
        return "cybk/recc/loan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return getSftpPathPrefix() + "/" + getFileName(data);
    }

    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<CYBKReccLoan> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<CYBKReccLoan> entityList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("长银消金直连解析数据异常,line:" + (entityList.size() + 1) + "__" + line);
                }

                String loanNo = lineArr[LOAN_ID_IDX];

                if (!loanNo.startsWith("LO")) {
                    logger.info("reccLoan loanId: [{}] 非本平台固有借据号格式 忽略", loanNo);
                    continue;
                }
                Loan loan = getLoanService().findByIdAndChannel(loanNo, BankChannel.CYBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccLoan loanId: [{}] 未查到数据库匹配信息 忽略", loanNo);
                    continue;
                }

                //保存长银消金直连放款数据
                CYBKReccLoan entity = new CYBKReccLoan();
                if (loan != null) {
                    entity.setSysId(loan.getId());
                }
                entity.setOutAppSeq(lineArr[LOAN_ID_IDX]);
                entity.setCustId(lineArr[CUST_ID_IDX]);
                entity.setApplyDt(lineArr[LOAN_APPLY_TIME_IDX]);
                entity.setApplSeq(lineArr[LOAN_APPLY_ID_IDX]);
                entity.setContNo(lineArr[LOAN_CONTRACT_NO_IDX]);
                entity.setBasicIntRat(lineArr[RATE_IDX]);
                entity.setAmount(new BigDecimal(lineArr[AMOUNT_IDX]));
                entity.setLoanNo(loanNo);
                entity.setPeriod(Integer.valueOf(lineArr[PERIOD_IDX]));
                entity.setLoanTime(LocalDate.parse(lineArr[LOAN_TIME_IDX], DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                entity.setChannel(BankChannel.CYBK.name());
                entityList.add(entity);
            }
        } catch (Exception e) {
            logger.error("cybk loan file recc error.", e);
            throw new RuntimeException("长银消金直连解析文件失败", e);
        }
        return entityList;
    }


    public void processTszRecFile(LocalDate fileDate, List<CYBKReccLoan> entityList) {
        List<LoanReccDto> result = new ArrayList<>();
        for (CYBKReccLoan reccLoan : entityList) {
            Loan loan = getLoanService().findByIdAndChannel(reccLoan.getSysId(), BankChannel.CYBK.name());
            if (loan != null) {
                LoanReccDto tszRecc = toTszRecc(reccLoan);
                tszRecc.setOutLoanId(loan.getOuterLoanId());
                tszRecc.setChannel(BankChannel.CYBK.name());
                result.add(tszRecc);
            }
        }

        try {
            Path tempFile = Files.createTempFile("tsz_loan_recc", "txt");
            BufferedWriter writer = Files.newBufferedWriter(tempFile);
            for (LoanReccDto dto : result) {
                writer.write(dto.getFormattedTxt());
                writer.newLine();
            }
            writer.close();

            String targetFileName = fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "_loanCheck.txt";
            String ossPath = "tsz/recc/cybk/loan/" + targetFileName;
            fileService.switchOss(ossBucketName()).putObject(ossBucketName(), ossPath, Files.newInputStream(tempFile));

            ReconciliationFile tszRecFile = new ReconciliationFile();

            tszRecFile.setProduct(Product.ZC_CASH);
            tszRecFile.setBankChannel(BankChannel.CYBK);
            tszRecFile.setFileType(FileType.LOAN_FILE);
            tszRecFile.setFileName(targetFileName);
            tszRecFile.setFileDate(fileDate);
            tszRecFile.setMode(FileMode.OSS);
            tszRecFile.setReconciliationState(ReccStateEnum.P);
            tszRecFile.setTargetOssBucket(ossBucketName());
            tszRecFile.setTargetOssKey(ossPath);

            getReconciliationFileService().save(tszRecFile);

            Files.delete(tempFile);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    private LoanReccDto toTszRecc(CYBKReccLoan reccLoan) {
        LoanReccDto dto = new LoanReccDto();
        dto.setLoanId(reccLoan.getSysId());
        dto.setAmount(reccLoan.getAmount());
        dto.setPeriod(reccLoan.getPeriod());
        dto.setLoanTime(reccLoan.getLoanTime());
        dto.setStatus(reccLoan.getStatus());
        dto.setRemark(reccLoan.getRemark());
        return dto;
    }

    @Override
    protected void saveEntity(List<CYBKReccLoan> entityList) {
        reccLoanService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<CYBKReccLoan> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });

    }


    @Override
    public CYBKReccFileTypeEnum getReccType() {
        return CYBKReccFileTypeEnum.LOAN_FILE;
    }


    @Override
    public void pushToAnt(ReccUploadAntJobParamEntity jobEntity) {
        LocalDate fileDate = LocalDate.parse(jobEntity.getFileDate());
        boolean needCover = jobEntity.isNeedCover();
        String sftpFilePath = sftpFilePath(fileDate);
        String sftpOkFilePath = sftpFilePath + ".ok";

        logger.info("[长银对账文件上传蚂蚁job]开始处理长银消金直连对账文件: [{}] 和 [{}]", sftpFilePath, sftpOkFilePath);

        try {
            super.sftp.custom(channelSftp -> {
                // 1. 处理主csv文件
                processAndUploadFile(channelSftp, sftpFilePath, fileDate, true, needCover);

                // 2. 处理.ok文件（直接上传，不修改内容）
                try {
                    processAndUploadFile(channelSftp, sftpOkFilePath, fileDate, false, needCover);
                } catch (Exception e) {
                    logger.warn("[长银对账文件上传蚂蚁job].ok文件处理失败，可能文件不存在，继续执行", e);
                }
            });
        } catch (Exception e) {
            warningService.warn("[长银对账文件上传蚂蚁job]长银消金直连对账文件处理失败|%s".formatted(e.getMessage()),
                    msg -> logger.error(msg, e));
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 通用文件处理方法
     *
     * @param channelSftp    SFTP通道
     * @param sourcePath     源文件路径
     * @param fileDate       文件日期
     * @param needProcessing 是否需要处理文件内容
     */
    private void processAndUploadFile(ChannelSftp channelSftp, String sourcePath, LocalDate fileDate, boolean needProcessing, boolean needCover) {
        try (InputStream inputStream = channelSftp.get(sourcePath)) {
            byte[] fileContent;
            if (needProcessing) {
                fileContent = processFileWithCreditId(inputStream); // 处理csv文件
            } else {
                fileContent = inputStream.readAllBytes(); // 直接读取.ok文件内容
            }

            // 构建目标路径
            String targetFileName = sourcePath.substring(sourcePath.lastIndexOf('/') + 1);
            String remotePath = config.getAntUploadDir() + "loan/"
                    + fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + targetFileName;

            super.antSftp.custom(antChannelSftp -> {
                // 创建目录逻辑（保持不变）
                String dirPath = remotePath.substring(0, remotePath.lastIndexOf('/'));
                String remotePathAbs = "";
                try {
                    String currentDir = antChannelSftp.pwd();
                    dirPath = currentDir + dirPath;
                    remotePathAbs = currentDir + remotePath;

                    // 检查文件是否存在
                    if (!needCover) {
                        try {
                            antChannelSftp.stat(remotePathAbs);
                            logger.warn("[长银对账文件上传蚂蚁job]文件已存在且不覆盖: {}", remotePathAbs);
                            return; // 文件存在且不覆盖，直接返回
                        } catch (SftpException e) {
                            if (e.id != ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                                throw e; // 非"文件不存在"异常则抛出
                            }
                        }
                    }

                    mkdirs(antChannelSftp, dirPath);
                } catch (SftpException e) {
                    e.printStackTrace();
                }


                // 上传文件
                try (InputStream antInputStream = new ByteArrayInputStream(fileContent)) {
                    antChannelSftp.put(antInputStream, remotePathAbs);
                    logger.info("[长银对账文件上传蚂蚁job]成功上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                } catch (IOException e) {
                    e.printStackTrace();
                    warningService.warn("[长银对账文件上传蚂蚁job]异常，上传文件到蚂蚁 SFTP。" + e.getMessage());
                    logger.error("[长银对账文件上传蚂蚁job]IO异常，上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                } catch (SftpException e) {
                    e.printStackTrace();
                    warningService.warn("[长银对账文件上传蚂蚁job]异常，上传文件到蚂蚁 SFTP。" + e.getMessage());
                    logger.error("[长银对账文件上传蚂蚁job]SftpException异常，上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                    throw new RuntimeException(e);
                }
            });
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                throw new RuntimeException("[长银对账文件上传蚂蚁job]文件不存在: " + sourcePath, e);
            }
            throw new RuntimeException("[长银对账文件上传蚂蚁job]SFTP操作失败", e);
        } catch (IOException e) {
            throw new RuntimeException("[长银对账文件上传蚂蚁job]文件处理失败", e);
        } catch (com.jinghang.common.sftp.exception.SftpException e) {
            logger.error("[长银对账文件上传蚂蚁job]SFTP工具异常", e);
            throw new RuntimeException("SFTP工具异常", e);
        }
    }

    /**
     * 创建目录（复用原有逻辑）
     * todo 测试
     */
    private void mkdirs(ChannelSftp channelSftp, String dirPath) throws SftpException {
        try {
            channelSftp.cd(dirPath);
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                String[] folders = dirPath.split("/");
                String currentPath = "";
                for (String folder : folders) {
                    if (folder.isEmpty()) continue;
                    currentPath += "/" + folder;
                    try {
                        channelSftp.cd(currentPath);
                    } catch (SftpException ex) {
                        channelSftp.mkdir(currentPath);
                        channelSftp.cd(currentPath);
                    }
                }
            }
        }
    }


    private byte[] processFileWithCreditId(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {

            String line;
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                if (isFirstLine) {
                    // 处理header行，在第二列位置插入credit_id
                    String[] headers = line.split(",", -1);
                    if (headers.length > 1) {
                        writer.write(headers[0] + ",credit_id");
                        for (int i = 1; i < headers.length; i++) {
                            writer.write("," + headers[i]);
                        }
                    } else {
                        writer.write(line + ",credit_id");
                    }
                    isFirstLine = false;
                } else {
                    // 处理数据行，在第二列位置插入credit_id数据
                    String[] columns = line.split(",", -1);
                    if (columns.length > 1) {
                        writer.write(columns[0] + "," + getCreditId(columns));
                        for (int i = 1; i < columns.length; i++) {
                            writer.write("," + columns[i]);
                        }
                    } else {
                        writer.write(line + "," + getCreditId(columns));
                    }
                }
                writer.newLine();
            }
        }
        return outputStream.toByteArray();
    }


    // 获取credit_id的方法，根据实际业务需求实现
    private String getCreditId(String[] columns) {
        String loanId = columns[0];
        try {
            Loan loan = loanService.getById(loanId);
            if (null != loan && null != loan.getId()) {
                Credit credit = creditService.getById(loan.getCreditId());
                return credit.getCreditNo();
            }
            logger.info("[长银对账文件上传蚂蚁job]放款明细文件解析后，loanId不存在与loan表；{}", loanId);
            return "";
        } catch (RuntimeException e) {
            logger.error("[长银对账文件上传蚂蚁job]放款明细文件解析后，loanId不存在与loan表；{}", loanId, e);
            return "";
        }

    }
}

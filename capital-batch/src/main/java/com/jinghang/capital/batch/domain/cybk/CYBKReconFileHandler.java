package com.jinghang.capital.batch.domain.cybk;

import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;

public interface CYBKReconFileHandler {

    void handle(ReccJobParamEntity jobEntity);

    CYBKReccFileTypeEnum getReccType();

    /**
     *
     * @param jobEntity
     */
    void pushToAnt(ReccUploadAntJobParamEntity jobEntity);
}

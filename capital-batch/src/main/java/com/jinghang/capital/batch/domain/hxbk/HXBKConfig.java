package com.jinghang.capital.batch.domain.hxbk;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HXBKConfig {

    @Value("${mayi.sftp.username}")
    private String sftpUsername;
    @Value("${mayi.sftp.password}")
    private String sftpPassword;
    @Value("${mayi.sftp.host}")
    private String sftpHost;
    @Value("${mayi.sftp.port}")
    private Integer sftpPort;
    @Value("${mayi.sftp.download.loan}")
    private String loanDir;
    @Value("${mayi.sftp.download.repay}")
    private String repayDir;
    @Value("${mayi.sftp.download.contract}")
    private String contractDir;
    @Value("${mayi.sftp.download.idcard}")
    private String idCardDir;
    @Value("${mayi.sftp.download.photo}")
    private String photoDir;
    /**
     * 湖消放款超时时间配置
     */
    @Value("${hxbk.loan.timeout.conf}")
    private String loanTimeoutConf;

    /**
     * 湖消还款超时时间配置
     */
    @Value("${hxbk.repay.timeout.conf}")
    private String repayTimeoutConf;

    public String getSftpUsername() {
        return sftpUsername;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpUsername( String sftpUsername ) {
        this.sftpUsername = sftpUsername;
    }

    public void setSftpPassword( String sftpPassword ) {
        this.sftpPassword = sftpPassword;
    }

    public void setSftpHost( String sftpHost ) {
        this.sftpHost = sftpHost;
    }

    public void setSftpPort( Integer sftpPort ) {
        this.sftpPort = sftpPort;
    }

    public String getLoanDir() {
        return loanDir;
    }

    public void setLoanDir( String loanDir ) {
        this.loanDir = loanDir;
    }

    public String getRepayDir() {
        return repayDir;
    }

    public void setRepayDir( String repayDir ) {
        this.repayDir = repayDir;
    }

    public String getContractDir() {
        return contractDir;
    }

    public void setContractDir( String contractDir ) {
        this.contractDir = contractDir;
    }

    public String getIdCardDir() {
        return idCardDir;
    }

    public void setIdCardDir( String idCardDir ) {
        this.idCardDir = idCardDir;
    }

    public String getPhotoDir() {
        return photoDir;
    }

    public void setPhotoDir( String photoDir ) {
        this.photoDir = photoDir;
    }

    public String getRepayTimeoutConf() {
        return repayTimeoutConf;
    }

    public void setRepayTimeoutConf(String repayTimeoutConf) {
        this.repayTimeoutConf = repayTimeoutConf;
    }


    public String getLoanTimeoutConf() {
        return loanTimeoutConf;
    }

    public void setLoanTimeoutConf(String loanTimeoutConf) {
        this.loanTimeoutConf = loanTimeoutConf;
    }
}

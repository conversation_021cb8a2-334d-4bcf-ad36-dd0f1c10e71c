package com.jinghang.capital.batch.domain.hxbk;

import com.jinghang.capital.batch.domain.cybk.CYBKReccFileTypeEnum;
import com.jinghang.capital.batch.domain.cybk.CYBKReconFileHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/09 16:49
 */
@Component
public class HXBKHandlerFactory {

  private Map<HXBKReccFileTypeEnum, HXBKReconFileHandler> hxbkReccHandlerMap = new HashMap<>();

  public HXBKReconFileHandler get( String key ) {
    HXBKReccFileTypeEnum typeEnum = HXBKReccFileTypeEnum.valueOf(key);
    return hxbkReccHandlerMap.get(typeEnum);
  }

  @Autowired
  public void setHandlers( List<HXBKReconFileHandler> handles ) {
    for (HXBKReconFileHandler handle : handles) {
      hxbkReccHandlerMap.put(handle.getReccType(), handle);
    }
  }

}

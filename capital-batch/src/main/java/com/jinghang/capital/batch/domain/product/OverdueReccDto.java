package com.jinghang.capital.batch.domain.product;

import java.math.BigDecimal;

public class OverdueReccDto {
    private static final String SPLIT = "|";

    /**
     * core放款ID
     */
    private String loanId;
    /**
     * 业务系统放款ID
     */
    private String outLoanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 最新罚息
     */
    private BigDecimal penalty;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanId() {
        return outLoanId;
    }

    public void setOutLoanId(String outLoanId) {
        this.outLoanId = outLoanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public String getFormattedTxt() {
        // core放款ID|业务系统放款ID|期数|最新罚息
        return loanId
            + SPLIT + outLoanId
            + SPLIT + period
            + SPLIT + penalty;
    }
}

package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@TableName("account")
public class Account implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String certNo;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 出生日期
     */
    private LocalDate birthDay;

    /**
     * 身份证地址
     */
    private String certAddress;

    /**
     * 签发机关
     */
    private String certSignOrg;

    /**
     * 身份证有效期起始
     */
    private LocalDate certValidStart;

    /**
     * 身份证有效期截至
     */
    private LocalDate certValidEnd;

    /**
     * 省行政区划
     */
    private String provinceCode;

    /**
     * 市行政区划
     */
    private String cityCode;

    /**
     * 区行政区划
     */
    private String districtCode;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 婚姻状况
     */
    private String marriage;

    /**
     * 最高学历
     */
    private String education;

    /**
     * 月收入
     */
    private Integer income;

    /**
     * 行业
     */
    private String industry;

    /**
     * 职业
     */
    private String position;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 人脸识别通道
     */
    private String faceChannel;

    /**
     * 人脸识别时间
     */
    private String faceTime;

    /**
     * 人脸识别分
     */
    private String faceScore;

    /**
     * 居住地址
     */
    private String livingAddress;

    /**
     * 居住省份
     */
    private String livingProvinceCode;

    /**
     * 居住市
     */
    private String livingCityCode;

    /**
     * 居住区
     */
    private String livingDistrictCode;

    /**
     * 街道
     */
    private String livingStreet;

    /**
     * 工作单位
     */
    private String unit;

    /**
     * 工作单位地址
     */
    private String unitAddress;

    /**
     * 工作单位省份
     */
    private String unitProvinceCode;

    /**
     * 工作单位市
     */
    private String unitCityCode;

    /**
     * 工作单位区
     */
    private String unitDistrictCode;

    /**
     * 街道
     */
    private String unitStreet;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public LocalDate getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(LocalDate birthDay) {
        this.birthDay = birthDay;
    }

    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public String getCertSignOrg() {
        return certSignOrg;
    }

    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    public LocalDate getCertValidStart() {
        return certValidStart;
    }

    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public Integer getIncome() {
        return income;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(String faceChannel) {
        this.faceChannel = faceChannel;
    }

    public String getFaceTime() {
        return faceTime;
    }

    public void setFaceTime(String faceTime) {
        this.faceTime = faceTime;
    }

    public String getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(String faceScore) {
        this.faceScore = faceScore;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public String getUnitProvinceCode() {
        return unitProvinceCode;
    }

    public void setUnitProvinceCode(String unitProvinceCode) {
        this.unitProvinceCode = unitProvinceCode;
    }

    public String getUnitCityCode() {
        return unitCityCode;
    }

    public void setUnitCityCode(String unitCityCode) {
        this.unitCityCode = unitCityCode;
    }

    public String getUnitDistrictCode() {
        return unitDistrictCode;
    }

    public void setUnitDistrictCode(String unitDistrictCode) {
        this.unitDistrictCode = unitDistrictCode;
    }

    public String getUnitStreet() {
        return unitStreet;
    }

    public void setUnitStreet(String unitStreet) {
        this.unitStreet = unitStreet;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

}

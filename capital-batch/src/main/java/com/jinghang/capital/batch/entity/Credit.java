package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 授信表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@TableName("credit")
public class Credit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    private String guaranteeCompany;

    /**
     * 用户id
     */
    private String accountId;

    /**
     * 门店id
     */
    private String shopId;

    /**
     * 外部id
     */
    private String outerCreditId;

    /**
     * 资方渠道
     */
    private String channel;

    /**
     * 申请金额
     */
    private BigDecimal creditAmt;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 通过时间
     */
    private LocalDateTime passTime;

    /**
     * 资方有效期
     */
    private LocalDateTime capExpireTime;

    /**
     * 申请期数
     */
    private Integer periods;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 流量渠道
     */
    private String flowChannel;

    /**
     * 卡号
     */
    private String cardId;

    /**
     * 授信状态
     */
    private String creditStatus;

    /**
     * 资方授信编号
     */
    private String creditNo;

    /**
     * 授信合同编号
     */
    private String creditContractNo;

    /**
     * 用户姓名
     */
    private String custName;

    /**
     * 用户手机
     */
    private String custMobile;

    /**
     * 用户身份证
     */
    private String custCertNo;

    /**
     * 对客费率
     */
    private BigDecimal customRate;

    /**
     * 对资费率
     */
    private BigDecimal bankRate;

    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(String guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getOuterCreditId() {
        return outerCreditId;
    }

    public void setOuterCreditId(String outerCreditId) {
        this.outerCreditId = outerCreditId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDateTime getCapExpireTime() {
        return capExpireTime;
    }

    public void setCapExpireTime(LocalDateTime capExpireTime) {
        this.capExpireTime = capExpireTime;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCreditStatus() {
        return creditStatus;
    }

    public void setCreditStatus(String creditStatus) {
        this.creditStatus = creditStatus;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustMobile() {
        return custMobile;
    }

    public void setCustMobile(String custMobile) {
        this.custMobile = custMobile;
    }

    public String getCustCertNo() {
        return custCertNo;
    }

    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
    }

    public BigDecimal getCustomRate() {
        return customRate;
    }

    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }
}

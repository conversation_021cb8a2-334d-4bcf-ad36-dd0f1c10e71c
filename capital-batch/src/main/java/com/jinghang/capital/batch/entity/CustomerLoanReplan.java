package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 对客实还款计划表;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@TableName("customer_loan_replan")
public class CustomerLoanReplan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 借款id
     */
    private String loanId;

    /**
     * 资方渠道
     */
    private String channel;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 对客还款状态;current, clear
     */
    private String repayStatus;

    /**
     * 实还时间
     */
    private LocalDateTime actRepayTime;

    /**
     * 实还总金额
     */
    private BigDecimal actTotalAmt;

    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;

    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;

    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;

    /**
     * 实还违约金
     */
    private BigDecimal actBreachAmt;

    /**
     * 实还融担费用
     */
    private BigDecimal actGuaranteeAmt;

    /**
     * 还款类型
     */
    private String repayType;

    /**
     * 还款模式;online,offline
     */
    private String repayMode;

    /**
     * 还款目的
     */
    private String repayPurpose;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(String repayStatus) {
        this.repayStatus = repayStatus;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getActTotalAmt() {
        return actTotalAmt;
    }

    public void setActTotalAmt(BigDecimal actTotalAmt) {
        this.actTotalAmt = actTotalAmt;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActBreachAmt() {
        return actBreachAmt;
    }

    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

}

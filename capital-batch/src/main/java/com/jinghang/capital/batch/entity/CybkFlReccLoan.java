package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 长银分润放款对账文件;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-31
 */
@TableName("cybk_fl_recc_loan")
public class CybkFlReccLoan extends BaseEntity implements Serializable {

    /**
     * recc_id
     */
    private String reccId;
    /**
     * 对账状态
     */
    private String reccStatus;
    /**
     * 对方业务号
     */
    private String outAppSeq;
    /**
     * 客户编号
     */
    private String custId;
    /**
     * 用款申请日期
     */
    private LocalDate applyDt;
    /**
     * 用款申请流水号
     */
    private String applSeq;
    /**
     * 合同号
     */
    private String contNo;
    /**
     * 借据号
     */
    private String loanNo;
    /**
     * 放款金额
     */
    private BigDecimal dnAmt;
    /**
     * 申请期限
     */
    private Integer applyTnr;
    /**
     * 年化利率
     */
    private BigDecimal basicIntRat;
    /**
     * 放款时间
     */
    private LocalDate loanActvDt;
    /**
     * 合同到期日
     */
    private LocalDate contEndDt;
    /**
     * 放款账户姓名
     */
    private String acctName;
    /**
     * 放款账户开户行
     */
    private String acctBank;
    /**
     * 放款账号
     */
    private String acctNo;

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctBank() {
        return acctBank;
    }

    public void setAcctBank(String acctBank) {
        this.acctBank = acctBank;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public LocalDate getContEndDt() {
        return contEndDt;
    }

    public void setContEndDt(LocalDate contEndDt) {
        this.contEndDt = contEndDt;
    }

    public LocalDate getLoanActvDt() {
        return loanActvDt;
    }

    public void setLoanActvDt(LocalDate loanActvDt) {
        this.loanActvDt = loanActvDt;
    }

    public BigDecimal getBasicIntRat() {
        return basicIntRat;
    }

    public void setBasicIntRat(BigDecimal basicIntRat) {
        this.basicIntRat = basicIntRat;
    }

    public Integer getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(Integer applyTnr) {
        this.applyTnr = applyTnr;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getContNo() {
        return contNo;
    }

    public void setContNo(String contNo) {
        this.contNo = contNo;
    }

    public String getApplSeq() {
        return applSeq;
    }

    public void setApplSeq(String applSeq) {
        this.applSeq = applSeq;
    }

    public LocalDate getApplyDt() {
        return applyDt;
    }

    public void setApplyDt(LocalDate applyDt) {
        this.applyDt = applyDt;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getOutAppSeq() {
        return outAppSeq;
    }

    public void setOutAppSeq(String outAppSeq) {
        this.outAppSeq = outAppSeq;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }
}

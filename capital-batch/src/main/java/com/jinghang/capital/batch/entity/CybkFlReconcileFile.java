package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 长银分润对账文件;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-31
 */
@TableName("cybk_fl_reconcile_file")
public class CybkFlReconcileFile extends BaseEntity implements Serializable {

    /**
     * 产品
     */
    private String product;
    /**
     * 资方
     */
    private String channel;
    /**
     * 对账文件类型
     */
    private String reccType;
    /**
     * 文件日期
     */
    private LocalDate fileDate;
    /**
     * 对账日期
     */
    private LocalDate reccDate;
    /**
     * 对账状态
     */
    private String reccState;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 来源文件bucket
     */
    private String ossBucket;
    /**
     * 来源文件key
     */
    private String ossKey;
    /**
     * 来源文件oss
     */
    private String ossUrl;

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public LocalDate getReccDate() {
        return reccDate;
    }

    public void setReccDate(LocalDate reccDate) {
        this.reccDate = reccDate;
    }

    public String getReccState() {
        return reccState;
    }

    public void setReccState(String reccState) {
        this.reccState = reccState;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getOssUrl() {
        return ossUrl;
    }

    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }
}

package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileMode;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.SyncState;
import com.jinghang.capital.batch.enums.ReccStateEnum;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@TableName("reconciliation_file")
public class ReconciliationFile extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = 5535683467442004138L;

    /**
     * 产品
     */
    private Product product;

    /**
     * 资方
     */
    private BankChannel bankChannel;

    /**
     * 对账文件类型
     */
    private FileType fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件日期
     */
    private LocalDate fileDate;

    /**
     * 文件输入地址
     */
    private String inputUrl;

    /**
     * 文件输出地址
     */
    private String outputUrl;

    /**
     * 文件交互方式
     */
    private FileMode mode;

    /**
     * 来源文件bucket
     */
    private String sourceOssBucket;

    /**
     * 来源文件key
     */
    private String sourceOssKey;

    /**
     * 来源文件oss
     */
    private String sourceOssUrl;

    /**
     * 对账状态
     */
    private ReccStateEnum reconciliationState;

    /**
     * 文件输出bucket
     */
    private String targetOssBucket;

    /**
     * 文件输出key
     */
    private String targetOssKey;


    /**
     * 同步状态
     */
    private SyncState syncState;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getInputUrl() {
        return inputUrl;
    }

    public void setInputUrl(String inputUrl) {
        this.inputUrl = inputUrl;
    }

    public String getOutputUrl() {
        return outputUrl;
    }

    public void setOutputUrl(String outputUrl) {
        this.outputUrl = outputUrl;
    }


    public String getSourceOssBucket() {
        return sourceOssBucket;
    }

    public void setSourceOssBucket(String sourceOssBucket) {
        this.sourceOssBucket = sourceOssBucket;
    }

    public String getSourceOssKey() {
        return sourceOssKey;
    }

    public void setSourceOssKey(String sourceOssKey) {
        this.sourceOssKey = sourceOssKey;
    }

    public String getSourceOssUrl() {
        return sourceOssUrl;
    }

    public void setSourceOssUrl(String sourceOssUrl) {
        this.sourceOssUrl = sourceOssUrl;
    }

    public String getTargetOssBucket() {
        return targetOssBucket;
    }

    public void setTargetOssBucket(String targetOssBucket) {
        this.targetOssBucket = targetOssBucket;
    }

    public String getTargetOssKey() {
        return targetOssKey;
    }

    public void setTargetOssKey(String targetOssKey) {
        this.targetOssKey = targetOssKey;
    }

    public SyncState getSyncState() {
        return syncState;
    }

    public void setSyncState(SyncState syncState) {
        this.syncState = syncState;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public FileMode getMode() {
        return mode;
    }

    public void setMode(FileMode mode) {
        this.mode = mode;
    }

    public ReccStateEnum getReconciliationState() {
        return reconciliationState;
    }

    public void setReconciliationState(ReccStateEnum reconciliationState) {
        this.reconciliationState = reconciliationState;
    }
}

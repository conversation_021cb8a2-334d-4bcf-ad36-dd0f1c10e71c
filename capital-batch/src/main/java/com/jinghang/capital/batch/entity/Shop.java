package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 门店;
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@TableName("shop")
public class Shop implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 组织机构代码
     */
    private String unifiedCode;

    /**
     * 登记地址
     */
    private String licAddress;

    /**
     * 省代码
     */
    private String licProvinceCode;

    /**
     * 市代码
     */
    private String licCityCode;

    /**
     * 区代码
     */
    private String licDistrictCode;

    /**
     * 营业执照有效期开始
     */
    private LocalDate licStart;

    /**
     * 营业执照有效期结束
     */
    private LocalDate licEnd;

    /**
     * 实际营业地址
     */
    private String realAddress;

    /**
     * 实际省代码
     */
    private String realProvinceCode;

    /**
     * 实际市代码
     */
    private String realCityCode;

    /**
     * 实际区代码
     */
    private String realDistrictCode;

    /**
     * 法人姓名
     */
    private String legalName;

    /**
     * 法人手机
     */
    private String legalMobile;

    /**
     * 联系人姓名
     */
    private String linkmanName;

    /**
     * 联系人手机
     */
    private String linkmanMobile;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnifiedCode() {
        return unifiedCode;
    }

    public void setUnifiedCode(String unifiedCode) {
        this.unifiedCode = unifiedCode;
    }

    public String getLicAddress() {
        return licAddress;
    }

    public void setLicAddress(String licAddress) {
        this.licAddress = licAddress;
    }

    public String getLicProvinceCode() {
        return licProvinceCode;
    }

    public void setLicProvinceCode(String licProvinceCode) {
        this.licProvinceCode = licProvinceCode;
    }

    public String getLicCityCode() {
        return licCityCode;
    }

    public void setLicCityCode(String licCityCode) {
        this.licCityCode = licCityCode;
    }

    public String getLicDistrictCode() {
        return licDistrictCode;
    }

    public void setLicDistrictCode(String licDistrictCode) {
        this.licDistrictCode = licDistrictCode;
    }

    public LocalDate getLicStart() {
        return licStart;
    }

    public void setLicStart(LocalDate licStart) {
        this.licStart = licStart;
    }

    public LocalDate getLicEnd() {
        return licEnd;
    }

    public void setLicEnd(LocalDate licEnd) {
        this.licEnd = licEnd;
    }

    public String getRealAddress() {
        return realAddress;
    }

    public void setRealAddress(String realAddress) {
        this.realAddress = realAddress;
    }

    public String getRealProvinceCode() {
        return realProvinceCode;
    }

    public void setRealProvinceCode(String realProvinceCode) {
        this.realProvinceCode = realProvinceCode;
    }

    public String getRealCityCode() {
        return realCityCode;
    }

    public void setRealCityCode(String realCityCode) {
        this.realCityCode = realCityCode;
    }

    public String getRealDistrictCode() {
        return realDistrictCode;
    }

    public void setRealDistrictCode(String realDistrictCode) {
        this.realDistrictCode = realDistrictCode;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getLegalMobile() {
        return legalMobile;
    }

    public void setLegalMobile(String legalMobile) {
        this.legalMobile = legalMobile;
    }

    public String getLinkmanName() {
        return linkmanName;
    }

    public void setLinkmanName(String linkmanName) {
        this.linkmanName = linkmanName;
    }

    public String getLinkmanMobile() {
        return linkmanMobile;
    }

    public void setLinkmanMobile(String linkmanMobile) {
        this.linkmanMobile = linkmanMobile;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

}

package com.jinghang.capital.batch.job;

import com.fasterxml.jackson.core.JsonProcessingException;

import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;

/**
 * 单一执行job
 * fileType 在各自的处理中解析
 */

public abstract class AbstractSingleJob extends IJobHandler {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractSingleJob.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // {"reccDate": "2023-06-28", "fileType":""}     文件日期: yyyy-MM-dd
        ReccJobParamSingle jobEntity;
        try {
            jobEntity = JsonUtil.convertToObject(param, ReccJobParamSingle.class);
        } catch (JsonProcessingException e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        if (jobEntity == null) {
            jobEntity = new ReccJobParamSingle();
        }
        if (jobEntity.getReccDate() == null) {
            // default is yesterday
            if (!StringUtils.equalsAny(jobEntity.getFileType(), ReccType.PRE_COMPENSATION.name(), ReccType.PRE_REPURCHASE.name())) {
                jobEntity.setReccDate(LocalDate.now().minusDays(1L));
            } else {
                jobEntity.setReccDate(LocalDate.now());
            }
        }
        return doExecute(jobEntity);
    }

    public abstract ReturnT<String> doExecute(ReccJobParamSingle jobEntity) throws Exception;

}

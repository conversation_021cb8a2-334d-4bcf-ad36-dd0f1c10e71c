package com.jinghang.capital.batch.job;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * 文件类型fileType 设置有重写
 * <p>
 * {
 * WldJoParambEntity entity = new WldJoParambEntity();
 * entity.setFileDate(LocalDate.now().toString());
 * entity.setFileType(List.of("LOAN_FILE"));
 * entity.setFileType(FileType.LOAN_FILE.name());
 * entity.setEnableRewrite(true);
 * }
 */

public class ReccJobParamEntity implements Serializable {

    /**
     * 文件日期: yyyy-MM-dd
     */
    private String fileDate;

    /**
     * 文件类型
     */
    private List<String> fileType;

    /**
     * 是否允许重写
     *
     * @default: false
     */
    private Boolean enableRewrite = false;

    /**
     * 针对富民固收 下载新旧模式对账文件
     */
    private String channelType;

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }

    public List<String> getFileType() {
        return fileType;
    }

    public void setFileType(List<String> fileType) {
        this.fileType = fileType;
    }

    public Boolean getEnableRewrite() {
        return enableRewrite;
    }

    public void setEnableRewrite(Boolean enableRewrite) {
        this.enableRewrite = enableRewrite;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }
}

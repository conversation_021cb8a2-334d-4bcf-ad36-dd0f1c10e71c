package com.jinghang.capital.batch.job;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 对账文件单一执行参数类型
 * }
 */

public class ReccJobParamSingle implements Serializable {

    /**
     * 对账文件日期
     * 文件日期: yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reccDate;

    /**
     * 文件类型
     */
    private String fileType;

    public LocalDate getReccDate() {
        return reccDate;
    }

    public void setReccDate(LocalDate reccDate) {
        this.reccDate = reccDate;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
}

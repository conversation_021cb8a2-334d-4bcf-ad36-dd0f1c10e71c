package com.jinghang.capital.batch.job;

import java.io.Serializable;
import java.util.List;


public class ReccUploadAntJobParamEntity implements Serializable {

    /**
     * 文件日期: yyyy-MM-dd
     */
    private String fileDate;
    /**
     * 区间范围内文件获取并上传
     */
    private String startDate;
    /**
     * 区间范围内文件获取并上传
     */
    private String endDate;
    /**
     * 若存在相同文件名，是否覆盖掉老文件，默认覆盖
     */
    private boolean needCover;
    /**
     * 文件类型
     */
    private List<String> fileType;

    /**
     * 是否允许重写
     *
     * @default: false
     */
    private Boolean enableRewrite = false;

    /**
     * 针对富民固收 下载新旧模式对账文件
     */
    private String channelType;

    public boolean isNeedCover() {
        return needCover;
    }

    public void setNeedCover(boolean needCover) {
        this.needCover = needCover;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getFileDate() {
        return fileDate;
    }

    public void setFileDate(String fileDate) {
        this.fileDate = fileDate;
    }

    public List<String> getFileType() {
        return fileType;
    }

    public void setFileType(List<String> fileType) {
        this.fileType = fileType;
    }

    public Boolean getEnableRewrite() {
        return enableRewrite;
    }

    public void setEnableRewrite(Boolean enableRewrite) {
        this.enableRewrite = enableRewrite;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }
}

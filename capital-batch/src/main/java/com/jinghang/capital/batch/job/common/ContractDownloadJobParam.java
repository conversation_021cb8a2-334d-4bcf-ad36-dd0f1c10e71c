package com.jinghang.capital.batch.job.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Setter
@Getter
public class ContractDownloadJobParam {

    /**
     * 文件日期: yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate fileDate;

    /**
     * 资方
     */
    private String bankChannel;

    /**
     * 授信or借据ID
     */
    private String relateId;

    /**
     *  文件类型
     */
    private String fileType;
}

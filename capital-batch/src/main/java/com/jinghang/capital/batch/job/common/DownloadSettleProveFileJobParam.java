package com.jinghang.capital.batch.job.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Setter
@Getter
public class DownloadSettleProveFileJobParam {

    /**
     * 结清日期: yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate settleDate;
    /**
     * 资方
     */
    private String bankChannel;
}

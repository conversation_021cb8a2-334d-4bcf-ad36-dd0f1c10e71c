package com.jinghang.capital.batch.job.common;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.BusinessChronosProcessDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.capital.batch.remote.FinLoanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 重新获取借款合同编号
 */
@Component
@JobHandler("loanContractNoQueryJob")
public class LoanContractNoQueryJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(LoanContractNoQueryJob.class);

    @Autowired
    private FinLoanService loanService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        // {"channel": "LH_RL", "startDate":"2024-01-01", "endDate": "2024-01-03", "businessId": "**********"}

        if (StringUtil.isBlank(param)) {
            logger.error("process param is empty");
            return ReturnT.FAIL;
        }

        BusinessChronosParam processParam = JsonUtil.convertToObject(param, BusinessChronosParam.class);

        BankChannel channel = processParam.getChannel();
        if (null == channel) {
            logger.error("process channel is empty");
            return ReturnT.FAIL;
        }

        try {
            BusinessChronosProcessDto processDto = new BusinessChronosProcessDto();
            processDto.setBankChannel(channel);

            if (StringUtil.isNotEmpty(processParam.getBusinessId())) {
                processDto.setBusinessId(processParam.getBusinessId());
            } else {
                LocalDate startDate = processParam.getStartDate();
                LocalDate endDate = processParam.getEndDate();
                if (null == startDate || null == endDate) {
                    logger.error("process date is empty");
                    return ReturnT.FAIL;
                }
                processDto.setStartDate(startDate);
                processDto.setEndDate(endDate);
            }

            logger.info("channel: {},  loan contract query, content: {}", channel, JsonUtil.toJsonString(processDto));

            loanService.loanContractNoQuery(processDto);

        } catch (Exception ex) {
            logger.error("channel: {}, loan contract query exception", channel, ex);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}

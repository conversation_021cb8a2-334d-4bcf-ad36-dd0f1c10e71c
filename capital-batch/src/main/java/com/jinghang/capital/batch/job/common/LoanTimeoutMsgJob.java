package com.jinghang.capital.batch.job.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jinghang.capital.batch.domain.hxbk.HXBKConfig;
import com.jinghang.capital.batch.mapper.LoanMapper;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.service.WarningService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 放款超时消息提醒定时任务
 * 10分钟发起一次、数据查询范围：超过15分钟仍未返回终态的数据。异常则告警，人工介入
 *
 * @Author: zkj
 * @CreateTime: 2025/7/18
 */
@Component
@JobHandler("loanTimeoutMsgJob")
public class LoanTimeoutMsgJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(LoanTimeoutMsgJob.class);
    @Autowired
    private LoanMapper loanMapper;
    @Autowired
    private WarningService warningService;
    @Autowired
    private HXBKConfig hxbkConfig;

    /**
     * 放款超时消息提醒定时任务执行方法
     *
     * @param param 任务参数，JSON格式，包含reccDay字段
     * @return 执行结果
     * @throws Exception 执行异常
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("放款超时消息提醒定时任务=========执行开始");
        try {
            String loanTimeoutConf = hxbkConfig.getLoanTimeoutConf();//湖消放款超时时间配置(当前配置的10分钟)
            //当前时间戳减去配置的10分钟
            LocalDateTime dateTime = LocalDateTime.now().minusMinutes(Long.parseLong(loanTimeoutConf));
            //计算后的时间
            String countBackTime = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            logger.info("放款超时检查开始，配置超时时间：{}分钟，查询时间范围：<= {}",
                    hxbkConfig.getLoanTimeoutConf(), countBackTime);
            //放款状态不为终态和放款时间小于等于计算后的时间的放款记录信息列表
            List<Loan> loanList = findLoansNotInSuccessOrFail(countBackTime);
            logger.info("放款超时的记录数为：{}", loanList.size());
            if (CollectionUtils.isNotEmpty(loanList)) {
                loanList.forEach(loan -> getWarningService().warn(
                        String.format("主键为%s,借款id为：%s的放款记录超时未返回终态。请人工介入处理！",
                                loan.getId(), loan.getOuterLoanId())
                ));
            }
        } catch (Exception ex) {
            logger.error("放款超时消息提醒定时任务(LoanTimeoutMsgJob)=====执行异常", ex);
            return ReturnT.FAIL;
        }
        logger.info("放款超时消息提醒定时任务=========执行结束");
        return ReturnT.SUCCESS;
    }
    /**
     * 放款状态不为终态
     * 放款时间小于等于计算后的时间的放款表
     * @param countBackTime 计算后的时间
     * @return 放款表
     */
    private List<Loan> findLoansNotInSuccessOrFail(String countBackTime) {
        QueryWrapper<Loan> queryWrapper = new QueryWrapper<>();
        // 贷款状态不在SUCCESS和FAIL中
        queryWrapper.notIn("loan_status", "SUCCESS", "FAIL");
        // 贷款时间小于countBackTime
        queryWrapper.lt("loan_time", countBackTime);

        // 执行查询（假设有一个LoanMapper接口继承了BaseMapper<Loan>）
        return loanMapper.selectList(queryWrapper);
    }

    //引入发送企业微信的方法
    private WarningService getWarningService() {
        return warningService;
    }

}

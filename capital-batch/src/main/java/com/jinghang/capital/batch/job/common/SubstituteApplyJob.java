package com.jinghang.capital.batch.job.common;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.repay.SubstituteApplyDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.capital.batch.remote.FinRepayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 代还申请
 */
@Component
@JobHandler("substituteApplyJob")
public class SubstituteApplyJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(SubstituteApplyJob.class);

    @Autowired
    private FinRepayService repayService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        // {"substituteDay": "", "bankChannel": "", "guaranteeCompany": ""}
        LocalDate substituteApplyDay = null;
        BankChannel bankChannel = null;
        GuaranteeCompany guaranteeCompany = null;
        if (!StringUtil.isEmpty(param)) {
            SubstituteMarkParam markParam = JsonUtil.convertToObject(param, SubstituteMarkParam.class);
            substituteApplyDay = markParam.getSubstituteDay();
            bankChannel = BankChannel.valueOf(markParam.getBankChannel());
            guaranteeCompany = GuaranteeCompany.valueOf(markParam.getGuaranteeCompany());
        }

        if (null == substituteApplyDay) {
            substituteApplyDay = LocalDate.now();
        }

        logger.info("substitute apply, date: {}, bankChannel: {}, guaranteeCompany: {}",
            substituteApplyDay.format(DateTimeFormatter.ISO_DATE), bankChannel, guaranteeCompany);

        SubstituteApplyDto dto = new SubstituteApplyDto();
        dto.setBankChannel(bankChannel);
        dto.setGuaranteeCompany(guaranteeCompany);
        dto.setSubstituteDay(substituteApplyDay);
        logger.info("substitute apply, dto: {}", JsonUtil.toJsonString(dto));
        repayService.substituteApply(dto);
        return ReturnT.SUCCESS;
    }
}

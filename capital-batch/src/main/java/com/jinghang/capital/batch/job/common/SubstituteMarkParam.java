package com.jinghang.capital.batch.job.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Setter
@Getter
public class SubstituteMarkParam {

    /**
     * 代还日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate substituteDay;

    /**
     * 资方渠道
     */
    private String bankChannel;

    /**
     * 融担公司
     */
    private String guaranteeCompany;
}

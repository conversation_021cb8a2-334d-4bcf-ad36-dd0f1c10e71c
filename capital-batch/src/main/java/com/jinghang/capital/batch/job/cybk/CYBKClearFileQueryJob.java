package com.jinghang.capital.batch.job.cybk;

import com.jinghang.capital.batch.job.AbstractBaseJob;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.service.cybk.CYBKDownloadVoucherFileService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长银消金直连结清证明文件结果查询、下载
 * 20分钟一次，每次取30天内的记录
 */
@Component
@JobHandler("cybkClearFileQueryJob")
public class CYBKClearFileQueryJob extends AbstractBaseJob {

    @Autowired
    private CYBKDownloadVoucherFileService cybkDownloadVoucherFileService;

    @Override
    public ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception {

        logger.info("长银消金直连结清证明文件结果查询、下载 开始");
        try {
            cybkDownloadVoucherFileService.processCYBKDownloadVoucher();
        } catch (Exception ex) {
            logger.error("长银消金直连结清证明文件结果查询、下载 异常：", ex);
        }

        logger.info("长银消金直连结清证明文件结果查询、下载 结束");
        return ReturnT.SUCCESS;
    }

}

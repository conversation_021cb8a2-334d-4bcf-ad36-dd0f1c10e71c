package com.jinghang.capital.batch.job.cybk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.remote.FinFileService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */
@Component
@JobHandler("cybkContractDownloadJob")
public class CYBKContractDownloadJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKContractDownloadJob.class);

    private static final Long DEFAULT_DAY = 1L;

    private FinFileService finLoanFileService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        LocalDate processDay = LocalDate.now().minusDays(DEFAULT_DAY);
        try {
            CYBKReccParam reccParam = JsonUtil.convertToObject(param, CYBKReccParam.class);
            LocalDate paramDay = reccParam.getReccDay();
            if (null != paramDay) {
                processDay = paramDay;
            }
        } catch (Exception ex) {
            logger.error("cybkContractDownloadJob执行异常", ex);
            return ReturnT.FAIL;
        }
        logger.info("下载长银消金直连借款合同&额度合同, process job day: {}", processDay);
        return doDownloadContract(processDay);
    }

    public ReturnT<String> doDownloadContract(LocalDate processDay) {
        FileDailyProcessDto processDto = new FileDailyProcessDto();
        processDto.setBankChannel(BankChannel.CYBK);
        processDto.setProcessDate(processDay);
        processDto.setType(FileType.LOAN_CONTRACT);

        RestResult<Void> restResult = finLoanFileService.dailyProcess(processDto);
        logger.info("cybk contract download finished. {}", restResult);
        return ReturnT.SUCCESS;
    }

    @Autowired
    public void setFinLoanFileService(FinFileService finLoanFileService) {
        this.finLoanFileService = finLoanFileService;
    }
}

package com.jinghang.capital.batch.job.cybk.cust;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.remote.FinReccService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 长银消金直连 对客日终担保费文件
 */

@Component
@JobHandler("cybkCustDailyGuaranteeJob")
public class CYBKCustDailyGuaranteeJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustDailyGuaranteeJob.class);

    @Autowired
    private FinReccService reccService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        // 交易发生日是job的D-1日
        // {"reccDay": "2023-05-01"}, default is  yesterday
        LocalDate reccDay = LocalDate.now().minusDays(1L);
        ReccApplyDto reccApplyDto = new ReccApplyDto();
        reccApplyDto.setChannel(BankChannel.CYBK);

        try {
            CYBKReccParam reccParam = JsonUtil.convertToObject(param, CYBKReccParam.class);
            LocalDate paramDay = reccParam.getReccDay();
            if (null != paramDay) {
                reccDay = paramDay;
            }
        } catch (Exception ex) {
            // no throw
        }

        reccApplyDto.setReccType(ReccType.CUST_DAILY_GUARANTEE);
        reccApplyDto.setReccDay(reccDay);
        logger.info("长银消金直连对客日终担保费文件 request:{}", JsonUtil.toJsonString(reccApplyDto));
        return doProcessRecc(reccApplyDto);
    }

    public ReturnT<String> doProcessRecc(ReccApplyDto applyDto) throws Exception {

        RestResult<ReccResultDto> result = reccService.process(applyDto);
        logger.info("长银消金直连对客日终担保费文件 result. status: {}, content: {}", result.getCode(), result.getData());
        return ReturnT.SUCCESS;
    }
}

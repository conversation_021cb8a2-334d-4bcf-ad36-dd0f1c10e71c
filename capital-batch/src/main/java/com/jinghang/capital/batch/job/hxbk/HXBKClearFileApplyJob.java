package com.jinghang.capital.batch.job.hxbk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.job.hxbk.recc.HXBKReccParam;
import com.jinghang.capital.batch.remote.FinVoucherFileService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Objects;

/**
 * HXBK结清证明申请任务
 * 每日执行，处理前一天的结清数据
 *
 * @Author: Lior
 * @CreateTime: 2025/7/14 10:41
 */
@Component
@JobHandler("hxbkClearFileApplyJob")
public class HXBKClearFileApplyJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(HXBKClearFileApplyJob.class);
    private static final Long DEFAULT_DAY = 1L;

    @Autowired
    private FinVoucherFileService finVoucherFileService;

    /**
     * HXBK结清证明申请任务执行方法
     *
     * @param param 任务参数，JSON格式，包含reccDay字段
     * @return 执行结果
     * @throws Exception 执行异常
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        LocalDate processDay = LocalDate.now().minusDays(DEFAULT_DAY);
        try {
            if (Objects.nonNull(param)) {
                HXBKReccParam reccParam = JsonUtil.convertToObject(param, HXBKReccParam.class);
                LocalDate paramDay = reccParam.getReccDay();
                if (null != paramDay) {
                    processDay = paramDay;
                }
            }
        } catch (Exception ex) {
            logger.error("hxbkClearFileApplyJob执行异常", ex);
            return ReturnT.FAIL;
        }
        logger.info("申请下载湖消结清证明, process job day: {}", processDay);
        return doDownloadFile(processDay);
    }

    /**
     * 执行HXBK结清证明文件下载
     *
     * @param processDay 处理日期
     * @return 执行结果
     */
    private ReturnT<String> doDownloadFile(LocalDate processDay) {
        FileDailyProcessDto fileDailyProcessDto = new FileDailyProcessDto();
        fileDailyProcessDto.setProcessDate(processDay);
        fileDailyProcessDto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
        fileDailyProcessDto.setBankChannel(BankChannel.HXBK);
        finVoucherFileService.batchDownload(fileDailyProcessDto);
        return ReturnT.SUCCESS;
    }
}

package com.jinghang.capital.batch.job.hxbk;

import com.jinghang.capital.batch.job.AbstractBaseJob;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.service.hxbk.HXBKDownloadVoucherFileService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * HXBK结清证明文件结果查询、下载
 * 20分钟一次，每次取30天内的记录
 */
@Component
@JobHandler("hxbkClearFileQueryJob")
public class HXBKClearFileQueryJob extends AbstractBaseJob {

    @Autowired
    private HXBKDownloadVoucherFileService hxbkDownloadVoucherFileService;

    @Override
    public ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception {

        logger.info("HXBK结清证明文件结果查询、下载 开始");
        try {
            hxbkDownloadVoucherFileService.processHXBKDownloadVoucher();
        } catch (Exception ex) {
            logger.error("HXBK结清证明文件结果查询、下载 异常：", ex);
        }

        logger.info("HXBK结清证明文件结果查询、下载 结束");
        return ReturnT.SUCCESS;
    }

}

package com.jinghang.capital.batch.job.hxbk;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jinghang.capital.batch.domain.hxbk.HXBKConfig;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.mapper.BankRepayRecordMapper;
import com.jinghang.capital.batch.service.WarningService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * HXBK还款超时消息提醒定时任务
 * 10分钟发起一次、数据查询范围：超过10分钟仍未返回终态的数据。异常则告警，人工介入
 * @Author: lhf
 * @CreateTime: 2025/7/17 10:41
 */
@Component
@JobHandler("hxbkRepayTimeoutMsgJob")
public class HXBKRepayTimeoutMsgJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(HXBKRepayTimeoutMsgJob.class);
    @Autowired
    private BankRepayRecordMapper bankRepayRecordMapper;
    @Autowired
    private WarningService warningService;
    @Autowired
    private HXBKConfig hxbkConfig;

    /**
     * HXBK还款超时消息提醒定时任务执行方法
     * @param param 任务参数，JSON格式，包含reccDay字段
     * @return 执行结果
     * @throws Exception 执行异常
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("HXBK还款超时消息提醒定时任务=========执行开始");
        try {
            String repayTimeoutConf = hxbkConfig.getRepayTimeoutConf();//湖消还款超时时间配置(当前配置的10分钟)
            //当前时间戳减去配置的10分钟
            LocalDateTime dateTime = LocalDateTime.now().minusMinutes(Long.parseLong(repayTimeoutConf));
            //计算后的时间
            String countBackTime = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            //查询还款状态不为终态和还款时间小于等于计算后的时间的还款记录信息列表
            List<BankRepayRecord> bankRepayRecordList = bankRepayRecordMapper.findByRepayStatusAndCountBackTime(countBackTime);
            logger.info("还款超时的记录数为：{}", bankRepayRecordList.size());
            if(CollectionUtils.isNotEmpty(bankRepayRecordList)){
                for (BankRepayRecord bankRepayRecord : bankRepayRecordList) {
                    //还款超时15分钟，企业微信告警
                    getWarningService().warn("主键为" + bankRepayRecord.getId() + ",借款id为：" +
                            bankRepayRecord.getLoanId() + "的还款记录超时仍未返回终态。请人工介入处理！");
                }
            }
        } catch (Exception ex) {
            logger.error("HXBK还款超时消息提醒定时任务(hxbkRepayTimeoutMsgJob)=====执行异常", ex);
            return ReturnT.FAIL;
        }
        logger.info("HXBK还款超时消息提醒定时任务=========执行结束");
        return ReturnT.SUCCESS;
    }

    //引入发送企业微信的方法
    private WarningService getWarningService() {
        return warningService;
    }

}

package com.jinghang.capital.batch.job.hxbk.contract;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.job.hxbk.recc.HXBKReccParam;
import com.jinghang.capital.batch.remote.FinFileService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 湖消借款合同、征信授权书、仲裁协议下载任务
 * @作者 Mr.sandman
 * @时间 2025/07/11 15:52
 */
@Component
@JobHandler("hxbkContractDownloadJob")
public class HYBKContractDownloadJob extends IJobHandler {

  private static final Logger logger = LoggerFactory.getLogger(HYBKContractDownloadJob.class);

  private static final Long DEFAULT_DAY = 1L;

  private FinFileService finLoanFileService;

  @Override
  public ReturnT<String> execute( String param ) throws Exception {
    LocalDate processDay = LocalDate.now().minusDays(DEFAULT_DAY);
    try {
      HXBKReccParam reccParam = JsonUtil.convertToObject(param, HXBKReccParam.class);
      LocalDate paramDay = reccParam.getReccDay();
      if (null != paramDay) {
        processDay = paramDay;
      }
    } catch (Exception ex) {
      logger.error("hxbkContractDownloadJob执行异常", ex);
      return ReturnT.FAIL;
    }
    logger.info("下载湖消借款合同, process job day: {}", processDay);
    return doDownloadContract(processDay);
  }

  public ReturnT<String> doDownloadContract( LocalDate processDay ) {
    FileDailyProcessDto processDto = new FileDailyProcessDto();
    processDto.setBankChannel(BankChannel.HXBK);
    processDto.setProcessDate(processDay);
    processDto.setType(FileType.LOAN_CONTRACT);

    RestResult<Void> restResult = finLoanFileService.dailyProcess(processDto);
    logger.info("湖消借款合同下载 {}", restResult);
    return ReturnT.SUCCESS;
  }

  @Autowired
  public void setFinLoanFileService(FinFileService finLoanFileService) {
    this.finLoanFileService = finLoanFileService;
  }
}

package com.jinghang.capital.batch.job.hxbk.recc;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.remote.FinReccService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 湖消对账文件比对job
 * @作者 Mr.sandman
 * @时间 2025/07/10 17:41
 */
@Component
@JobHandler("hxbkReccJob")
public class HXBKReccJob extends IJobHandler {

  private static final Logger logger = LoggerFactory.getLogger(HXBKReccJob.class);

  @Autowired
  private FinReccService reccService;

  @Override
  public ReturnT<String> execute( String param ) throws Exception {
    ReccApplyDto reccApplyDto = new ReccApplyDto();
    reccApplyDto.setChannel(BankChannel.HXBK);
    logger.info("HXBK对账开始");
    try {
      HXBKReccParam reccParam = JsonUtil.convertToObject(param, HXBKReccParam.class);
      if (reccParam.getReccType() == null) {
        logger.error("HXBK 对账失败, type is null");
        return ReturnT.FAIL;
      }
      reccApplyDto.setReccType(reccParam.getReccType());
      LocalDate reccDay = reccParam.getReccDay();
      if (null == reccDay) {
        reccDay = LocalDate.now().minusDays(1L);
      }
      reccApplyDto.setReccDay(reccDay);
    } catch (Exception ex) {
      logger.error("HXBK 对账 异常: ", ex);
      return ReturnT.FAIL;
    }
    return doProcessRecc(reccApplyDto);
  }

  public ReturnT<String> doProcessRecc( ReccApplyDto applyDto ) {
    RestResult<ReccResultDto> result = reccService.process(applyDto);
    logger.info("HXBK对账结果. status: {}, content: {}", result.getCode(), result.getData());
    return ReturnT.SUCCESS;
  }

}

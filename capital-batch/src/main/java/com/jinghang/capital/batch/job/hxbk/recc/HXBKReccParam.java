package com.jinghang.capital.batch.job.hxbk.recc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.api.dto.recc.ReccType;

import java.time.LocalDate;

/**
 * 对账文件job参数
 */
public class HXBKReccParam {

    /**
     *  对账文件日期
     *  D-1 日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reccDay;

    private ReccType reccType;

    public LocalDate getReccDay() {
        return reccDay;
    }

    public void setReccDay(LocalDate reccDay) {
        this.reccDay = reccDay;
    }

    public ReccType getReccType() {
        return reccType;
    }

    public void setReccType(ReccType reccType) {
        this.reccType = reccType;
    }
}

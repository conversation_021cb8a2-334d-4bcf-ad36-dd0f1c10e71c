package com.jinghang.capital.batch.job.hxbk.repayreturn;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RepayStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.RepayMode;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadDto;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.batch.job.AbstractBaseJob;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.remote.FinRepayService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 线下还款回盘文件上传资方（蚂蚁）sftp服务器 的任务
 *
 * 触发：每天生成前一天 repay_mode = OFFLINE + repay_status = SUCCEED + channel = HXBK
 *
 * <AUTHOR>
 * @date 2025-07-14 13:40
 */
@Component
@JobHandler("hxbkOfflineRepayReturnFileUploadJob")
public class HXBKOfflineRepayReturnFileUploadJob extends AbstractBaseJob {

    @Autowired
    private FinRepayService finRepayService;

    @Override
    public ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception {
        // 获取前一天 repay_mode = OFFLINE + repay_status = SUCCEED + channel = HXBK 的还款记录，做回盘文件
        // 上传sftp
        // 比对差异，并写入 还款差异表，并实现告警
        // 1. 入参
        // 2. 调用core 开始推送 sftp
        RepayReturnUploadDto dto = new RepayReturnUploadDto();
        LocalDate now = LocalDate.now();
        dto.setActualRepayDate(now.minusDays(1));
        if (StringUtils.isNotEmpty(jobEntity.getFileDate())){
            dto.setActualRepayDate(LocalDate.parse(jobEntity.getFileDate()));
        }
        dto.setRepayMode(RepayMode.OFFLINE);
        dto.setRepayStatus(ProcessStatus.SUCCESS);
        dto.setBankChannel(BankChannel.HXBK);

        logger.info("[线下还款回盘文件推送]定时任务,start... param:{}", JsonUtil.toJsonString(dto));
        RestResult<RepayReturnUploadResultDto> response = finRepayService.repayOfflineReturnFileUpload(dto);

        logger.info("[线下还款回盘文件推送]定时任务,end... result:{}",JsonUtil.toJsonString(response));
        return ReturnT.SUCCESS;
    }
}


package com.jinghang.capital.batch.job.state.Dto;


import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.repay.RepayMode;
import com.jinghang.capital.api.dto.repay.RepayPurpose;
import com.jinghang.capital.api.dto.repay.RepayType;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class RepaidSyncDto {

    private String id;
    /**
     * 还款记录
     */
    private String repaidRecordId;
    /**
     * 借据
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 目的(当期，结清)
     */
    private RepayPurpose repayPurpose;
    /**
     * 模式（线上，线下）
     */
    private RepayMode repayMode;
    /**
     * 类型（还款，代偿...）
     */
    private RepayType repayType;
    /**
     * 状态
     */
    private ProcessStatus state;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRepaidRecordId() {
        return repaidRecordId;
    }

    public void setRepaidRecordId(String repaidRecordId) {
        this.repaidRecordId = repaidRecordId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public ProcessStatus getState() {
        return state;
    }

    public void setState(ProcessStatus state) {
        this.state = state;
    }
}

package com.jinghang.capital.batch.job.state;


import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.capital.batch.entity.BankLoanReplan;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.service.WarningService;
import com.jinghang.capital.batch.service.tc.IBankLoanReplanService;
import com.jinghang.capital.batch.service.tc.ICustomerRepayRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资金系统 对客还款与对资还款对账预警
 */
@Component
@JobHandler("warningLocalRepayReccJob")
public class WarningLocalRepayReccJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningLocalRepayReccJob.class);

    @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private ICustomerRepayRecordService customerRepayRecordService;

    @Autowired
    private IBankLoanReplanService bankLoanReplanService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("资金系统-对客还款与对资还款对账预警开始");
        LocalDate start = LocalDate.now().minusDays(1);
        // 如果需要指定时间
        if (StringUtil.isNotBlank(param)) {
            start = LocalDate.parse(param);
        }
        LocalDate end = start.plusDays(1);
        logger.info("资金系统-对客还款与对资还款对账日期为:{}", start.format(DateUtil.WEB_FORMATTER));
        StringBuilder sb = new StringBuilder();

        // 查询对客成功，但是对资无成功数据（返回值里包含代偿后还款）
        List<CustomerRepayRecord> customerRepayRecords = customerRepayRecordService.queryCustomerSuccessBankNotSuccess(start, end);

        List<CustomerRepayRecord> errorRecords = new ArrayList<>();
        if (!customerRepayRecords.isEmpty()) {
            for (CustomerRepayRecord customerRepayRecord : customerRepayRecords) {
                BankLoanReplan bankLoanReplan = bankLoanReplanService.findByLoanAndPeriod(
                        customerRepayRecord.getLoanId(), customerRepayRecord.getPeriod());

                // 不存在对应的对资成功记录
                if (bankLoanReplan == null) {
                    errorRecords.add(customerRepayRecord);
                }
            }
        }

        if (errorRecords.isEmpty()) {
            warningService.warn("【无需处理】 对账日：" + start.format(DateUtil.WEB_FORMATTER)
                    + "\n <内部对账> 资金系统-对客还款与对资还款对账成功");
        } else {
            // 将所有 failRepayRecords 的 id 收集到一个字符串中
            String ids = errorRecords.stream()
                    .map(CustomerRepayRecord::getId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(", "));

            logger.info("资金系统-对客还款与对资还款对账失败 customerRepayRecordIds: {}", ids);
            warningService.warn(message(start, errorRecords.size()));
        }

        logger.info("资金系统-对客还款与对资还款对账预警结束");
        return ReturnT.SUCCESS;
    }

    public String message(LocalDate start, Integer number) {
        return "对账日："
                + start.format(DateUtil.WEB_FORMATTER)
                + "\n"
                + "<内部对账> 资金系统-对客还款与对资还款对账异常的条数:" + number;
    }

}

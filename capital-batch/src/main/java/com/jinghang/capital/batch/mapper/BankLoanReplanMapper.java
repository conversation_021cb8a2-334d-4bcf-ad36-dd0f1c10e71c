package com.jinghang.capital.batch.mapper;

import com.jinghang.capital.batch.entity.BankLoanReplan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 对资实还款计划表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface BankLoanReplanMapper extends BaseMapper<BankLoanReplan> {

    List<BankLoanReplan> findSuccessList(@Param("channel") String channel, @Param("guaranteeCompany") String guaranteeCompany,
                                          @Param("repayType") String repayType, @Param("beginTime") LocalDateTime beginTime,
                                          @Param("endTime") LocalDateTime endTime);

}

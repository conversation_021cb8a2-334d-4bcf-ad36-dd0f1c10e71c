package com.jinghang.capital.batch.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.job.state.Dto.RepaidSyncDto;
import com.jinghang.capital.batch.job.state.Dto.RepayRecordDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户还款记录表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface CustomerRepayRecordMapper extends BaseMapper<CustomerRepayRecord> {

    List<CustomerRepayRecord> findSuccessList(@Param("productNo") String productNo, @Param("beginTime") LocalDateTime beginTime,
                                              @Param("endTime") LocalDateTime endTime);

    //查询360还款记录
    List<RepayRecordDto> findRepayRecordList(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    //查询360同步记录
    List<RepaidSyncDto> findRepaidSyncList(@Param("rrId") List<String> rrId);

    //查询对客记录
    List<CustomerRepayRecord> findCustomerRecordList(@Param("rsId") List<String> rsId);

    //对资记录
    List<BankRepayRecord> findBankRepayRecord(@Param("crrId") List<String> crrId);

    //对资代偿结清
    List<BankRepayRecord> findClaimBankRepayRecordList(@Param("loanId") List<String> loanId);

    /**
     * 查询对客成功，但是对资无成功数据（返回值里包含代偿后还款）
     * @param startTime
     * @param endTime
     * @return
     */
    List<CustomerRepayRecord> queryCustomerSuccessBankNotSuccess(LocalDate startTime, LocalDate endTime);


}

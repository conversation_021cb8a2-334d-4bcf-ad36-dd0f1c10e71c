package com.jinghang.capital.batch.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.entity.LoanFile;
import com.jinghang.capital.batch.mapper.LoanFileMapper;
import com.jinghang.capital.batch.mapper.LoanMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 资方协议文件校验处理
 */
@Service
public class LoanFileCheckService {

    private static final Logger logger = LoggerFactory.getLogger(LoanFileCheckService.class);

    @Autowired
    private LoanMapper loanMapper;
    @Autowired
    private LoanFileMapper loanFileMapper;
    @Autowired
    private FileService fileService;

    @Resource(name = "warningStateService")
    private WarningService warningService;

    public void checkLoanFile(LocalDate start, LocalDate end, BankChannel bankChannel, List<String> fileTypes) {

        //查询借据
        QueryWrapper<Loan> loanQw = new QueryWrapper<>();
        loanQw.lambda().eq(Loan::getChannel, bankChannel).between(Loan::getLoanTime, start, end).eq(Loan::getLoanStatus, "SUCCESS");
        List<Loan> loans = loanMapper.selectList(loanQw);
        logger.info("协议文件检验预警, 查询放款成功数据条数: {}", loans.size());

        List<String> strings = new ArrayList<>();

        //查询资方下协议并校验
        loans.forEach(loan -> {
            QueryWrapper<LoanFile> qw = new QueryWrapper<>();
            qw.lambda().in(LoanFile::getFileType, fileTypes).eq(LoanFile::getChannel, loan.getChannel())
                .eq(LoanFile::getCreditId, loan.getCreditId());
            List<LoanFile> loanFiles = loanFileMapper.selectList(qw);

            logger.info("协议文件检验预警, 借据loanId: {}, 协议数据条数: {}", loan.getId(), loanFiles.size());

            //去重校验
            List<String> list = loanFiles.stream().map(LoanFile::getFileType).toList();
            if (list.size() != fileTypes.size()) {
                //表示协议文件可能有缺失
                logger.info("协议文件检验预警, 借据loanId: {},  协议数据条数: {}, 协议存在缺失", loanFiles.size(), loan.getId());
                strings.add(loan.getId());
            } else {
                //校验是否存在文件
                if (CollectionUtils.isEmpty(loanFiles)) {
                    logger.info("协议文件检验预警, 借据loanId: {}, 无对应协议文件", loan.getId());
                    strings.add(loan.getId());
                } else {
                    //已存在协议校验状态大小
                    loanFiles.forEach(loanFile -> {
                        boolean needDownload = needDownloadCheck(loanFile, loan.getId());
                        if (needDownload) {
                            //需重新下载协议
                            strings.add(loan.getId());
                        }
                    });
                }
            }
        });

        StringBuilder sb = new StringBuilder();
        if (!strings.isEmpty()) {
            logger.info("协议文件检验预警, 资方: {}, 需重新下载协议文件loanId: {}", bankChannel.getName(), JsonUtil.toJsonString(strings));
            sb.append(message(start, end, "loan", strings.size(), bankChannel)).append("\n").append("\n");
            warningService.warn(sb.toString());
        }

    }

    /**
     * 检测是否需要重新下载文件
     *
     * @param loanFile
     * @param loanId
     * @return
     */
    private boolean needDownloadCheck(LoanFile loanFile, String loanId) {
        if (!"SUCCESS".equals(loanFile.getSignStatus())) {
            logger.info("协议文件检验预警, 借据loanId: {}, 协议:{}, 不是成功状态", loanId, loanFile.getFileName());
            return true;
        }
        long ossFileSize = fileService.getOssFileSize(loanFile.getOssBucket(), loanFile.getOssKey());
        if (ossFileSize == 0) {
            logger.info("协议文件检验预警, 借据loanId: {}, 协议:{}, 协议大小为0", loanId, loanFile.getFileName());
        }
        return ossFileSize == 0;
    }

    public String message(LocalDate start, LocalDate end, String table, Integer number, BankChannel bankChannel) {
        StringBuilder string = new StringBuilder();
        string.append(start).append(" ~ ").append(end)
            .append("\n")
            .append("fin-core.").append(table).append(" - ").append(bankChannel).append(":协议文件存在异常借据条数:").append(number);
        return string.toString();
    }

}

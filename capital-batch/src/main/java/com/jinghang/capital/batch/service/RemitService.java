package com.jinghang.capital.batch.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.repay.ClaimMarkApplyDto;
import com.jinghang.capital.api.dto.repay.RepayMode;
import com.jinghang.capital.api.dto.repay.RepayType;
import com.jinghang.capital.batch.entity.BankRemitRecord;
import com.jinghang.capital.batch.entity.BankRemitRecordRelation;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.enums.RemitStatusEnum;
import com.jinghang.capital.batch.mapper.BankRemitRecordMapper;
import com.jinghang.capital.batch.mapper.BankRemitRecordRelationMapper;
import com.jinghang.capital.batch.mapper.BankRepayRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 打款服务
 */
@Service
public class RemitService {

    @Autowired
    private BankRemitRecordRelationMapper bankRemitRecordRelationMapper;

    @Autowired
    private BankRemitRecordMapper bankRemitRecordMapper;

    @Autowired
    private BankRepayRecordMapper bankRepayRecordMapper;

    /**
     * 生成打款数据
     *
     * @param applyDto 请求对象
     */
    public void genRemit(ClaimMarkApplyDto applyDto) {
        BankChannel channel = applyDto.getChannel();
        LocalDate repayDay = applyDto.getRepayDay();

        genOfflineRemit(channel, repayDay);

        genClaimRemit(channel, repayDay.plusDays(1L));

    }


    /**
     * 生成线下还款打款数据.
     *
     * @param channel  渠道
     * @param repayDay 还款日
     */
    private void genOfflineRemit(BankChannel channel, LocalDate repayDay) {

        LambdaQueryWrapper<BankRepayRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankRepayRecord::getChannel, channel.name())
            .eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name())
            .eq(BankRepayRecord::getRepayMode, RepayMode.OFFLINE.name())
            .eq(BankRepayRecord::getRepayType, RepayType.REPAY.name())
            .ge(BankRepayRecord::getRepayTime, repayDay.atStartOfDay())
            .lt(BankRepayRecord::getRepayTime, repayDay.plusDays(1L).atStartOfDay());
        List<BankRepayRecord> repays = bankRepayRecordMapper.selectList(queryWrapper);
        saveRemitData(channel, repays, repayDay.plusDays(1L), RepayType.REPAY, RemitStatusEnum.WAIT, null, null);
    }


    /**
     * 生成理赔打款数据.
     *
     * @param channel  渠道
     * @param repayDay 还款日（理赔是当天）
     */
    private void genClaimRemit(BankChannel channel, LocalDate repayDay) {
        LambdaQueryWrapper<BankRepayRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankRepayRecord::getChannel, channel.name())
            .eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name())
            .eq(BankRepayRecord::getRepayMode, RepayMode.OFFLINE.name())
            .eq(BankRepayRecord::getRepayType, RepayType.CLAIM.name())
            .ge(BankRepayRecord::getRepayTime, repayDay.atStartOfDay())
            .lt(BankRepayRecord::getRepayTime, repayDay.plusDays(1L).atStartOfDay());
        List<BankRepayRecord> repays = bankRepayRecordMapper.selectList(queryWrapper);
        saveRemitData(channel, repays, repayDay, RepayType.CLAIM, RemitStatusEnum.NO_NEED, null, null);
    }

    private void saveRemitData(BankChannel channel, List<BankRepayRecord> repays, LocalDate remitDay, RepayType repayType,
                               RemitStatusEnum remitStatus, String remitTarget, String remitAccount) {
        BigDecimal totalAmt = repays.stream().map(BankRepayRecord::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal principalAmt = repays.stream().map(BankRepayRecord::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal intAmt = repays.stream().map(BankRepayRecord::getInterestAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal breachAmt = repays.stream().map(BankRepayRecord::getBreachAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal penaltyAmt = repays.stream().map(BankRepayRecord::getPenaltyAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal guaranteeAmt = repays.stream().map(BankRepayRecord::getGuaranteeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

        BankRemitRecord remitRecord = new BankRemitRecord();
        remitRecord.setChannel(channel.name());
        remitRecord.setRemitStatus(remitStatus.name());
        remitRecord.setRemitTime(remitDay.atStartOfDay());
        remitRecord.setRemitTarget(remitTarget);
        remitRecord.setRemitAccount(remitAccount);
        remitRecord.setRemitAmt(totalAmt);
        remitRecord.setRemitNum(repays.size());
        remitRecord.setPrincipalAmt(principalAmt);
        remitRecord.setInterestAmt(intAmt);
        remitRecord.setPenaltyAmt(penaltyAmt);
        remitRecord.setBreachAmt(breachAmt);
        remitRecord.setGuaranteeAmt(guaranteeAmt);

        remitRecord.setRepayType(repayType.name());
        remitRecord.setRepayMode(RepayMode.OFFLINE.name());

        bankRemitRecordMapper.insert(remitRecord);

        String remitId = remitRecord.getId();
        LocalDateTime now = LocalDateTime.now();
        repays.forEach(r -> {
            BankRemitRecordRelation relation = new BankRemitRecordRelation();

            relation.setRemitId(remitId);
            relation.setRecordId(r.getId());
            relation.setCreatedBy("sys");
            relation.setCreatedTime(now);
            relation.setUpdatedBy("sys");
            relation.setUpdatedTime(now);

            bankRemitRecordRelationMapper.insert(relation);
        });


    }


}

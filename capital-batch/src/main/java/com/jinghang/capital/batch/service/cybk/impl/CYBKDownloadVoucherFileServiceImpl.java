package com.jinghang.capital.batch.service.cybk.impl;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.remote.FinVoucherFileService;
import com.jinghang.capital.batch.service.cybk.CYBKDownloadVoucherFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长银消金直连 证明、凭证文件下载
 */
@Component
public class CYBKDownloadVoucherFileServiceImpl implements CYBKDownloadVoucherFileService {

    private static final Logger logger = LoggerFactory.getLogger(CYBKDownloadVoucherFileServiceImpl.class);

    @Autowired
    private FinVoucherFileService voucherFileService;

    @Override
    public void processCYBKDownloadVoucher() {
        FileDailyProcessDto dto = new FileDailyProcessDto();
        dto.setBankChannel(BankChannel.CYBK);
        dto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
        voucherFileService.fileApplyQuery(dto);
    }


}

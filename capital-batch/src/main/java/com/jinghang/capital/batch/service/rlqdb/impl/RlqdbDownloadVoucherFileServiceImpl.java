package com.jinghang.capital.batch.service.rlqdb.impl;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.remote.FinVoucherFileService;
import com.jinghang.capital.batch.service.rlqdb.RlqdbDownloadVoucherFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 润楼青岛结清凭证文件下载
 */
@Component
public class RlqdbDownloadVoucherFileServiceImpl implements RlqdbDownloadVoucherFileService {

    private static final Logger logger = LoggerFactory.getLogger(RlqdbDownloadVoucherFileServiceImpl.class);

    @Autowired
    private FinVoucherFileService voucherFileService;

    @Override
    public void processDownloadVoucher() {
        FileDailyProcessDto dto = new FileDailyProcessDto();
        dto.setBankChannel(BankChannel.RL_SUS);
        dto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
        voucherFileService.fileApplyQuery(dto);
    }


}

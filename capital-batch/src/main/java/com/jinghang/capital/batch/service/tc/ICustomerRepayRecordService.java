package com.jinghang.capital.batch.service.tc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 用户还款记录表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface ICustomerRepayRecordService extends IService<CustomerRepayRecord> {

    List<CustomerRepayRecord> findSuccessList(Product productNo, LocalDate date);

    List<CustomerRepayRecord> queryByLoanId(String loanId);

    /**
     * 查询对客成功，但是对资无成功数据（返回值里包含代偿后还款）
     * @param startTime
     * @param endTime
     * @return
     */
    List<CustomerRepayRecord> queryCustomerSuccessBankNotSuccess(LocalDate startTime, LocalDate endTime);

}

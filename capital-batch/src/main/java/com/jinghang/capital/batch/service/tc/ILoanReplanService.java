package com.jinghang.capital.batch.service.tc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.capital.batch.entity.LoanReplan;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 还款计划表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface ILoanReplanService extends IService<LoanReplan> {

    /**
     * 查询逾期未还的还款计划数据
     *
     * @param date
     * @param channels
     * @return
     */
    List<LoanReplan> queryOverdueLoanPlans(LocalDate date, List<String> channels);

    List<LoanReplan> getByLoanId(List<String> loanId);

    List<LoanReplan> getByLoanId(String loanId);
}

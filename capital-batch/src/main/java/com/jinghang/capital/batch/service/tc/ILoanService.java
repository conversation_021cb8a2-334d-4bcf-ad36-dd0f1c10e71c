package com.jinghang.capital.batch.service.tc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.entity.Loan;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 借款表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface ILoanService extends IService<Loan> {

    Loan findByLoanNoAndChannel(String loanNo, String channel);

    Loan findByIdAndChannel(String id, String channel);

    List<Loan> findSuccessList(Product productNo, LocalDate date);

    List<String> queryBankChannelsByProduct(Product product);

    List<Loan> findByStatusAndChannel(String status, String channel, LocalDate date);

    Loan findByLoanNo(String loanNo);

    Long countStatusAndBankChannelLoanTime(String name, String name1, LocalDate fileDate);

    /**
     * 中科金辽农 查询总账数据
     */
    Map<String, BigDecimal> queryZkjLnFU300(String channel, LocalDate startDate, LocalDate endDate);

    /**
     * 对资--查询至截止日未结清的借据（包含截止日当天结清的借据）
     * <p>
     * 即：2025-03-11日为截止日时，2025-03-11当日结清的借据依然存在结果集中，2025-03-10日结清的借据不存在结果集中
     *
     * @param channel
     * @param endDate
     * @return
     */
    List<Loan> findBankNotSettleLoan(String channel, LocalDate endDate);

    /**
     * 对资--查询至截止日未结清的借据（包含截止日当天结清的借据）
     * <p>
     * 即：2025-03-11日为截止日时，2025-03-11当日结清的借据依然存在结果集中，2025-03-10日结清的借据不存在结果集中
     *
     * @param channel
     * @param endDate
     * @return
     */
    List<Loan> findCustomerNotSettleLoan(String channel, LocalDate endDate);


    List<Loan> queryLoansByChannelAndLoanTime(String channel, LocalDate startDate, LocalDate endDate);

}

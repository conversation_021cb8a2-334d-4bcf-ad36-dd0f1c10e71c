package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.capital.batch.entity.AccountBankCard;
import com.jinghang.capital.batch.mapper.AccountBankCardMapper;
import com.jinghang.capital.batch.service.tc.IAccountBankCardService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户银行卡表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class AccountBankCardServiceImpl extends ServiceImpl<AccountBankCardMapper, AccountBankCard> implements IAccountBankCardService {

}

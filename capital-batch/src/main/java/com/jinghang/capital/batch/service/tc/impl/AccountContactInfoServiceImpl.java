package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.capital.batch.entity.AccountContactInfo;
import com.jinghang.capital.batch.mapper.AccountContactInfoMapper;
import com.jinghang.capital.batch.service.tc.IAccountContactInfoService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户联系人表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class AccountContactInfoServiceImpl extends ServiceImpl<AccountContactInfoMapper, AccountContactInfo> implements IAccountContactInfoService {

}

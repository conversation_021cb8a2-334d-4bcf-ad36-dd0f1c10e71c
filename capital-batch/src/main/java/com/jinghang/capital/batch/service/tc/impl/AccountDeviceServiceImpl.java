package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.capital.batch.entity.AccountDevice;
import com.jinghang.capital.batch.mapper.AccountDeviceMapper;
import com.jinghang.capital.batch.service.tc.IAccountDeviceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户设备信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class AccountDeviceServiceImpl extends ServiceImpl<AccountDeviceMapper, AccountDevice> implements IAccountDeviceService {

}

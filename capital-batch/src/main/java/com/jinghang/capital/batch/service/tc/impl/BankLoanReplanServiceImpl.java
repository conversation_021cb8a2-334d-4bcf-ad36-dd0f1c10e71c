package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jinghang.capital.api.dto.repay.RepayPurpose;
import com.jinghang.capital.api.dto.repay.RepayType;
import com.jinghang.capital.batch.entity.BankLoanReplan;
import com.jinghang.capital.batch.mapper.BankLoanReplanMapper;
import com.jinghang.capital.batch.service.tc.IBankLoanReplanService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 对资实还款计划表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class BankLoanReplanServiceImpl extends ServiceImpl<BankLoanReplanMapper, BankLoanReplan> implements IBankLoanReplanService {

    @Override
    public List<BankLoanReplan> queryClaimBankLoanPlans(LocalDate date, List<String> channels) {
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.plusDays(1L).atStartOfDay();
        return baseMapper.selectList(new LambdaQueryWrapper<BankLoanReplan>()
            .in(BankLoanReplan::getChannel, channels)
            .eq(BankLoanReplan::getRepayType, RepayType.CLAIM.name())
            .ge(BankLoanReplan::getActRepayTime, startTime)
            .lt(BankLoanReplan::getActRepayTime, endTime));
    }

    @Override
    public BankLoanReplan findByLoanAndPeriod(String loanId, Integer period) {
        return baseMapper.selectOne(new LambdaQueryWrapper<BankLoanReplan>()
            .eq(BankLoanReplan::getLoanId, loanId)
            .eq(BankLoanReplan::getPeriod, period));
    }

    @Override
    public List<BankLoanReplan> queryByLoanIdAndRepayPurpose(String loanId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BankLoanReplan>()
            .eq(BankLoanReplan::getLoanId, loanId)
            .eq(BankLoanReplan::getRepayPurpose, RepayPurpose.CLEAR.name()));
    }

}

package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.repay.RepayPurpose;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.mapper.BankRepayRecordMapper;
import com.jinghang.capital.batch.service.tc.IBankRepayRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 还款记录表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class BankRepayRecordServiceImpl extends ServiceImpl<BankRepayRecordMapper, BankRepayRecord> implements IBankRepayRecordService {

    @Override
    public BankRepayRecord getByLoanIdAndPeriodAndStatus(String loanId, Integer period) {
        LambdaQueryWrapper<BankRepayRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankRepayRecord::getLoanId, loanId);
        queryWrapper.eq(BankRepayRecord::getPeriod, period);
        queryWrapper.eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name());
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<BankRepayRecord> queryByLoanId(String loanId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BankRepayRecord>()
            .in(BankRepayRecord::getLoanId, loanId)
            .eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name()));
    }

    @Override
    public BankRepayRecord queryByLoanIdAndRepayPurpose(String loanId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<BankRepayRecord>()
            .in(BankRepayRecord::getLoanId, loanId)
            .eq(BankRepayRecord::getRepayPurpose, RepayPurpose.CLEAR.name())
            .eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name()));
    }
}

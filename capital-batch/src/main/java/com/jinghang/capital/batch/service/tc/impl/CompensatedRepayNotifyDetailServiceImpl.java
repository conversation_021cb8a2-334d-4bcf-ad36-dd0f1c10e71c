package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.capital.batch.entity.CompensatedRepayNotifyDetail;
import com.jinghang.capital.batch.mapper.CompensatedRepayNotifyDetailMapper;
import com.jinghang.capital.batch.service.tc.ICompensatedRepayNotifyDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代偿后还款通知明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class CompensatedRepayNotifyDetailServiceImpl
        extends ServiceImpl<CompensatedRepayNotifyDetailMapper, CompensatedRepayNotifyDetail>
        implements ICompensatedRepayNotifyDetailService {

}

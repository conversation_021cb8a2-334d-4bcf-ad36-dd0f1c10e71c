package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.capital.batch.entity.CompensatedRepayNotify;
import com.jinghang.capital.batch.mapper.CompensatedRepayNotifyMapper;
import com.jinghang.capital.batch.service.tc.ICompensatedRepayNotifyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代偿后还款通知 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class CompensatedRepayNotifyServiceImpl extends ServiceImpl<CompensatedRepayNotifyMapper, CompensatedRepayNotify>
        implements ICompensatedRepayNotifyService {

}

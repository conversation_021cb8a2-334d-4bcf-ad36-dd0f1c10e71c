package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.mapper.CustomerRepayRecordMapper;
import com.jinghang.capital.batch.service.tc.ICustomerRepayRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 用户还款记录表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class CustomerRepayRecordServiceImpl extends ServiceImpl<CustomerRepayRecordMapper, CustomerRepayRecord> implements ICustomerRepayRecordService {

    @Autowired
    private CustomerRepayRecordMapper customerRepayRecordMapper;

    @Override
    public List<CustomerRepayRecord> findSuccessList(Product productNo, LocalDate date) {
        return baseMapper.findSuccessList(productNo.name(), LocalDateTime.of(date, LocalTime.MIN), LocalDateTime.of(date, LocalTime.MAX));
    }

    @Override
    public List<CustomerRepayRecord> queryByLoanId(String loanId) {
        return baseMapper.selectList(new LambdaQueryWrapper<CustomerRepayRecord>()
            .in(CustomerRepayRecord::getLoanId, loanId)
            .eq(CustomerRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name()));
    }

    @Override
    public List<CustomerRepayRecord> queryCustomerSuccessBankNotSuccess(LocalDate startTime, LocalDate endTime) {
        return customerRepayRecordMapper.queryCustomerSuccessBankNotSuccess(startTime, endTime);
    }

}

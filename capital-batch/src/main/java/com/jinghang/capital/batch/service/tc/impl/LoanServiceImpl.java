package com.jinghang.capital.batch.service.tc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.mapper.LoanMapper;
import com.jinghang.capital.batch.service.tc.ILoanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 借款表; 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
public class LoanServiceImpl extends ServiceImpl<LoanMapper, Loan> implements ILoanService {

    @Autowired
    private LoanMapper loanMapper;

    @Override
    public Loan findByLoanNoAndChannel(String loanNo, String channel) {
        LambdaQueryWrapper<Loan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Loan::getLoanNo, loanNo).eq(Loan::getChannel, channel);
        List<Loan> loans = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(loans)) {
            return null;
        }
        return loans.get(0);
    }

    @Override
    public Loan findByIdAndChannel(String id, String channel) {
        LambdaQueryWrapper<Loan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Loan::getId, id).eq(Loan::getChannel, channel);
        List<Loan> loans = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(loans)) {
            return null;
        }
        return loans.get(0);
    }

    @Override
    public List<Loan> findSuccessList(Product productNo, LocalDate date) {
        return baseMapper.selectList(new LambdaQueryWrapper<Loan>()
            .eq(Loan::getProductNo, productNo.name())
            .eq(Loan::getLoanStatus, ProcessStatus.SUCCESS)
            .between(Loan::getLoanTime, LocalDateTime.of(date, LocalTime.MIN), LocalDateTime.of(date, LocalTime.MAX)));
    }

    @Override
    public List<Loan> findByStatusAndChannel(String status, String channel, LocalDate date) {
        return baseMapper.selectList(new LambdaQueryWrapper<Loan>()
            .eq(Loan::getLoanStatus, ProcessStatus.SUCCESS)
            .eq(Loan::getChannel, channel)
            .ge(Loan::getLoanTime, date)
            .lt(Loan::getLoanTime, date.plusDays(1)));
    }

    @Override
    public List<String> queryBankChannelsByProduct(Product product) {
        return baseMapper.selectObjs(new LambdaQueryWrapper<Loan>()
                .select(Loan::getChannel)
                .eq(Loan::getProductNo, product.name())
                .groupBy(Loan::getChannel))
            .stream().map(channel -> (String) channel).toList();
    }


    @Override
    public Loan findByLoanNo(String loanNo) {
        LambdaQueryWrapper<Loan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Loan::getLoanNo, loanNo);
        List<Loan> loans = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(loans)) {
            return null;
        }
        return loans.get(0);
    }

    @Override
    public Long countStatusAndBankChannelLoanTime(String status, String bankChannel, LocalDate fileDate) {

        return baseMapper.selectCount(new LambdaQueryWrapper<Loan>()
            .eq(Loan::getLoanStatus, status)
            .eq(Loan::getChannel, bankChannel)
            .between(Loan::getLoanTime, fileDate.atStartOfDay(), fileDate.plusDays(1).atStartOfDay()));
    }

    @Override
    public Map<String, BigDecimal> queryZkjLnFU300(String channel, LocalDate startDate, LocalDate endDate) {
        return loanMapper.queryZkjLnFU300(channel, startDate, endDate);
    }

    @Override
    public List<Loan> findBankNotSettleLoan(String channel, LocalDate endDate) {
        return loanMapper.findBankNotSettleLoan(channel, endDate);
    }

    @Override
    public List<Loan> findCustomerNotSettleLoan(String channel, LocalDate endDate) {
        return loanMapper.findCustomerNotSettleLoan(channel, endDate);
    }

    @Override
    public List<Loan> queryLoansByChannelAndLoanTime(String channel, LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectList(new LambdaQueryWrapper<Loan>()
            .eq(Loan::getLoanStatus, "SUCCESS")
            .eq(Loan::getChannel, channel)
            .ge(Loan::getLoanTime, startDate)
            .lt(Loan::getLoanTime, endDate));
    }
}

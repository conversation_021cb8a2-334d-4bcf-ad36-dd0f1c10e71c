package com.jinghang.capital.batch.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import cn.hutool.core.codec.Base64;

/**
 * Aes 加解密⼯具类
 */
public class AESUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(AESUtil.class);
    private static final String AES_ALGORITHM = "AES";
    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

    private static final int KEY_SIZE   = 128;
    private static final int CACHE_SIZE = 1024;

    public static void main(String[] args) throws Exception {
        String seed = "abc";
        String sourceFilePath = "/Users/<USER>/test.txt";
        String tmpFilePath = "/Users/<USER>/tmps.txt";
        String decryptFilePath = "/Users/<USER>/decrypt.txt";
        encryptFile(seed, sourceFilePath, tmpFilePath);
        decryptFile(seed, tmpFilePath, decryptFilePath);
    }

    /**
     * AES 加密操作
     *
     * @param content 待加密内容
     * @param seed    加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content, String seed) {
        try {
            if (content == null) {return null;}
            // 创建密码器
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            // 初始化为加密模式的密码器
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(seed));
            // 加密
            byte[] result = cipher.doFinal(byteContent);
            // 通过Base64转码返回
            return Base64.encode(result);
        } catch (Exception e) {
            LOGGER.error("AESUtil.encrypt-error:", e);
        }
        return null;
    }

    /**
     * AES 解密操作
     *
     * @param content 待解密内容
     * @param seed    解密密码
     * @return 返回Base64转码后的解密数据
     */
    public static String decrypt(String content, String seed) {
        try {
            if (content == null) {return null;}
            // 实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            // 使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(seed));
            // 执行操作
            byte[] result = cipher.doFinal(Base64.decode(content));
            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            LOGGER.error("AESUtil.decrypt-error:", e);
        }
        return null;
    }

    /**
     * 获取密钥
     *
     * @param seed 密钥
     * @return 密钥
     * @throws Exception
     */
    private static SecretKeySpec getSecretKey(final String seed) {
        // 返回生成指定算法密钥生成器的 KeyGenerator 对象
        KeyGenerator kg;
        try {
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(seed.getBytes());
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            kg.init(KEY_SIZE, random);
            // 生成一个密钥
            SecretKey secretKey = kg.generateKey();
            // 转换为AES专用密钥
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        } catch ( NoSuchAlgorithmException e) {
            LOGGER.error("AESUtil.getSecretKey-error:", e);
        }
        return null;
    }

    /**
     * <p>
     * 文件加密
     * </p>
     *
     * @param seed
     * @param sourceFilePath
     * @param destFilePath
     * @throws Exception
     */
    public static void encryptFile(String seed, String sourceFilePath, String destFilePath) throws Exception {
        File sourceFile = new File(sourceFilePath);
        File destFile = new File(destFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return;
        }
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }
        destFile.createNewFile();
        try ( InputStream in = new FileInputStream(sourceFile)) {
            try ( OutputStream out = new FileOutputStream(destFile)) {
                SecretKeySpec secretKeySpec = getSecretKey(seed);
                Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
                try (CipherInputStream cin = new CipherInputStream(in, cipher)) {
                    byte[] cache = new byte[CACHE_SIZE];
                    int nRead = 0;
                    while ((nRead = cin.read(cache)) != -1) {
                        out.write(cache, 0, nRead);
                        out.flush();
                    }
                }
            }
        }

    }

    /**
     * <p>
     * 文件解密
     * </p>
     *
     * @param seed
     * @param sourceFilePath
     * @param destFilePath
     * @throws Exception
     */
    public static void decryptFile(String seed, String sourceFilePath, String destFilePath) throws Exception {
        File sourceFile = new File(sourceFilePath);
        File destFile = new File(destFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return;
        }
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }
        destFile.createNewFile();
        try (FileInputStream in = new FileInputStream(sourceFile)) {
            try (FileOutputStream out = new FileOutputStream(destFile)) {
                SecretKeySpec secretKeySpec = getSecretKey(seed);
                Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
                try (CipherOutputStream cipherOutputStream = new CipherOutputStream(out, cipher)) {
                    byte[] cache = new byte[CACHE_SIZE];
                    int nRead = 0;
                    while ((nRead = in.read(cache)) != -1) {
                        cipherOutputStream.write(cache, 0, nRead);
                        cipherOutputStream.flush();
                    }
                }
            }
        }
    }


    // /**
    //  * 加密数据
    //  *
    //  * @param data
    //  * @param aesBase64Key
    //  * @return
    //  * @throws Exception
    //  */
    // public static String encrypt(byte[] data, String aesBase64Key) {
    //     try {
    //         Cipher cipher = initCipher(aesBase64Key, Cipher.ENCRYPT_MODE);
    //         byte[] bytOut = cipher.doFinal(data);
    //         return Base64.getEncoder().encodeToString(bytOut);
    //     } catch (Exception e) {
    //         throw new RuntimeException(e);
    //     }
    // }
    //
    // /**
    //  * 解密数据
    //  *
    //  * @param encryptedData
    //  * @param aesBase64Key
    //  * @return
    //  * @throws Exception
    //  */
    // public static byte[] decrypt(byte[] encryptedData, String aesBase64Key) {
    //     try {
    //         Cipher cipher = initCipher(aesBase64Key, Cipher.DECRYPT_MODE);
    //         return cipher.doFinal(encryptedData);
    //     } catch (Exception e) {
    //         throw new RuntimeException(e);
    //     }
    // }

    /**
     * 初始化密码器
     *
     * @param aesBase64Key
     * @param mode
     * @return
     * @throws Exception
     */
    // private static Cipher initCipher(String aesBase64Key, int mode) throws Exception {
    //     SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(aesBase64Key), AES_ALGORITHM);
    //     Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
    //     cipher.init(mode, secretKeySpec);
    //     return cipher;
    // }

}


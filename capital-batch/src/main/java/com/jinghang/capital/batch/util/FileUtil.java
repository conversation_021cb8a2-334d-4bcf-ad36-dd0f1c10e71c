package com.jinghang.capital.batch.util;


import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.sftp.exception.SftpException;
import com.jinghang.common.sftp.exception.SftpFileNotExistsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;

public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 密钥登录sftp，注册密钥
     *
     * @param resourcePath resource下的文件路径
     * @return
     */
    public static void sftpRegistrationKey(String resourcePath) {
        // 创建临时文件
        File tempFile = createTempFile();

        // 将resource下的文件转存到临时文件
        try (InputStream keyInStream = FileUtil.class.getClassLoader().getResourceAsStream(resourcePath);
             FileOutputStream fileOutStream = new FileOutputStream(tempFile)) {
            if (keyInStream != null) {
                fileOutStream.write(keyInStream.readAllBytes());
            }
        } catch (Exception e) {
            logger.error("resource下的文件转存到临时文件异常", e);
            throw new RuntimeException(e);
        }

        // sftp注册密钥
        try {
            Sftp.addIdentity(tempFile.getAbsolutePath());
        } catch (SftpException e) {
            logger.error("sftp 密钥注册失败");
            throw new RuntimeException("sftp 密钥注册失败", e);
        }

        // 删除临时文件
        deleteFile(tempFile);
    }

    /**
     * 下载sftp文件到本地
     *
     * @param sftp
     * @param sftpPath
     * @return
     */
    public static Path sftpDownload(Sftp sftp, String sftpPath) {
        logger.info("下载sftp文件到本地 sftp文件路径 {}", sftpPath);
        Path tempPath = createTempPath();
        try {
            sftp.download(false, DestMapping.of(sftpPath, tempPath.toAbsolutePath().toString()));
        } catch (SftpFileNotExistsException e) {
            logger.error("文件不存在");
            return null;
        } catch (Exception e) {
            logger.error("sftp文件下载失败", e);
            throw new RuntimeException("sftp文件下载失败", e);
        }
        return tempPath;
    }

    /**
     * 将数据写入到临时文件
     *
     * @param data 需要写入的数据
     */
    public static Path stringToPath(String data) {
        Path tempPath = createTempPath();
        try {
            // 将文件流写入本地临时文件
            Files.writeString(tempPath, data);
            return tempPath;
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 上传文件到sftp
     *
     * @return
     */
    public static void uploadToSftp(Sftp sftp, String sftpPath, Path tempPath) {
        logger.info("上传文件到sftp, filePath: {}", sftpPath);
        try {
            sftp.upload(DestMapping.of(sftpPath, tempPath.toFile().getAbsolutePath()));
        } catch (SftpException e) {
            logger.error("文件上传异常", e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     * 创建临时文件
     *
     * @return
     */
    public static File createTempFile() {
        return createTempFile("temp", "temp");
    }

    /**
     * 创建临时文件
     *
     * @return
     */
    public static File createTempFile(String prefix, String suffix) {
        try {
            return File.createTempFile(prefix, suffix);
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建临时文件
     *
     * @return
     */
    public static Path createTempPath() {
        return createTempPath("temp", "temp");
    }

    /**
     * 创建临时文件
     *
     * @return
     */
    public static Path createTempPath(String prefix, String suffix) {
        try {
            return Files.createTempFile(prefix, suffix);
        } catch (IOException e) {
            logger.error("创建临时文件异常", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * InputStream 转 Path
     *
     * @return
     */
    public static Path toPath(InputStream inputStream) {
        if (inputStream == null) {
            return null;
        }
        try {
            // 将文件流写入本地临时文件
            Path tempPath = createTempPath();
            Files.write(tempPath, inputStream.readAllBytes());
            inputStream.close();
            return tempPath;
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    public static Path createTempFilePath(String prefix, String suffix) {
        Path path;
        try {
            path = Files.createTempFile(prefix, suffix);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return path;
    }


    /**
     * 删除临时文件
     *
     * @param file
     */
    public static void deleteFile(File file) {
        if (file != null && file.exists()) {
            if (!file.delete()) {
                logger.error("删除临时文件失败！");
            }
        }
    }

    /**
     * 删除临时Path
     *
     * @param targetFile
     */
    public static void deletePath(Path targetFile) {
        if (targetFile == null) {
            return;
        }

        try {
            Files.delete(targetFile);
        } catch (IOException e) {
            logger.error("delete file error, path: {}", targetFile, e);
        }
    }


    public static void deleteTempFile(Path dir, Path targetFile) {
        try {
            Files.delete(targetFile);
        } catch (IOException e) {
            logger.error("delete file error, path: {}", targetFile, e);
        }

        try {
            Files.delete(dir);
        } catch (IOException e) {
            logger.error("delete file error, path: {}", dir, e);
        }
    }


}

spring.application.name=fin-batch
spring.config.import=apollo://

logging.file.name=/home/<USER>/logs/fin-batch/fin-batch.log
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}][%thread] %-5level %logger{50}.%method:%line - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-}][%thread] %-5level %logger{50}.%method:%line - %msg%n
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=1500MB

server.port=8100

mybatis.mapper-locations=classpath:/mapper/*Mapper.xml



logging.level.com.qiangyun.fin.batch.mapper= debug

bucketName=test-jh01

spring.jpa.show-sql=true

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.BankLoanReplanMapper">
    <select id="findSuccessList" resultType="com.jinghang.capital.batch.entity.BankLoanReplan">
        SELECT
        r.*
        FROM
        bank_loan_replan r
        LEFT JOIN loan l ON r.loan_id = l.id
        WHERE
        <if test="channel != null">
            l.channel = #{channel}
        </if>
        <if test="guaranteeCompany != null">
            AND l.guarantee_company = #{guaranteeCompany}
        </if>
        <if test="repayType != null and repayType != ''">
            AND r.repay_type = #{repayType}
        </if>
        AND r.act_repay_time BETWEEN #{beginTime} AND #{endTime}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.CustomerRepayRecordMapper">

    <select id="findSuccessList" resultType="com.jinghang.capital.batch.entity.CustomerRepayRecord">
        SELECT
            r.*
        FROM
            customer_repay_record r
                LEFT JOIN loan l ON r.loan_id = l.id
        WHERE
            l.product_no = #{productNo}
          AND r.repay_status = 'SUCCESS'
          AND r.repay_time BETWEEN #{beginTime} AND #{endTime}
    </select>


    <!-- 查询360 线下支付宝还款成功数据-->
    <select id="findRepayRecordList" resultType="com.jinghang.capital.batch.job.state.Dto.RepayRecordDto">
        SELECT
            r.*
        FROM
            360_reapy_record r
        WHERE r.repay_state = 'SUCCEED'
          AND r.repay_mode = 'OFFLINE'
          AND r.repay_channel = 'ALIPAY'
          AND r.repay_apply_date  <![CDATA[>=]]> #{beginTime}
          AND r.repay_apply_date <![CDATA[<=]]> #{endTime}
    </select>

    <!--根据360还款记录查询同步记录数据-->
    <select id="findRepaidSyncList" resultType="com.jinghang.capital.batch.job.state.Dto.RepaidSyncDto">
        SELECT
            rs.*
        FROM
            `fin_service`.`360_repaid_sync` rs LEFT JOIN `fin_service`.`360_reapy_record` rr
        ON rs.`repaid_record_id` = rr.id
        WHERE rs.`loan_id` = rr.`loan_id`
          AND rs.`period` = rr.`period`
          AND rr.`repay_state` = 'SUCCEED'
          AND rr.repay_mode = 'OFFLINE'
          AND rr.`repay_channel` = 'ALIPAY'
          AND rs.state = 'SUCCESS'
          AND rs.`repaid_record_id` IN
            <foreach collection="rrId" item="rrIds" separator="," open="(" close=")">
                #{rrIds}
            </foreach>
    </select>

    <!--根据同步记录查询对客还款数据-->
    <select id="findCustomerRecordList" resultType="com.jinghang.capital.batch.entity.CustomerRepayRecord">
        SELECT
            crr.*
        FROM
            `fin_service`.`customer_repay_record` crr LEFT JOIN `fin_service`.`360_repaid_sync` rr
        ON   crr.outer_repay_id = rr.id
        WHERE
              crr.period = rr.period
          AND crr.repay_status = 'SUCCESS'
          AND crr.repay_mode = 'OFFLINE'
          AND crr.outer_repay_id IN
            <foreach collection="rsId" item="rsIds" separator="," open="(" close=")">
                #{rsIds}
            </foreach>
    </select>

    <!--根据对客查询对资还款数据-->
    <select id="findBankRepayRecord" resultType="com.jinghang.capital.batch.entity.BankRepayRecord">
        SELECT
            brr.*
        FROM
        `fin_service`.`bank_repay_record` brr LEFT JOIN `fin_service`.`customer_repay_record` crr
            ON brr.sys_id = crr.id
        WHERE
              crr.period = brr.period
          AND brr.loan_id = crr.loan_id
          AND brr.repay_status = 'SUCCESS'
          AND brr.sys_id IN
            <foreach collection="crrId" item="crrIds" separator="," open="(" close=")">
                #{crrIds}
            </foreach>
    </select>

    <select id="findClaimBankRepayRecordList" resultType="com.jinghang.capital.batch.entity.BankRepayRecord">
        SELECT
            cbrr.*
        FROM
            bank_repay_record cbrr
        WHERE
            cbrr.repay_type = 'CLAIM'
          AND cbrr.repay_status = 'SUCCESS'
          AND cbrr.loan_id IN
            <foreach collection="loanId" item="loanIds" separator="," open="(" close=")">
                #{loanIds}
            </foreach>
    </select>

    <!--查询对客成功，但是对资无成功数据（返回值里包含代偿后还款）-->
    <select id="queryCustomerSuccessBankNotSuccess" resultType="com.jinghang.capital.batch.entity.CustomerRepayRecord">
        select crr.*
        from customer_repay_record crr
        where repay_status = 'SUCCESS'
        and repay_time &gt;= '2025-04-09'
        and repay_time &lt; '2025-04-10'
        and not exists(select 1
        from bank_repay_record
        where crr.id = sys_id and repay_status = 'SUCCESS');
    </select>

</mapper>

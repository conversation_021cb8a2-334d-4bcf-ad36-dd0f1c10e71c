<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.LoanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jinghang.capital.batch.entity.Loan">
        <id column="id" property="id" />
        <result column="account_id" property="accountId" />
        <result column="credit_id" property="creditId" />
        <result column="outer_loan_id" property="outerLoanId" />
        <result column="channel" property="channel" />
        <result column="product_no" property="productNo" />
        <result column="loan_money" property="loanMoney" />
        <result column="periods" property="periods" />
        <result column="loan_purpose" property="loanPurpose" />
        <result column="loan_status" property="loanStatus" />
        <result column="fail_msg" property="failMsg" />
        <result column="loan_time" property="loanTime" />
        <result column="loan_no" property="loanNo" />
        <result column="third_apply_no" property="thirdApplyNo" />
        <result column="loan_card_id" property="loanCardId" />
        <result column="repay_card_id" property="repayCardId" />
        <result column="cust_name" property="custName" />
        <result column="cust_mobile" property="custMobile" />
        <result column="cust_cert_no" property="custCertNo" />
        <result column="custom_rate" property="customRate" />
        <result column="bank_rate" property="bankRate" />
        <result column="remark" property="remark" />
        <result column="revision" property="revision" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_id, credit_id, outer_loan_id, channel, product_no, loan_money, periods, loan_purpose, loan_status, fail_msg, loan_time, loan_no, third_apply_no, loan_card_id, repay_card_id, cust_name, cust_mobile, cust_cert_no, custom_rate, bank_rate, remark, revision, created_by, created_time, updated_by, updated_time
    </sql>

    <select id="queryZkjLnFU300" resultType="java.util.Map">
        select (select sum(l.loan_amt)
        from loan l
        where l.channel = #{channel}
        and l.loan_status = 'SUCCESS'
        and l.loan_time &gt;= #{startDate}
        and l.loan_time &lt; #{endDate})                          as 'S1001',
        (select sum(lr.interest_amt)
        from loan_replan lr
        where lr.channel = #{channel}
        and lr.repay_date &gt;= #{startDate}
        and lr.repay_date &lt; #{endDate})                        as 'S1011',
        0.00                                                   as 'S1012',
        (select sum(brr.principal_amt)
        from bank_repay_record brr
        where brr.repay_status = 'SUCCESS'
        and brr.channel = #{channel}
        and brr.repay_time &gt;= #{startDate}
        and brr.repay_time &lt; #{endDate})                       as 'S1021',
        (select sum(brr.interest_amt)
        from bank_repay_record brr
        where brr.repay_status = 'SUCCESS'
        and brr.channel = #{channel}
        and brr.repay_time &gt;= #{startDate}
        and brr.repay_time &lt; #{endDate})                       as 'S1022',
        0.00                                                   as 'S1023',
        (select sum(brr.principal_amt)
        from bank_repay_record brr
        where brr.repay_status = 'SUCCESS'
        and brr.channel = #{channel}
        and brr.repay_type = 'CLAIM'
        and brr.repay_time &gt;= #{startDate}
        and brr.repay_time &lt; #{endDate})                       as 'S1024',
        (select sum(brr.interest_amt)
        from bank_repay_record brr
        where brr.repay_status = 'SUCCESS'
        and brr.channel = #{channel}
        and brr.repay_type = 'CLAIM'
        and brr.repay_time &gt;= #{startDate}
        and brr.repay_time &lt; #{endDate})                       as 'S1025',
        0.00                                                   as 'S1026',
        0.00                                                   as 'S1027',
        0.00                                                   as 'S1028',
        0.00                                                   as 'S1029',
        0.00                                                   as 'S1030',
        0.00                                                   as 'S1031',
        (select ((select ifnull(sum(loan_amt), 0.00)
        from loan
        where channel = #{channel}
        and loan_status = 'SUCCESS'
        and loan_time &lt; #{endDate})
        -
        (select ifnull(sum(principal_amt), 0.00)
        from bank_repay_record
        where channel = #{channel}
        and repay_status = 'SUCCESS'
        and repay_time &lt; #{endDate})))                       as 'S1091',
        (select sum(lr.interest_amt)
        from loan_replan lr
        where lr.channel = #{channel}
        and lr.repay_date &lt; #{endDate}
        and not exists(select 1
        from bank_repay_record brr
        where brr.repay_status = 'SUCCESS'
        and brr.loan_id = lr.loan_id
        and brr.repay_time &lt; #{endDate}
        and (brr.period = lr.period
        or (brr.period &lt; lr.period
        and brr.repay_purpose = 'CLEAR'))))                       as 'S1092',
        0.00                                                    as 'S1093'
    </select>

    <select id="findBankNotSettleLoan" resultType="com.jinghang.capital.batch.entity.Loan">
        select l.*
        from loan l
        where l.channel = #{channel}
        and l.loan_status = 'SUCCESS'
        and l.loan_time &lt;= #{endDate}
        and not exists(select 1
        from bank_repay_record brr
        where brr.loan_id = l.id
        and brr.repay_status = 'SUCCESS'
        and brr.repay_time &lt; #{endDate}
        and (brr.repay_purpose = 'CLEAR' or brr.period = l.periods));
    </select>

    <select id="findCustomerNotSettleLoan" resultType="com.jinghang.capital.batch.entity.Loan">
        select l.*
        from loan l
        where l.channel = #{channel}
          and l.loan_status = 'SUCCESS'
          and l.loan_time &lt;= #{endDate}
          and not exists(select 1
                         from customer_repay_record crr
                         where crr.loan_id = l.id
                           and crr.repay_status = 'SUCCESS'
                           and crr.repay_time &lt; #{endDate}
                           and (crr.repay_purpose = 'CLEAR' or crr.period = l.periods));
    </select>

</mapper>

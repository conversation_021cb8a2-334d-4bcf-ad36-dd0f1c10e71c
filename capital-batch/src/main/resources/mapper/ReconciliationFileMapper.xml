<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.ReconciliationFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jinghang.capital.batch.entity.ReconciliationFile">
        <id column="id" property="id" />
        <result column="file_type" property="fileType" />
        <result column="file_name" property="fileName" />
        <result column="file_date" property="fileDate" />
        <result column="input_url" property="inputUrl" />
        <result column="output_url" property="outputUrl" />
        <result column="mode" property="mode" />
        <result column="source_oss_bucket" property="sourceOssBucket" />
        <result column="source_oss_key" property="sourceOssKey" />
        <result column="source_oss_url" property="sourceOssUrl" />
        <result column="reconciliation_state" property="reconciliationState" />
        <result column="target_oss_bucket" property="targetOssBucket" />
        <result column="target_oss_key" property="targetOssKey" />
        <result column="remark" property="remark" />
        <result column="revision" property="revision" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_type, file_name, file_date, input_url, output_url, mode, source_oss_bucket, source_oss_key, source_oss_url, reconciliation_state, target_oss_bucket, target_oss_key, remark, revision, created_by, created_time, updated_by, updated_time
    </sql>

</mapper>

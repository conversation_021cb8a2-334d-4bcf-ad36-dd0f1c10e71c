<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.capital.batch.mapper.RlqdbReconFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jinghang.capital.batch.entity.RlqdbReconcileFile">
        <id column="id" property="id" />
        <result column="product" property="product" />
        <result column="channel" property="channel" />
        <result column="recc_type" property="reccType" />
        <result column="file_name" property="fileName" />
        <result column="file_date" property="fileDate" />
        <result column="recc_state" property="reccState" />
        <result column="oss_bucket" property="ossBucket" />
        <result column="oss_key" property="ossKey" />
        <result column="oss_url" property="ossUrl" />
        <result column="recc_date" property="reccDate" />
        <result column="remark" property="remark" />
        <result column="revision" property="revision" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product, channel, recc_type, file_name, file_date, recc_state, oss_bucket, oss_key, oss_url, recc_date, remark, revision, created_by, created_time, updated_by, updated_time
    </sql>

</mapper>

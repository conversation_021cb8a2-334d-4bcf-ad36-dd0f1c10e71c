package com.jinghang.capital.batch.job.common;

import com.jinghang.capital.batch.FinBatchApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = FinBatchApplication.class, properties = "eureka.client.register-with-eureka:false")
public class ClaimRetryJobTest {

    @Autowired
    private ClaimRetryJob claimRetryJob;


    @Test
    void test() throws Exception {
        String param = "{\"claimDay\": \"2023-10-09\", \"bankChannel\": \"YL_QJ\"}";
        claimRetryJob.execute(param);
    }
}

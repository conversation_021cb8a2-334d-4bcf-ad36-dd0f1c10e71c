package com.jinghang.capital.batch.job.cybk;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.capital.batch.FinBatchApplication;
import com.jinghang.capital.batch.domain.cybk.CYBKReccFileTypeEnum;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;
import com.jinghang.capital.batch.job.cybk.cust.CYBKCustDailyGuaranteeJob;
import com.jinghang.capital.batch.job.cybk.cust.CYBKCustDailyGuaranteePlanJob;
import com.jinghang.capital.batch.job.cybk.cust.CYBKCustDailyLoanJob;
import com.jinghang.capital.batch.job.cybk.cust.CYBKCustDailyLoanPlanJob;
import com.jinghang.capital.batch.job.cybk.cust.CYBKCustRepayReccJob;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccFilePullJob;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccJob;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReconcilitionFileUploadToAntJob;
import com.jinghang.capital.batch.mapper.LoanMapper;
import com.jinghang.common.util.JsonUtil;
import com.netflix.discovery.converters.Auto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.List;

@SpringBootTest(classes = FinBatchApplication.class
//        ,
//        properties = {
//    "eureka.client.service-url.defaultZone = http://localhost:7900/eureka",
//    "spring.datasource.url=*********************************************************************************************************************************************************************",
//    "spring.datasource.username=fin_service",
//    "spring.datasource.password=3cuwU-Da9RXE"
//}
)
public class CybkJobTest {

    @Autowired
    private CYBKReccFilePullJob cybkReccFilePullJob;
    @Autowired
    private CYBKReccJob cybkReccJob;
    @Autowired
    private CYBKClaimMarkJob cybkClaimMarkJob;
    @Autowired
    private CYBKCustDailyLoanJob cybkCustDailyLoanJob;
    @Autowired
    private CYBKCustDailyLoanPlanJob cybkCustDailyLoanPlanJob;
    @Autowired
    private CYBKCustDailyGuaranteeJob cybkCustDailyGuaranteeJob;
    @Autowired
    private CYBKCustDailyGuaranteePlanJob cybkCustDailyGuaranteePlanJob;
    @Autowired
    private CYBKCustRepayReccJob cybkCustRepayReccJob;
    @Autowired
    private CYBKContractDownloadJob cybkContractDownloadJob;
    @Autowired
    private CYBKClearFileApplyJob cybkClearFileApplyJob;
    @Autowired
    private CYBKClearFileQueryJob cybkClearFileQueryJob;

//    @Autowired
//    private CYBKClearFileDownloadJob cybkClearFileDownloadJob;
//    @Autowired
//    private CYBKClearFileRequestDownloadJob cybkClearFileRequestDownloadJob;

//    @Test
//    void testClearApply() throws Exception {
//        CYBKReccParam param = new CYBKReccParam();
//        param.setReccDay(LocalDate.of(2024,9,21));
//        cybkClearFileRequestDownloadJob.execute(JSONObject.toJSONString(param));
//    }
//    @Test
//    void testClearQuery() throws Exception {
//        CYBKReccParam param = new CYBKReccParam();
////        param.setReccDay(LocalDate.of(2024,9,21));
//        cybkClearFileDownloadJob.execute(JSONObject.toJSONString(param));
//    }

    @Test
    void testDailyLoan() throws Exception {
        CYBKReccParam param = new CYBKReccParam();
        param.setReccDay(LocalDate.of(2032,12,30));
//        param.setReccType(ReccType.);
        cybkCustRepayReccJob.execute(JsonUtil.convertToString(param));
    }

    @Test
    void testClaimMark() throws Exception {
        CYBKClaimMarkParam param = new CYBKClaimMarkParam();
//        param.setRepayDay(LocalDate.of(2024,9,21));
        cybkClaimMarkJob.execute(JSONObject.toJSONString(param));
    }

    @Test
    void testReccFile() throws Exception {
        ReccJobParamEntity reccJobParamEntity = new ReccJobParamEntity();
        reccJobParamEntity.setFileDate("2033-11-15");
        reccJobParamEntity.setFileType(List.of(CYBKReccFileTypeEnum.REPAYMENT_FILE.name()));
        String jsonParams = JSON.toJSONString(reccJobParamEntity);
        System.out.println(jsonParams);
        cybkReccFilePullJob.execute(jsonParams);
    }

    @Test
    void testReccJob() throws Exception {
        CYBKReccParam param = new CYBKReccParam();
        param.setReccType(ReccType.REPAY);
        param.setReccDay(LocalDate.of(2033,11,15));

        cybkReccJob.execute(JSONObject.toJSONString(param));
    }

    @Test
    void testDownContractJob() throws Exception {
        CYBKReccParam param = new CYBKReccParam();
        param.setReccDay(LocalDate.of(2033,5,7));

        cybkContractDownloadJob.execute(JSONObject.toJSONString(param));
    }

    @Test
    void testClearApplyJob() throws Exception {
        CYBKReccParam param = new CYBKReccParam();
        param.setReccDay(LocalDate.of(2033,6,8));

        cybkClearFileApplyJob.execute(JSONObject.toJSONString(param));
    }

    @Test
    void testClearQueryJob() throws Exception {
        ReccJobParamEntity param = new ReccJobParamEntity();
        param.setFileDate("2033-06-08");

        cybkClearFileQueryJob.execute(JSONObject.toJSONString(param));
    }

    @Test
    void testCustRecc() throws Exception {
        CYBKReccParam param = new CYBKReccParam();
        param.setReccDay(LocalDate.of(2033,12,14));
        cybkCustDailyLoanJob.execute(JsonUtil.convertToString(param));
        cybkCustDailyLoanPlanJob.execute(JsonUtil.convertToString(param));
        cybkCustDailyGuaranteeJob.execute(JsonUtil.convertToString(param));
        cybkCustDailyGuaranteePlanJob.execute(JsonUtil.convertToString(param));
    }

    @Autowired
    com.jinghang.capital.batch.job.cybk.recc.CYBKReconcilitionFileUploadToAntJob cybkReconcilitionFileUploadToAntJob;

    @Test
    void testFileUpload() throws Exception {
        ReccUploadAntJobParamEntity jobEntity = new ReccUploadAntJobParamEntity();
        jobEntity.setFileDate("2025-07-29");
//        jobEntity.setStartDate("2025-07-27");
//        jobEntity.setEndDate("2025-07-28");
        jobEntity.setFileType(null);
        jobEntity.setChannelType("HXBK");
        jobEntity.setEnableRewrite(false);
        jobEntity.setNeedCover(true);
        cybkReconcilitionFileUploadToAntJob.execute(JsonUtil.convertToString(jobEntity));
    }
}

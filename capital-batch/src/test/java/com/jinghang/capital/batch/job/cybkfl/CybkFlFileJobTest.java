package com.jinghang.capital.batch.job.cybkfl;

import com.jinghang.capital.batch.FinBatchApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 */
@SpringBootTest(classes = FinBatchApplication.class, properties = "eureka.client.register-with-eureka = false")
class CybkFlFileJobTest {

    //@Autowired
    //private CybkFlFpdDetectionMarkJob job;
    //
    //
    //@Test
    //void contractUploadJob() throws Exception {
    //    String param = "{\"configList\":[{\"fpdDay\":7,\"fpdWarning\":0.2}]}";
    //    job.execute(param);
    //}

}

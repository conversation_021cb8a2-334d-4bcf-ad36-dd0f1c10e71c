package com.jinghang.capital.batch.test;

import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.service.tc.ILoanService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class MapperTest {

//    @Autowired
//    private IWldCompensationFileDetailService iWldCompensationFileDetailService;
//    @Autowired
//    ILoanService iLoanService;
//
//    @Test
//    public void testMapper() {
//        List<WldCompensationFileDetail> detail = iWldCompensationFileDetailService.list();
//    }
//
//    @Test
//    public void testMybatisPlus() {
////        String outerLoanId = Optional.of(.get().getOuterLoanId().orElseThrow());
//        List<Loan> outerLoanId = iLoanService.lambdaQuery().eq(Loan::getId, "1").select(Loan::getOuterLoanId).list();
//
//        System.out.println(outerLoanId);
//    }


}

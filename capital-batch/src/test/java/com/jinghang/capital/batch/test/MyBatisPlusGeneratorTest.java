package com.jinghang.capital.batch.test;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.jinghang.capital.batch.FinBatchApplication;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 代码生成工具
 * @date 2023-05-14 19:00:38
 */

@SpringBootTest(classes = FinBatchApplication.class)
public class MyBatisPlusGeneratorTest {


    /**
     * 需要手动修改的参数
     * <p>
     * 注意:
     * fileOverride = true 时, 会覆盖已有的文件, 请谨慎使用 (!!! 本例中,设置为true !!!)
     */

    // 数据表列表 , 为空则生成全部
    protected static final List<String> schemas = List.of(); // List.of("table_1", "table_2");
    // 对应项目名字的统一前缀, 例如: demo-model, 取值:demo, 生成的文件会放在demo-model中
    protected static final String project_prefix = "fin-batch";
    // 生成的包名 对应父pom中<artifactId>的值 如 <artifactId>demo-all</artifactId> 取值: demo.all
    protected static final String package_module = "fin.batch";
    // 生成的文件的包名, 涉及service项目, 例如:取值generator, 生成的文件会放在demo-service/src/main/java/com/moan/demo/all/service/generator 中
    // protected static final String _file_package = "generator";

    protected static final String db_url = "**********************************************************************************************************************************************************************";
    protected static final String db_user = "fin_service";
    protected static final String db_password = "3MdHqaW!XYx";
    protected static final String author = "system";


    public static void main(String[] args) {

        File file = new File("");
        String projectPath = file.getAbsolutePath();

        String entryPath = projectPath + "/" + project_prefix + "/src/main/java/com/qiangyun/fin/batch/entity";
        String mapperPath = projectPath + "/" + project_prefix + "/src/main/java/com/qiangyun/fin/batch/mapper";
        String mapperXmlPath = projectPath + "/" + project_prefix + "/src/main/resources/mapper";
        String servicePath = projectPath + "/" + project_prefix + "/src/main/java/com/qiangyun/fin/batch/service/tc";
        String serviceImplPath = servicePath + "/impl";
//        String controllerPath = projectPath + "/" + project_prefix + "-start/src/main/java/com/moan/demo/all/start/api/impl/controller";

        Map<OutputFile, String> pathInfoMap = new HashMap();
        pathInfoMap.put(OutputFile.entity, entryPath);
        pathInfoMap.put(OutputFile.mapper, mapperPath);
        pathInfoMap.put(OutputFile.xml, mapperXmlPath);
        pathInfoMap.put(OutputFile.service, servicePath);
        pathInfoMap.put(OutputFile.serviceImpl, serviceImplPath);
//        pathInfoMap.put(OutputFile.controller, controllerPath);

        FastAutoGenerator.create(db_url, db_user, db_password)
                //全局配置
                .globalConfig(builder -> {
                    builder.author(author)                                              // 设置作者
                            //.fileOverride()                                                 // 覆盖已生成文件
                            .disableOpenDir()                                               // 禁止打开输出目录
                            .dateType(DateType.TIME_PACK)                                   // 时间策略
                            .commentDate("yyyy-MM-dd")                                      // 注释日期
                    //.outputDir("E://Document/generator");                           // 指定输出目录
                    ;
                })
                //包配置
                .packageConfig(builder -> {
                    builder.parent("com.qiangyun")                                    // 设置父包名(也可以生成目录)
                            .moduleName(package_module)                                     // 设置父包模块名
                            .entity("entity")
                            .service("service.tc")
                            .serviceImpl("service.tc.impl")
//                            .controller("start.api.impl.controller")
                            .mapper("mapper")                                           // Mapper 包名
                            .xml("mapper.xml")                                              // Mapper XML 包名
                            .pathInfo(pathInfoMap);           // 设置mapperXml生成路径

                })
                //策略配置
                .strategyConfig(builder -> {
                    builder
                            .addInclude(schemas)                                          // 设置需要生成的表名
                            .addTablePrefix("")                                       // 表前缀过滤
                            .entityBuilder()                                            // 切换至Entity设置
//                            .superClass(BasePO.class)                                   // 设置父类
//                            .addIgnoreColumns("is_deleted", "create_userid", "create_username", "create_userip", "create_time", "create_time_db", "update_userid", "update_username", "update_userip", "update_time", "update_time_db", "tenant_id") // 设置忽略字段(这些字段在父类中已经存在
//                            .idType(IdType.INPUT)                                      // 主键策略
                            //.versionColumnName("version")                               // 乐观锁字段名(数据库)
//                            .logicDeleteColumnName("isDeleted")                         // 逻辑删除字段名(数据库)
//                            .enableLombok()                                 // lombok生效
//                            .enableTableFieldAnnotation()                               // 所有实体类加注解
//                            .serviceBuilder()                                           // 切换至Service层设置
//                            .formatServiceFileName("%sService")// 设定后缀名
//                            .formatServiceImplFileName("%sServiceImpl")                // 设定后缀名
                            .mapperBuilder()                        // 切换至mapper.xml
                            .enableBaseColumnList()
                            .enableBaseResultMap();

                })
                //模板配置
                .templateEngine(new FreemarkerTemplateEngine())                         // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}

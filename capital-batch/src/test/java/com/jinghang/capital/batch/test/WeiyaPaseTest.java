package com.jinghang.capital.batch.test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.common.util.JsonUtil;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

public class WeiyaPaseTest {
    public static void main(String[] args) throws Exception {
        //new WeiyaPaseTest().test1();
    }

    //public void test1() throws Exception {
    //    Path tempPath = Paths.get("/Users/<USER>/Desktop/other/test/weiya/file/zyxfB91_20250223.dat");
    //    Stream<String> lines = Files.lines(tempPath, StandardCharsets.UTF_8);
    //    lines.skip(1).forEach(key -> {
	//        WeiyaReccLoanDto weiyaReccLoanDto = null;
	//        try {
	//	        weiyaReccLoanDto = JsonUtil.convertToObject(key, WeiyaReccLoanDto.class);
	//        } catch (JsonProcessingException e) {
	//	        throw new RuntimeException(e);
	//        }
	//        System.out.println(weiyaReccLoanDto);
    //    });
    //}
}

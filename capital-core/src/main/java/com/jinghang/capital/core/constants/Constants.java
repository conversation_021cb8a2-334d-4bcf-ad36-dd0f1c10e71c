package com.jinghang.capital.core.constants;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/1
 * @description 常量
 */
public class Constants {

    public static final String SLASH = "/";
    public static final String UNDERSCORE = "_";
    public static final String LINE = "_";
    public static final String DOT = ".";
    public static final int BYTE_BUFFER_SIZE = 1024;
    public static final String PDF_SUFFIX = ".pdf";

    public static final String TAR_SUFFIX = ".tar";

    public static final String JPG_SUFFIX = ".jpg";



    public static final String PDF_FILE = ".pdf";

    public static final String DEFAULT_UNIT = "单位未知";

    public static final String UNKNOWN = "未知";

    /**
     * core身份证有效期长期值
     */
    public static final LocalDate CORE_ID_PERMANENT_END = LocalDate.of(2099, 1, 1);

}

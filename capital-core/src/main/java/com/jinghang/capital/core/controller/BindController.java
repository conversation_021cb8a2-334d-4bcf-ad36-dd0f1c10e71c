package com.jinghang.capital.core.controller;


import com.jinghang.capital.api.BindService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.jinghang.capital.api.dto.credit.BindConfirmDto;
import com.jinghang.capital.api.dto.credit.BindResultDto;
import com.jinghang.capital.core.convert.apivo.ApiBindConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MockService;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindConfirmVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;


import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绑卡服务
 */
@RestController
@RequestMapping("bind")
public class BindController implements BindService {
    private static final Logger logger = LoggerFactory.getLogger(BindController.class);

    private final ManageService manageService;

    @Autowired
    private MockService mockService;

    @Value("${sms.mock.qhyp}")
    private Boolean mock;

    @Autowired
    public BindController(ManageService manageService) {
        this.manageService = manageService;
    }


    @Override
    public RestResult<BindResultDto> bindApply(BindApplyDto bindApply) {
        if (mock) {
            return mockService.bindApply(bindApply);
        }

        logger.info("begin bind card apply: {}", JsonUtil.toJsonString(bindApply));
        BindApplyVo bindApplyVo = ApiBindConvert.INSTANCE.toBindApplyVo(bindApply);

        BindResultVo resultVo = manageService.bindApply(bindApplyVo);
        BindResultDto resultDto = ApiBindConvert.INSTANCE.toBindResultDto(resultVo);
        logger.info("end bind card apply: {}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);

    }

    @Override
    public RestResult<BindResultDto> bindConfirm(BindConfirmDto bindConfirm) {
        if (mock) {
            return mockService.bindConfirm(bindConfirm);
        }

        logger.info("begin bind confirm: {}", JsonUtil.toJsonString(bindConfirm));

        BindConfirmVo creditApplyVo = ApiBindConvert.INSTANCE.toBindConfirmVo(bindConfirm);
        BindResultVo resultVo = manageService.bindConfirm(creditApplyVo);
        BindResultDto creditDto = ApiBindConvert.INSTANCE.toBindResultDto(resultVo);

        logger.info("end bind confirm: {}", JsonUtil.toJsonString(creditDto));
        return RestResult.success(creditDto);
    }
}

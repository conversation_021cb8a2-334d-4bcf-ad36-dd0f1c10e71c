package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.ProtocolService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.protocol.ProtocolSyncDto;
import com.jinghang.capital.api.dto.protocol.ProtocolSyncRltDto;
import com.jinghang.capital.core.convert.apivo.ProtocolSyncApiConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@RestController
@RequestMapping("protocol")
public class ProtocolController implements ProtocolService {

    private static final Logger logger = LoggerFactory.getLogger(ProtocolController.class);

    private final ManageService manageService;

    @Autowired
    public ProtocolController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<ProtocolSyncRltDto> sync(ProtocolSyncDto protocolSyncDto) {
        ProtocolSyncVo protocolSyncVo = ProtocolSyncApiConvert.INSTANCE.toVo(protocolSyncDto);
        manageService.protocolSync(protocolSyncVo);
        return RestResult.success(null);
    }
}

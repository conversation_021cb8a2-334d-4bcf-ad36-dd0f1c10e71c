package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.VoucherFileService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.ResultCode;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.core.convert.apivo.ApiFileConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;


import com.jinghang.common.util.JsonUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
@RestController
@RequestMapping("voucher")
public class VoucherFileController implements VoucherFileService {

    private static final Logger logger = LoggerFactory.getLogger(VoucherFileController.class);

    private final ManageService manageService;

    @Autowired
    public VoucherFileController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<FileDownloadResultDto> download(FileDownloadDto fileDownloadDto) {
        RestResult<FileDownloadResultDto> result;
        logger.info("文件下载: {}", fileDownloadDto.toString());
        try {
            FileDownloadVo fileDownloadVo = ApiFileConvert.INSTANCE.toVo(fileDownloadDto);
            FileDownloadResultVo resultVo = manageService.voucherFileDownload(fileDownloadVo);
            FileDownloadResultDto downloadResultDto = ApiFileConvert.INSTANCE.toDto(resultVo);
            result = RestResult.success(downloadResultDto);
        } catch (Exception e) {
            logger.info("文件下载,发生异常", e);
            result = RestResult.fail(ResultCode.BIZ_ERROR, e.getMessage());
        }
        logger.info("文件下载,响应报文:{}", JsonUtil.toJsonString(result));
        return result;
    }

    @Override
    public RestResult<Void> fileApplyQuery(FileDailyProcessDto processDto) {
        logger.info("文件处理: [{}]", processDto.toString());
        FileDailyProcessVo fileDailyProcessVo = ApiFileConvert.INSTANCE.toProcessVo(processDto);
        manageService.processDailyFile(fileDailyProcessVo);
        return RestResult.success(null);
    }


    @Override
    public RestResult<Void> batchDownload(FileDailyProcessDto processDto) {
        logger.info("批量下载文件: [{}]", processDto.toString());
        FileDailyProcessVo fileDailyProcessVo = ApiFileConvert.INSTANCE.toProcessVo(processDto);
        manageService.batchVoucherDownload(fileDailyProcessVo);
        return RestResult.success(null);
    }

}

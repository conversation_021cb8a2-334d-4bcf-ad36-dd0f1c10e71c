package com.jinghang.capital.core.convert;


import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.LoanStatus;
import org.mapstruct.Mapper;

@Mapper
public interface StatusConvert {
    default ProcessStatus convertCreditStatus(CreditStatus status) {
        return switch (status) {
            case INIT -> ProcessStatus.INIT;
            case PROCESSING -> ProcessStatus.PROCESSING;
            case SUCCESS -> ProcessStatus.SUCCESS;
            default -> ProcessStatus.FAIL;
        };
    }

    default ProcessStatus convertLoanStatus(LoanStatus status) {
        return switch (status) {
            case INIT -> ProcessStatus.INIT;
            case PROCESSING -> ProcessStatus.PROCESSING;
            case SUCCESS -> ProcessStatus.SUCCESS;
            default -> ProcessStatus.FAIL;
        };
    }

    default ProcessStatus toApiProcessStatus(ProcessStatus status) {
        if (status == null) {
            return null;
        }
        return switch (status) {
            case INIT -> ProcessStatus.INIT;
            case PROCESSING -> ProcessStatus.PROCESSING;
            case SUCCESS -> ProcessStatus.SUCCESS;
            default -> ProcessStatus.FAIL;
        };
    }

}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.ActiveLaunchClaimApplyDto;
import com.jinghang.capital.api.dto.repay.ActiveLaunchClaimResultDto;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveLaunchClaimResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActiveLaunchClaimConvert {

    ActiveLaunchClaimConvert INSTANCE = Mappers.getMapper(ActiveLaunchClaimConvert.class);

    ActiveLaunchClaimApplyVo toApplyVo(ActiveLaunchClaimApplyDto dto);

    ActiveLaunchClaimResultDto toResultDto(ActiveLaunchClaimResultVo result);
}

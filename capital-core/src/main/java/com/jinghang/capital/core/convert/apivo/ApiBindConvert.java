package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.jinghang.capital.api.dto.credit.BindConfirmDto;
import com.jinghang.capital.api.dto.credit.BindResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindConfirmVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class, BankChannelConvert.class, BankCardConvert.class})
public interface ApiBindConvert {
    ApiBindConvert INSTANCE = Mappers.getMapper(ApiBindConvert.class);

    BindApplyVo toBindApplyVo(BindApplyDto apply);

    BindConfirmVo toBindConfirmVo(BindConfirmDto confirm);

    BindResultDto toBindResultDto(BindResultVo result);


}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.credit.CreditApplyDto;
import com.jinghang.capital.api.dto.credit.CreditQueryDto;
import com.jinghang.capital.api.dto.credit.CreditResultDto;
import com.jinghang.capital.api.dto.credit.ExtInfoDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.capital.api.dto.credit.RecreditApplyDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyVo;
import com.jinghang.capital.core.vo.credit.RecreditApplyVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class, BankChannelConvert.class, ProductConvert.class,
    IdCardConvert.class, BankCardConvert.class, UserInfoConvert.class, MerchantConvert.class,
    UserContactInfoConvert.class, FileInfoConvert.class, FlowChannelConvert.class, ExtInfoConvert.class})
public interface ApiCreditConvert {
    ApiCreditConvert INSTANCE = Mappers.getMapper(ApiCreditConvert.class);

    @Mapping(source = "userContactInfoDtoList", target = "userContactInfoVoList")
    @Mapping(source = "fileInfoDtoList", target = "fileInfoVoList")
    @Mapping(source = "extInfo", target = "extInfo", resultType = ExtInfoVo.class)
    CreditApplyVo<ExtInfoVo> toCreditApplyVo(CreditApplyDto<ExtInfoDto> apply);

    CreditQueryVo toCreditQueryVo(CreditQueryDto query);

    CreditResultDto toCreditResultDto(CreditResultVo result);

    RecreditApplyVo toRecreditApplyVo(RecreditApplyDto recreditApply);

    PreCreditApplyVo toPreCreditApplyVo(PreCreditApplyDto preCreditApply);

    PreCreditApplyResultDto toPreCreditApplyDto(PreCreditApplyResultVo result);


}

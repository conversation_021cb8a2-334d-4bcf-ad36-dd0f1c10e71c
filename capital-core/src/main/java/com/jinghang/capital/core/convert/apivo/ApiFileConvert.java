package com.jinghang.capital.core.convert.apivo;



import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadRequestDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanResultDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileResultDto;
import com.jinghang.capital.api.dto.file.FileUploadDto;
import com.jinghang.capital.api.dto.file.FileUploadResultDto;
import com.jinghang.capital.api.dto.file.PreviewDto;
import com.jinghang.capital.api.dto.file.PreviewResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.convert.TypeConvert;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanResultDVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileResultVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileVo;
import com.jinghang.capital.core.vo.file.FileUploadResultVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
@Mapper(uses = {BankChannelConvert.class, ProductConvert.class, StatusConvert.class, TypeConvert.class})
public interface ApiFileConvert {

    ApiFileConvert INSTANCE = Mappers.getMapper(ApiFileConvert.class);

    FileDownloadVo toVo(FileDownloadDto query);

    FileDownloadResultDto toDto(FileDownloadResultVo result);

    FileUploadVo toUploadVo(FileUploadDto query);

    FileUploadResultDto toUploadDto(FileUploadResultVo result);

    FileDailyProcessVo toProcessVo(FileDailyProcessDto dto);

    FileDownloadRequestVo toDownloadRequestVo(FileDownloadRequestDto fileDownloadRequestDto);

    PreviewVo toPreviewVo(PreviewDto dto);

    PreviewResultDto toPreviewDto(PreviewResultVo result);

    FileSynRepayPlanVo toSynVo(FileSynRepayPlanDto query);

    FileSynRepayPlanResultDto toSynDto(FileSynRepayPlanResultDVo result);

    FileSyncDueFileVo toDueVo(FileSyncDueFileDto result);

    FileSyncDueFileResultDto toDueDto(FileSyncDueFileResultVo result);


}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.plan.PlanSyncDto;
import com.jinghang.capital.api.dto.repay.OverduePlanDto;
import com.jinghang.capital.api.dto.repay.OverduePlanQueryDto;
import com.jinghang.capital.api.dto.repay.PlanDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.vo.repay.OverduePlanQueryVo;
import com.jinghang.capital.core.vo.repay.OverduePlanVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Mapper(uses = {BankChannelConvert.class, ProductConvert.class})
public interface ApiOverduePlanConvert {
    ApiOverduePlanConvert INSTANCE = Mappers.getMapper(ApiOverduePlanConvert.class);

    PlanSyncVo toVo(PlanSyncDto apiDto);

    OverduePlanQueryVo toVo(OverduePlanQueryDto apiDto);

    PlanVo toVo(PlanDto planDetailDto);

    OverduePlanDto toDto(OverduePlanVo planQueryVo);


}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.plan.BankPlanQueryDto;
import com.jinghang.capital.api.dto.plan.BankPlanQueryRltDto;
import com.jinghang.capital.api.dto.plan.PlanOverdueDto;
import com.jinghang.capital.api.dto.plan.PlanSyncDto;
import com.jinghang.capital.api.dto.repay.PlanDto;
import com.jinghang.capital.api.dto.repay.PlanQueryDto;
import com.jinghang.capital.api.dto.repay.TrustPlanReleaseDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.vo.repay.BankPlanQueryRltVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryVo;
import com.jinghang.capital.core.vo.repay.PlanOverdueVo;
import com.jinghang.capital.core.vo.repay.PlanQueryVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.capital.core.vo.repay.TrustPlanReleaseVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Mapper(uses = {BankChannelConvert.class, ProductConvert.class})
public interface ApiPlanConvert {
    ApiPlanConvert INSTANCE = Mappers.getMapper(ApiPlanConvert.class);

    PlanSyncVo toVo(PlanSyncDto apiDto);

    PlanQueryVo toVo(PlanQueryDto apiDto);

    PlanVo toVo(PlanDto planDetailDto);

    PlanOverdueVo toVo(PlanOverdueDto apiDto);

    TrustPlanReleaseVo toVo(TrustPlanReleaseDto apiDto);

    BankPlanQueryVo toVo(BankPlanQueryDto apiDto);

    BankPlanQueryRltDto toDto(BankPlanQueryRltVo apiDto);

    PlanDto toDto(PlanVo planQueryVo);


}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.*;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.repay.*;
import org.mapstruct.Mapper;
import org.mapstruct.ValueMapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/20
 */
@Mapper(uses = {StatusConvert.class})
public interface ApiRepayConvert {
    ApiRepayConvert INSTANCE = Mappers.getMapper(ApiRepayConvert.class);

    RepayTrailVo toVo(RepayTrailDto trailDto);

    RepayApplyVo toVo(RepayApplyDto applyDto);

    RepayQueryVo toVo(RepayQueryDto queryDto);

    BatchTrailVo toVo(BatchTrailDto trailDto);

    RepayDeductionApplyVo toVo(RepayDeductionApplyDto applyDto);

    CompensatedRepaySyncVo toVo(CompensatedRepaySyncDto compensatedRepaySyncDto);

    DefrayVo toVo(DefrayDto defrayDto);

    SubstituteMarkApplyVo toVo(SubstituteMarkApplyDto applyDto);

    SubstituteApplyVo toVo(SubstituteApplyDto applyDto);



    TrailResultDto toDto(TrailResultVo trailResultVo);

    RepayResultDto toDto(RepayResultVo repayResultVo);

    RepayBatchResultDto toDto(RepayBatchResultVo repayBatchResultVo);

    CompensatedRepaySyncRltDto toDto(CompensatedRepaySyncRlt compensatedRepaySyncRlt);

    BatchTrialResultDto toDto(BatchTrialResultVo batchTrialResultVo);

    DefrayResultDto toDto(DefrayResultVo defrayResultVo);

    SubstituteMarkResultDto toDto(SubstituteMarkResultVo substituteMarkResultVo);

    SubstituteApplyResultDto toDto(SubstituteApplyResultVo substituteApplyResultVo);


    Payee toPayeeDto(Payee payee);

    @ValueMapping(source = "P", target = "PROCESSING")
    @ValueMapping(source = "S", target = "PROCESSING")
    @ValueMapping(source = "F", target = "PROCESSING")
    @ValueMapping(source = "R", target = "PROCESSING")
    @ValueMapping(source = "U", target = "PROCESSING")
    ProcessStatus map(com.jinghang.capital.api.dto.ProcessStatus status);

    RepayReturnUploadVo toVo(RepayReturnUploadDto dto);

}

package com.jinghang.capital.core.convert.apivo;

import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.vo.bank.BankResultBackVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {BankChannelConvert.class})
public interface BankCallBackConvert {
    BankCallBackConvert INSTANCE = Mappers.getMapper(BankCallBackConvert.class);

    BankResultBackVo toBankResultBackVo(BankResultBackDto dto);
}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.bank.BankFetchApplyDto;
import com.jinghang.capital.api.dto.bank.BankFetchResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.bank.BankFetchApplyVo;
import com.jinghang.capital.core.vo.bank.BankFetchResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface BankFetchConvert {

    BankFetchConvert INSTANCE = Mappers.getMapper(BankFetchConvert.class);

    BankFetchApplyVo toApplyVo(BankFetchApplyDto applyDto);

    BankFetchResultDto toResultDto(BankFetchResultVo result);

}

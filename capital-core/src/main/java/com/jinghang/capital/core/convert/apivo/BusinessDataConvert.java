package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.BusinessChronosProcessDto;
import com.jinghang.capital.api.dto.BusinessChronosProcessResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.BusinessChronosProcessResultVo;
import com.jinghang.capital.core.vo.BusinessChronosProcessVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface BusinessDataConvert {

    BusinessDataConvert INSTANCE = Mappers.getMapper(BusinessDataConvert.class);

    BusinessChronosProcessVo toBusinessProcessVo(BusinessChronosProcessDto processDto);

    BusinessChronosProcessResultDto toBusinessProcessResultDto(BusinessChronosProcessResultVo processResultVo);

}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.ClaimMarkApplyDto;
import com.jinghang.capital.api.dto.repay.ClaimMarkResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.repay.ClaimMarkApplyVo;
import com.jinghang.capital.core.vo.repay.ClaimMarkResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface ClaimMarkConvert {

    ClaimMarkConvert INSTANCE = Mappers.getMapper(ClaimMarkConvert.class);

    ClaimMarkApplyVo toApplyVo(ClaimMarkApplyDto applyDto);

    ClaimMarkResultDto toResultDto(ClaimMarkResultVo result);

}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.ClaimRetryDto;
import com.jinghang.capital.api.dto.repay.ClaimRetryResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.repay.ClaimRetryResultVo;
import com.jinghang.capital.core.vo.repay.ClaimRetryVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface ClaimRetryConvert {

    ClaimRetryConvert INSTANCE = Mappers.getMapper(ClaimRetryConvert.class);

    ClaimRetryVo toVo(ClaimRetryDto claimRetryDto);

    ClaimRetryResultDto toResultDto(ClaimRetryResultVo claimRetryResultVo);

}

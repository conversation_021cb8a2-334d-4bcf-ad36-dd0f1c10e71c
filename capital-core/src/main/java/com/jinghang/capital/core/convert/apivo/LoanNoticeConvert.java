package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.loan.LoanNoticeDto;
import com.jinghang.capital.api.dto.loan.LoanNoticeResultDto;
import com.jinghang.capital.core.vo.loan.LoanNoticeResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LoanNoticeConvert {

    LoanNoticeConvert INSTANCE = Mappers.getMapper(LoanNoticeConvert.class);

    LoanNoticeVo toApplyVo(LoanNoticeDto loanNotice);

    LoanNoticeResultDto toResultDto(LoanNoticeResultVo result);
}

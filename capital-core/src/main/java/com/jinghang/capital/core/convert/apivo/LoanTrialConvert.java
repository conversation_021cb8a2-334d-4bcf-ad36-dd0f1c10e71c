package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.loan.LoanTrialQueryDto;
import com.jinghang.capital.api.dto.loan.LoanTrialResultDto;
import com.jinghang.capital.core.vo.loan.LoanTrialQueryVo;
import com.jinghang.capital.core.vo.loan.LoanTrialResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LoanTrialConvert {

    LoanTrialConvert INSTANCE = Mappers.getMapper(LoanTrialConvert.class);

    LoanTrialQueryVo toApplyVo(LoanTrialQueryDto queryDto);

    LoanTrialResultDto toResultDto(LoanTrialResultVo result);
}

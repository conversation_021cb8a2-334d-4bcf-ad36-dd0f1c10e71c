package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.loan.LoanLprQueryDto;
import com.jinghang.capital.api.dto.loan.LoanLprResultDto;
import com.jinghang.capital.core.vo.loan.LoanLprQueryVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface LprConvert {

    LprConvert INSTANCE = Mappers.getMapper(LprConvert.class);

    LoanLprQueryVo toApplyVo(LoanLprQueryDto queryDto);

    LoanLprResultDto toResultDto(LoanLprResultVo result);

}

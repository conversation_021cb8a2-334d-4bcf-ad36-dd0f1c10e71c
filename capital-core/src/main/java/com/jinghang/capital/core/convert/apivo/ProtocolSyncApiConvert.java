package com.jinghang.capital.core.convert.apivo;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */


import com.jinghang.capital.api.dto.protocol.ProtocolSyncDto;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {BankCardConvert.class})
public interface ProtocolSyncApiConvert {
    ProtocolSyncApiConvert INSTANCE = Mappers.getMapper(ProtocolSyncApiConvert.class);


    ProtocolSyncVo toVo(ProtocolSyncDto protocolSyncDto);
}

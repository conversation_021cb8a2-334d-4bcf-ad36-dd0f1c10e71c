package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccDownloadDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface ReccConvert {

    ReccConvert INSTANCE = Mappers.getMapper(ReccConvert.class);

    ReccApplyVo toApplyVo(ReccApplyDto applyDto);

    ReccResultDto toResultDto(ReccResultVo result);

    ReccDownloadVo toDownloadVo(ReccDownloadDto dto);


}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.RepayDateApplyDto;
import com.jinghang.capital.api.dto.repay.RepayDateResultDto;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.repay.RepayDateApplyVo;
import com.jinghang.capital.core.vo.repay.RepayDateResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface RepayDateApplyConvert {

    RepayDateApplyConvert INSTANCE = Mappers.getMapper(RepayDateApplyConvert.class);

    RepayDateApplyVo toApplyVo(RepayDateApplyDto applyDto);

    RepayDateResultDto toResultDto(RepayDateResultVo result);

}

package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.RepayNoticeDto;
import com.jinghang.capital.api.dto.repay.RepayNoticeResultDto;
import com.jinghang.capital.core.vo.repay.RepayNoticeResultVo;
import com.jinghang.capital.core.vo.repay.RepayNoticeVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RepayNoticeConvert {

    RepayNoticeConvert INSTANCE = Mappers.getMapper(RepayNoticeConvert.class);

    RepayNoticeVo toApplyVo(RepayNoticeDto repayNotice);

    RepayNoticeResultDto toResultDto(RepayNoticeResultVo result);
}

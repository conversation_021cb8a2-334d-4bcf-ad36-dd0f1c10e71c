package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.enums.AgreementType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 * @date 2023/5/20
 */
@Mapper
public interface AgreementConvert {
    AgreementConvert INSTANCE = Mappers.getMapper(AgreementConvert.class);

    /**
     * 初始化协议签署信息
     *
     * @param agrType
     * @param account
     * @param businessId
     * @return
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "businessId", target = "businessId")
    @Mapping(source = "agrType.bankChannel", target = "channel")
    @Mapping(source = "agrType.fileType", target = "fileType")
    @Mapping(source = "agrType.templateNo", target = "templateNo")
    @Mapping(source = "account.livingAddress", target = "address")
    @Mapping(source = "account.mobile", target = "bankMobilePhone")
    @Mapping(source = "account.certNo", target = "identNo")
    @Mapping(source = "account.name", target = "personName")
    @Mapping(target = "signState", expression = "java(com.jinghang.capital.core.enums.ProcessStatus.INIT)")
    @Mapping(source = "agrType.signatureType", target = "signatureType")
    AgreementSignature initAgreementSignature(AgreementType agrType, Account account, String businessId);

}

package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.vo.FileInfoVo;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.credit.BankCardInfoVo;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.IdCardInfoVo;
import com.jinghang.capital.core.vo.credit.MerchantInfoVo;
import com.jinghang.capital.core.vo.credit.UserContactInfoVo;
import com.jinghang.capital.core.vo.credit.UserInfoVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Mapper
public interface CreditLoanConvert {
    CreditLoanConvert INSTANCE = Mappers.getMapper(CreditLoanConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "id", target = "creditId")
    @Mapping(target = "outerLoanId", ignore = true)
    @Mapping(target = "loanAmt", ignore = true)
    @Mapping(target = "periods", ignore = true)
    @Mapping(target = "loanStatus", ignore = true)
    @Mapping(target = "loanTime", ignore = true)
    @Mapping(target = "loanNo", ignore = true)
    @Mapping(target = "loanContractNo", ignore = true)
    @Mapping(source = "cardId", target = "loanCardId")
    @Mapping(source = "cardId", target = "repayCardId")
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    Loan creditToLoan(Credit credit);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "bankChannel", target = "channel")
    @Mapping(source = "product", target = "productNo")
    @Mapping(source = "applyVo.idCardInfo.name", target = "custName")
    @Mapping(source = "applyVo.userInfo.mobile", target = "custMobile")
    @Mapping(source = "applyVo.idCardInfo.certNo", target = "custCertNo")
    Credit toCreditEntity(CreditApplyVo applyVo);


    FailedCredit toFailedCredit(Credit credit);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    LoanFile toLoanFileEntity(FileInfoVo fileInfoVo);

    FailedLoanFile toFailedLoanFile(LoanFile loanFile);

    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "name", source = "idCardInfoVo.name")
    Account toAccount(UserInfoVo userInfo, IdCardInfoVo idCardInfoVo);

    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    Account toAccount(IdCardInfoVo idCardInfoVo);

    FailedAccount toFailedAccount(Account account);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    AccountContactInfo toUserContactInfo(UserContactInfoVo contactInfo);

    FailedAccountContactInfo toFailedAccountContactInfo(AccountContactInfo account);

    @Mapping(source = "cardInfo.cardNo", target = "cardNo")
    @Mapping(source = "cardInfo.cardName", target = "cardName")
    @Mapping(source = "cardInfo.phone", target = "phone")
    @Mapping(source = "loan.custCertNo", target = "certNo")
    @Mapping(source = "cardInfo.bankName", target = "bankName")
    @Mapping(source = "cardInfo.protocolNo", target = "agreeNo")
    @Mapping(source = "cardInfo.payChannel", target = "payChannel")
    @Mapping(target = "id", ignore = true)
    @Mapping(source = "cardInfo.bankCode", target = "bankCode")
    @Mapping(target = "remark", ignore = true)
    AccountBankCard toLoanBankCard(BankCardInfoVo cardInfo, Loan loan);

    @Mapping(source = "cardInfo.cardNo", target = "cardNo")
    @Mapping(source = "cardInfo.cardName", target = "cardName")
    @Mapping(source = "cardInfo.phone", target = "phone")
    @Mapping(source = "idCardInfo.certNo", target = "certNo")
    @Mapping(source = "cardInfo.bankName", target = "bankName")
    @Mapping(source = "cardInfo.protocolNo", target = "agreeNo")
    @Mapping(source = "cardInfo.payChannel", target = "payChannel")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    AccountBankCard toBankCard(BankCardInfoVo cardInfo, IdCardInfoVo idCardInfo);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(source = "agreementNo", target = "agreeNo")
    @Mapping(source = "protocolChannel", target = "payChannel")
    AccountBankCard toBankCard(BindApplyVo applyVo);

    FailedAccountBankCard toFailedBankCard(AccountBankCard card);


    @Mapping(source = "certNo", target = "unifiedCode")
    @Mapping(source = "address", target = "licAddress")
    @Mapping(source = "province", target = "licProvinceCode")
    @Mapping(source = "city", target = "licCityCode")
    @Mapping(source = "district", target = "licDistrictCode")
    @Mapping(source = "contactName", target = "linkmanName")
    @Mapping(source = "contactPhone", target = "linkmanMobile")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    Shop toMerchantShop(MerchantInfoVo merchant);
}

package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.entity.ReconciliationFile;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import com.jinghang.capital.core.vo.repay.PlanPushVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
@Mapper
public interface PlanConvert {
    PlanConvert INSTANCE = Mappers.getMapper(PlanConvert.class);

    LoanReplan toEntity(PlanItemVo itemVo);

    @Mapping(source = "type", target = "fileType")
    ReconciliationFile toRecEntity(PlanSyncVo planSyncVo);

    PlanItemVo toVo(LoanReplan plan);
    @Mapping(source = "fileType", target = "type")
    PlanPushVo toVo(ReconciliationFile recFile);




}

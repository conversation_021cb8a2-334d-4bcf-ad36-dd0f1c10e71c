package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
@Mapper
public interface ProtocolSyncConvert {

    ProtocolSyncConvert INSTANCE = Mappers.getMapper(ProtocolSyncConvert.class);

    @Mapping(source = "protocolNo", target = "agreeNo")
    @Mapping(target = "channel", ignore = true)
    @Mapping(source = "bankCard.cardNo", target = "cardNo")
    @Mapping(source = "bankCard.cardName", target = "cardName")
    @Mapping(source = "bankCard.phone", target = "phone")
    @Mapping(source = "bankCard.certNo", target = "certNo")
    @Mapping(source = "bankCard.bankCode", target = "bankCode")
    @Mapping(source = "bankCard.bankName", target = "bankName")
    AccountBankCard toEntity(ProtocolSyncVo syncVo);
}

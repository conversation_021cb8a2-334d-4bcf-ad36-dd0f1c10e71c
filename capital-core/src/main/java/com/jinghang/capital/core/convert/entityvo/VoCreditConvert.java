package com.jinghang.capital.core.convert.entityvo;


import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.FailedCredit;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class})
public interface VoCreditConvert {
    VoCreditConvert INSTANCE = Mappers.getMapper(VoCreditConvert.class);


    @Mapping(source = "outerCreditId", target = "sysId")
    @Mapping(source = "id", target = "creditId")
    @Mapping(source = "periods", target = "period")
    @Mapping(source = "passTime", target = "creditTime")
    @Mapping(source = "creditStatus", target = "status")
    @Mapping(source = "remark", target = "failMsg")
    CreditResultVo toCreditResultDto(Credit credit);


    @Mapping(source = "outerCreditId", target = "sysId")
    @Mapping(source = "id", target = "creditId")
    @Mapping(source = "periods", target = "period")
    @Mapping(source = "passTime", target = "creditTime")
    @Mapping(source = "creditStatus", target = "status")
    @Mapping(source = "remark", target = "failMsg")
    CreditResultVo toFailedCreditResultDto(FailedCredit credit);


}

package com.jinghang.capital.core.convert.entityvo;


import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class})
public interface VoLoanConvert {
    VoLoanConvert INSTANCE = Mappers.getMapper(VoLoanConvert.class);


    @Mapping(source = "outerLoanId", target = "sysId")
    @Mapping(source = "id", target = "loanId")
    @Mapping(source = "periods", target = "period")
    @Mapping(source = "loanTime", target = "loanTime")
    @Mapping(source = "loanStatus", target = "status")
    @Mapping(source = "loanContractNo", target = "loanContractNo")
    LoanResultVo toLoanResultDto(Loan loan);


}

package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.repay.RepayCategory;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;

import com.jinghang.capital.core.enums.RepayType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 还款申请明细
 */
@Entity
@Table(name = "batch_customer_repay_detail")
public class BatchCustomerRepayDetail extends BaseEntity {

    /**
     * 对客还款id
     */
    private String batchRepayId;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 还款时间
     */
    private LocalDateTime repayTime;

    /**
     * 还款类型
     */
    @Enumerated(EnumType.STRING)
    private RepayType repayType;

    /**
     * 还款分类
     */
    @Enumerated(EnumType.STRING)
    private RepayCategory repayCategory;

    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;

    /**
     * 还款金额
     */
    private BigDecimal totalAmt;

    /**
     * 还款本金
     */
    private BigDecimal principalAmt;

    /**
     * 还款利息
     */
    private BigDecimal interestAmt;

    /**
     * 还款罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 融单费
     */
    private BigDecimal guaranteeFee;

    /**
     * 减免金额
     */
    private BigDecimal reductAmt;

    /**
     * 还款卡号
     */
    private String bankNo;

    /**
     * 还款卡户名
     */
    private String accountName;


    /**
     * 借据号
     */
    private String loanNo;

    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus status;


    /**
     * 还款流水号
     */
    private String tranNo;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public LocalDateTime getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    public String getBatchRepayId() {
        return batchRepayId;
    }

    public void setBatchRepayId(String batchRepayId) {
        this.batchRepayId = batchRepayId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getReductAmt() {
        return reductAmt;
    }

    public void setReductAmt(BigDecimal reductAmt) {
        this.reductAmt = reductAmt;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public String getTranNo() {
        return tranNo;
    }

    public void setTranNo(String tranNo) {
        this.tranNo = tranNo;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }
}

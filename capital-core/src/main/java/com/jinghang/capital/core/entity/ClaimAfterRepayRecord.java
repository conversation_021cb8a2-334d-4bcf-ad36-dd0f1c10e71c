package com.jinghang.capital.core.entity;

import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代偿后还款记录表
 */
@Entity
@Table(name = "claim_after_repay_record")
public class ClaimAfterRepayRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 6986126810381045897L;

    /**
     * 借款id
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;

    /**
     * 外部还款id
     */
    private String outerRepayId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus repayStatus;
    /**
     * 通知状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus notifyStatus;
    /**
     * 还款时间
     */
    private LocalDateTime repayTime;

    /**
     * 通知时间
     */
    private LocalDateTime notifyTime;
    /**
     * 总金额
     */
    private BigDecimal totalAmt;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 融单成本费 q001
     */
    private BigDecimal guaranteeFee;


    /**
     * 资产代扣费 q002
     */
    private BigDecimal deductFee;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmt;

    /**
     * 是否代还发起的通知
     */
    @Enumerated(EnumType.STRING)
    private WhetherState isSubstituteNotify;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public ProcessStatus getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(ProcessStatus repayStatus) {
        this.repayStatus = repayStatus;
    }

    public ProcessStatus getNotifyStatus() {
        return notifyStatus;
    }

    public void setNotifyStatus(ProcessStatus notifyStatus) {
        this.notifyStatus = notifyStatus;
    }

    public LocalDateTime getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    public LocalDateTime getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(LocalDateTime notifyTime) {
        this.notifyTime = notifyTime;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getReduceAmt() {
        return reduceAmt;
    }

    public void setReduceAmt(BigDecimal reduceAmt) {
        this.reduceAmt = reduceAmt;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getDeductFee() {
        return deductFee;
    }

    public void setDeductFee(BigDecimal deductFee) {
        this.deductFee = deductFee;
    }

    public WhetherState getIsSubstituteNotify() {
        return isSubstituteNotify;
    }

    public void setIsSubstituteNotify(WhetherState isSubstituteNotify) {
        this.isSubstituteNotify = isSubstituteNotify;
    }

    @Override
    public String prefix() {
        return "CAR";
    }
}

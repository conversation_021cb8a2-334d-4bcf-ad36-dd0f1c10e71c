package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Entity
@Table(name = "compensated_repay_notify")
public class CompensatedRepayNotify extends BaseEntity {
    /**
     * 单据
     */
    private String loanId;
    /**
     * 外部单据
     */
    private String outerLoanId;
    /**
     * 外部还款流水号
     */
    private String outerRepayNo;
    /**
     * 还款时间
     */
    private LocalDateTime actRepayTime;


    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    @Override
    protected String prefix() {
        return "CRN";
    }
}

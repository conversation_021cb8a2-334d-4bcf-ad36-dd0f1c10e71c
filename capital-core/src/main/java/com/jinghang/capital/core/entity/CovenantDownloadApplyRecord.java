package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.CovenantDownloadType;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * 合同下载申请记录表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-11-28
 */
@Entity
@Table(name = "covenant_download_apply_record")
public class CovenantDownloadApplyRecord extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 借据id
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus applyStatus;
    /**
     * 下载类型: sftp,http等
     */
    @Enumerated(EnumType.STRING)
    private CovenantDownloadType downloadType;
    /**
     * 可下载时间
     */
    private LocalDateTime downloadTime;
    /**
     * 下载状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus downloadStatus;
    /**
     * 外部下载链接
     */
    private String downloadUrl;
    /**
     * ossBucket
     */
    private String ossBucket;
    /**
     * oss文件路径
     */
    private String ossFilePath;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }


    public ProcessStatus getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(ProcessStatus applyStatus) {
        this.applyStatus = applyStatus;
    }

    public CovenantDownloadType getDownloadType() {
        return downloadType;
    }

    public void setDownloadType(CovenantDownloadType downloadType) {
        this.downloadType = downloadType;
    }

    public LocalDateTime getDownloadTime() {
        return downloadTime;
    }

    public void setDownloadTime(LocalDateTime downloadTime) {
        this.downloadTime = downloadTime;
    }

    public ProcessStatus getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(ProcessStatus downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssFilePath() {
        return ossFilePath;
    }

    public void setOssFilePath(String ossFilePath) {
        this.ossFilePath = ossFilePath;
    }
}

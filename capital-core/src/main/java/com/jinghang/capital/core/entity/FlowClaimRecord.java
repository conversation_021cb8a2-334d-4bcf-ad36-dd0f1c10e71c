package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.ClaimResult;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.RepayPurpose;


import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代偿记录表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-10-17
 */
@Entity
@Table(name = "flow_claim_record")
public class FlowClaimRecord extends BaseEntity {

    /**
     * 借款id
     */
    private String loanId;
    /**
     * 外部还款id
     */
    private String outerClaimId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ClaimResult claimStatus;
    /**
     * 申请日期
     */
    private LocalDateTime applyTime;

    /**
     * 实还时间
     */
    private LocalDateTime actClaimTime;
    /**
     * 总金额
     */
    private BigDecimal totalAmt;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;
    /**
     * 咨询费
     */
    private BigDecimal consultAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;
    /**
     * 代偿模式
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose claimPurpose;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterClaimId() {
        return outerClaimId;
    }

    public void setOuterClaimId(String outerClaimId) {
        this.outerClaimId = outerClaimId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public ClaimResult getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(ClaimResult claimStatus) {
        this.claimStatus = claimStatus;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public LocalDateTime getActClaimTime() {
        return actClaimTime;
    }

    public void setActClaimTime(LocalDateTime actClaimTime) {
        this.actClaimTime = actClaimTime;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public RepayPurpose getClaimPurpose() {
        return claimPurpose;
    }

    public void setClaimPurpose(RepayPurpose claimPurpose) {
        this.claimPurpose = claimPurpose;
    }

    protected String prefix() {
        return "FCR";
    }
}

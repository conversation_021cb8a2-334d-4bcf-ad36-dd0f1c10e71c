package com.jinghang.capital.core.entity;

import com.jinghang.capital.core.enums.BankChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

/**
 * 循环额度用户表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-25
 */
@Entity
@Table(name = "quota_cycle_user_info")
public class QuotaCycleUserInfo extends BaseEntity {
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 用户身份证
     */
    private String userIdCard;
    /**
     * 授信ID
     */
    private String creditId;
    /**
     * 授信申请流水号
     */
    private String creditSeq;
    /**
     * 资方授信流水号
     */
    private String bankCreditSeq;
    /**
     * 资方授信编号
     */
    private String creditNo;
    /**
     * 资方客户编号
     */
    private String bankUserId;
    /**
     * 最新身份证有效期截至
     */
    private LocalDate certValidEnd;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getUserIdCard() {
        return userIdCard;
    }

    public void setUserIdCard(String userIdCard) {
        this.userIdCard = userIdCard;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCreditSeq() {
        return creditSeq;
    }

    public void setCreditSeq(String creditSeq) {
        this.creditSeq = creditSeq;
    }

    public String getBankCreditSeq() {
        return bankCreditSeq;
    }

    public void setBankCreditSeq(String bankCreditSeq) {
        this.bankCreditSeq = bankCreditSeq;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getBankUserId() {
        return bankUserId;
    }

    public void setBankUserId(String bankUserId) {
        this.bankUserId = bankUserId;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    protected String prefix() {
        return "QCU";
    }
}

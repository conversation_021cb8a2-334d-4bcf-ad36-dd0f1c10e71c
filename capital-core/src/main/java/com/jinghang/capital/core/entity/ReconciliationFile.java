package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileMode;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.enums.SyncState;
import com.jinghang.capital.core.vo.ProductVo;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Entity
@Table(name = "reconciliation_file")
public class ReconciliationFile extends BaseEntity {
    /**
     * 产品
     */
    @Enumerated(EnumType.STRING)
    private ProductVo product;

    /**
     * 资方
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 对账文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 同步状态
     */
    @Enumerated(EnumType.STRING)
    private SyncState syncState;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件日期
     */
    private LocalDate fileDate;
    /**
     * 文件输入地址
     */
    private String inputUrl;
    /**
     * 文件输出地址
     */
    private String outputUrl;
    /**
     * 文件交互方式
     */
    @Enumerated(EnumType.STRING)
    private FileMode mode;
    /**
     * 来源文件bucket
     */
    private String sourceOssBucket;
    /**
     * 来源文件key
     */
    private String sourceOssKey;
    /**
     * 来源文件oss
     */
    private String sourceOssUrl;
    /**
     * 对账状态
     */
    @Enumerated(EnumType.STRING)
    private ReccStateEnum reconciliationState;
    /**
     * 文件输出bucket
     */
    private String targetOssBucket;
    /**
     * 文件输出key
     */
    private String targetOssKey;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getInputUrl() {
        return inputUrl;
    }

    public void setInputUrl(String inputUrl) {
        this.inputUrl = inputUrl;
    }

    public String getOutputUrl() {
        return outputUrl;
    }

    public void setOutputUrl(String outputUrl) {
        this.outputUrl = outputUrl;
    }

    public String getSourceOssBucket() {
        return sourceOssBucket;
    }

    public void setSourceOssBucket(String sourceOssBucket) {
        this.sourceOssBucket = sourceOssBucket;
    }

    public String getSourceOssKey() {
        return sourceOssKey;
    }

    public void setSourceOssKey(String sourceOssKey) {
        this.sourceOssKey = sourceOssKey;
    }

    public String getSourceOssUrl() {
        return sourceOssUrl;
    }

    public void setSourceOssUrl(String sourceOssUrl) {
        this.sourceOssUrl = sourceOssUrl;
    }

    public ReccStateEnum getReconciliationState() {
        return reconciliationState;
    }

    public void setReconciliationState(ReccStateEnum reconciliationState) {
        this.reconciliationState = reconciliationState;
    }

    public String getTargetOssBucket() {
        return targetOssBucket;
    }

    public void setTargetOssBucket(String targetOssBucket) {
        this.targetOssBucket = targetOssBucket;
    }

    public String getTargetOssKey() {
        return targetOssKey;
    }

    public void setTargetOssKey(String targetOssKey) {
        this.targetOssKey = targetOssKey;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public SyncState getSyncState() {
        return syncState;
    }

    public void setSyncState(SyncState syncState) {
        this.syncState = syncState;
    }

    public FileMode getMode() {
        return mode;
    }

    public void setMode(FileMode mode) {
        this.mode = mode;
    }

    public ProductVo getProduct() {
        return product;
    }

    public void setProduct(ProductVo product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }


    @Override
    protected String prefix() {
        return "RF";
    }
}

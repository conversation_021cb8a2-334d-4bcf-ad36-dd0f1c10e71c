package com.jinghang.capital.core.exception;

public enum BizErrorCode {

    CHANNEL_NOT_FOUND("00001", "资方渠道不存在"),
    PARAM_EXCEPTION("00002", "参数异常"),
    SYSTEM_ERROR("00003", "系统异常，请联系管理员！"),
    CHANNEL_CONFIG_NOT_FOUND("00004", "资方配置不存在"),

    /// 业务系统校验授信申请是否成功到fin_core  core与msg信息不要随便改动 CREDIT_NOT_FOUND
    CREDIT_NOT_FOUND("10001", "授信不存在"),
    CREDIT_ID_INVALID("10002", "授信ID无效"),
    CREDIT_CONTACT_EXCEPTION("10003", "授信用户联系人不正确"),

    CREDIT_FLOW_NOT_FOUND("10004", "授信流程表不存在"),
    FILE_TYPE_NOT_FOUND("10005", "授信文件类型不存在"),
    CREDIT_INVALID("10006", "授信校验失败"),
    CREDIT_PRE_REFUSE("10007", "资方授信前筛拒绝"),
    CREDIT_AMOUNT_AVAILABLE_LIMIT("10008", "可用授信额度不足"),

    CREDIT_USER_PHONE_ERROR("10009", "授信手机号码不一致"),
    CREDIT_FAIL_IN_THIRTY_DAY("10010", "30天内授信失败"),
    CREDIT_FAIL_IN_FIFTEEN_DAY("10011", "15天内授信失败"),
    CREDIT_TIME_INVALID("10007", "授信时间不在有效期"),
    CREDIT_ID_DISTRICT_LIMIT("10008", "授信区域限制"),
    CREDIT_GPS_INVALID("10013", "授信gps有误"),
    CREDIT_AGE_LIMIT("10009", "授信年龄限制"),

    CREDIT_AMOUNT_LIMIT("10010", "授信金额限制"),
    CREDIT_PERIODS_LIMIT("10011", "授信期数限制"),
    CREDIT_CERT_LIMIT("10012", "授信身份证限制"),

    CREDIT_PHONE_INCONSISTENT("10013", "已授信的手机不一致"),
    CREDIT_ADJUST_FAIL("10014", "额度不足,授信金额限制"),

    BANK_CREDIT_USER_NOT_EXIST("10015", "资方用户授信记录不存在"),
    CREDIT_AMOUNT_LIMIT_INVALID("10016", "授信金额限制"),
    CREDIT_ADJUST_PROCESSING("10016", "调额处理中"),
    CREDIT_USER_LIMIT_ERROR("10017", "授信状态异常"),
    CREDIT_ADJUST_NOT_FOUND("10018", "调额记录不存在"),

    BANK_RESPONSE_RESULT_ERROR("10018", "请求资方响应结果异常"),
    RECREDIT_STATUS_ERROR("10019", "重新授信异常,当前授信状态不为成功"),
    CREDIT_NAME_ERROR("10020", "姓名格式不正确"),
    LOAN_NOT_FOUND("20001", "借款不存在"),
    LOAN_ID_INVALID("20002", "借款ID无效"),
    LOAN_HAS_EXIST("20003", "借款已存在"),
    LOAN_REPLAN_NOT_EXIST("20004", "借款计划不存在"),
    LOAN_REPLAN_HAS_EXIST("20005", "借款计划已存在"),
    LOAN_INVALID("20006", "借款校验失败"),
    LOAN_TIME_INVALID("20007", "借款时间不在有效期"),
    LOAN_AMOUNT_LIMIT("20008", "借款金额限制"),
    LOAN_PERIODS_LIMIT("20009", "借款期数限制"),
    USER_CREDIT_NOT_FOUND("20010", "用户未授信不得借款"),
    LOAN_REPLAN_COUNT_INCONSISTENT("20011", "还款计划条数与借款期数不一致"),

    UPLOAD_IMAGE("20013", "放款成功后文件上传失败"),

    ACCOUNT_NOT_FOUND("30001", "用户不存在"),
    BANK_PROTOCOL_NOT_FOUND("40001", "卡不存在"),
    BANK_CODE_NOT_SUPPORT("40002", "不支持的还款银行卡"),
    BANK_CODE_SIGN_APPLY("40003", "银行卡签约申请失败"),
    BANK_CODE_SIGN_VERIFY("40004", "银行卡签约验证失败"),
    BANK_CODE_SIGN_QUERY("40005", "银行卡签约查询失败"),
    PAY_CHANNEL_CODE_INVALID("40009", "不支持的支付渠道"),

    CONTRACT_TEMPLATE_NOT_FOUND("40003", "签章合同模板不存在"),
    CONTRACT_DOWNLOAD_ERROR("40004", "签章合同下载失败"),
    CONTRACT_UPLOAD_APPLY_ERROR("40005", "签章合同上传失败"),
    CONTRACT_UPLOAD_ERROR("40006", "签章合同上传失败"),
    SIGN_APPLY_ERROR("40007", "请求公共签章失败"),
    SIGN_PARAM_ERROR("40008", "获取签章参数失败"),

    METHOD_NOT_IMPLEMENTS("50000", "对账文件记录不存在"),

    REC_NOT_FOUND("50001", "对账文件记录不存在"),
    REC_CHECK_NUM_DIFFERENT("50012", "对账文件条目不一致"),
    REC_HAS_SUCCESS("50002", "对账记录已成功对账"),

    REC_MULTI_PROCESSING_FILE("50003", "对账记录存在多条处理中记录"),

    FILE_TYPE_FAILED("50010", "文件类型有误"),

    REPAY_INVALID("50004", "还款校验失败"),
    REPAY_TIME_INVALID("50005", "还款时间限制"),

    BANK_NOT_SUPPORT_ERROR("50010", "该资方不支持线上获取结清证明"),
    DOWNLOAD_SETTLE_FILE_BIZ_ERROR("50011", "下载结清证明失败"),

    REPAY_NOT_FOUND("60001", "还款记录不存在"),
    BANK_REPAY_NOT_FOUND("60002", "还款记录不存在"),
    REPAY_TRIAL_ERROR("60003", "还款试算出错"),
    REPAY_PERIOD_NOT_EXIST("60004", "还款期数未指定"),

    REPAY_TRIAL_ERROR_AFTER_CLAIM("60005", "代偿后无法结清试算"),
    REPAY_TRIAL_NO_NEED("60006", "还款试算不需要"),
    REPAY_TRIAL_CLEAR_PERIOD_ERROR("60007", "还款结清试算期数不正确"),

    REPAY_TRIAL_PERIOD_ERROR("60008", "还款试算期数不正确"),

    REPAY_TRIAL_CURRENT_ADVANCE_NOT_SUPPORTED("60002", "提前还当期不支持"),
    REPAY_TRIAL_OVERDUE_ADVANCE_NOT_SUPPORTED("60002", "逾期提前结清不支持"),
    REPAY_TRIAL_CLEAR_DAYS_LIMIT("60010", "上期银行尚未入账，不支持试算/还款"),
    REPAY_NOT_SUPPORT("61000", "暂不支持还款"),


    CUSTOM_UNPAID_PLAN_NOT_FOUND("60001", "对客计划已还"),

    JSON_PROCESSING_ERROR("70001", "JSON处理异常"),
    HTTP_REQUEST_ERROR("70002", "HTTP请求异常"),
    RSA_SIGN_ERROR("70003", "RSA签名异常"),
    SM4_ERROR("70004", "SM4加解密异常"),
    TEMP_FILE_CREATE_ERROR("70005", "临时文件创建异常"),

    FILE_DOWNLOAD_ERROR("70006", "临时文件创建异常"),
    VERIFY_SIGN_ERROR("70007", "验签异常"),


    CREDIT_NOT_YET_CLEAR_ERROR("70008", "借款订单还未结清"),
    REPAY_TYPE_ERROR("70009", "还款类型不是代偿"),
    AES_ERROR("70010", "AES加解密错误"),
    SDK_INIT_ERROR("70011", "SDK初始化异常"),
    FILE_GENERATION_FAILED("70012", "文件生成失败"),
    FILE_UPLOAD_ERROR("70013", "文件上传至sftp失败"),
    GET_TRUST_ERROR("70014", "获取信托计划失败"),
    APPLY_TRUST_ERROR("70015", "申请信托计划额度失败"),
    REPAYMENT_ALREADY_EXISTS("70017", "还款申请已存在"),
    REPAYMENT_NOT_ALLOWED("70018", "放款日当天不允许还款"),
    LOAN_ALREADY_EXISTS("70019", "放款申请已存在"),
    LOAN_RESULT_QUERY_ERROR("70020", "放款结果查询异常"),
    FUND_QUERY_ERROR("70021", "资金流水查询失败"),
    FIN_EXISTS("70022", "流水不存在"),
    RESPONSE_CODE_UNKNOWN_ERROR("70024", "资方响应异常"),
    TRUST_PLAN_EXISTS("70023", "信托计划不存在"),
    GUARANTEE_COMPANY_NOT_SUPPORT("70025", "不支持当前担保公司"),
    DEFRAY_APPLY_ERROR("70025", "代付申请异常"),

    ONLINE_REPAYMENT_CHECK_ERROR("70031", "线上还款是否支付代扣为是时，检验必填项不通过。请检查！"),
    SUCH_INVALID_SIGN_ERROR("70032", "请求蚂蚁异常，密钥签名问题"),
    SETTLEMENT_CERTIFICATE_QUERY_ERROR("70033", "结清证明查询异常"),
    CHECK_YEAR_PERIOD_ERROR("70034", "当前时间属于年结期间不允许还款的时间，请次日再重试。"),
    CHECK_REPAY_DARK_PERIOD_ERROR("70035", "当前时间属于还款黑暗期(22:30-4:00)不允许还款，请稍后重试。"),
    BANK_CODE_IS_NULL_ERROR("70036", "还款时，还款银行编码为空。请检查！"),

    REQUEST_INTERFACE_ERROR("70010", "请求接口异常"),
    PROTOCOL_SHARE_ERROR("70012", "协议共享失败"),

    CERT_EXPIRE_YES_ERROR("70027", "身份证有效期限制"),
    CERT_EXPIRE_DAYS_ERROR("70026", "身份证有效期小于限制天数"),
    SHOP_NOT_FOUND("70027", "商户信息不存在"),

    CREDIT_PHONE_PREFIX_ERROR("10009", "授信手机号前缀限制"),
    PAYMENT_CHANNEL_NOT_SUPPORT("10010", "当前支付渠道不支持"),
    CREDIT_NATION_ERROR("10009", "授信民族限制");

    BizErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private final String code;

    private final String msg;


    public String getCode() {
        return code;
    }


    public String getMsg() {
        return msg;
    }


}

package com.jinghang.capital.core.exception;

import java.io.Serial;

public class BizException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 5300995145669519373L;

    public BizException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public BizException(BizErrorCode errorCode) {
        super(errorCode.getMsg());
        this.code = errorCode.getCode();
    }


    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


}

package com.jinghang.capital.core.exception;


import com.jinghang.common.sys.Sys;

public final class FinCoreSys implements Sys {

    public static final FinCoreSys INSTANCE = new FinCoreSys("10", "资金接入");

    private final String code;

    private final String name;

    private FinCoreSys(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}

package com.jinghang.capital.core.exception;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理校验的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public RestResult exceptionHandler(HttpServletRequest req, BindException e) {
        logger.error("发生校验异常！原因是:", e);
        String mag = e.getBindingResult().getAllErrors().stream()
            .map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(";"));
        return RestResult.fail(ResultCode.PARAM_ILLEGAL, mag);
    }

    /**
     * 处理空指针的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = NullPointerException.class)
    @ResponseBody
    public RestResult exceptionHandler(HttpServletRequest req, NullPointerException e) {
        logger.error("发生空指针异常！原因是:", e);
        return RestResult.fail(ResultCode.BIZ_ERROR, e.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public RestResult<Void> handException(Exception e) {
        logger.error("未知异常", e);
        return RestResult.fail(ResultCode.SYS_ERROR);
    }

    @ExceptionHandler(value = BizException.class)
    public RestResult<Void> handBizException(BizException e) {
        logger.error("业务异常", e);
        return RestResult.fail(ResultCode.BIZ_ERROR, e.getMessage());
    }


}

package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.AgreementSignAgain;
import com.jinghang.capital.core.enums.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.List;


public interface AgreementSignAgainRepository extends JpaRepository<AgreementSignAgain, String> {

    List<AgreementSignAgain> findBySignStatusAndProcessDate(ProcessStatus signStatus, LocalDate processDate);

}

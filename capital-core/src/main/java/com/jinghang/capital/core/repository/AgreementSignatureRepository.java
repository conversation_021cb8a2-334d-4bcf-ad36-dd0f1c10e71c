package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface AgreementSignatureRepository extends JpaRepository<AgreementSignature, String> {


    @Query(value = "select l from AgreementSignature l where l.businessId = ?1 and l.channel = ?2 and l.loanStage = ?3 and l.fileType in ?4")
    List<AgreementSignature> findByChannelAndLoanStageAndFileTypeList(String businessId, BankChannel channel, LoanStage loanStage, List<FileType> fileTypeList);

    AgreementSignature findByBusinessIdAndChannelAndLoanStageAndFileType(String businessId, BankChannel channel, LoanStage loanStage, FileType fileType);

    /**
     * 查询同一订单下，某些状态的签章记录
     *
     * @param loanId
     * @param states
     * @return
     */
    List<AgreementSignature> findByBusinessIdAndSignStateIn(String loanId, List<ProcessStatus> states);

    List<AgreementSignature> findByBusinessIdAndFileType(String businessId, FileType fileType);

    List<AgreementSignature> findByBusinessId(String businessId);

}

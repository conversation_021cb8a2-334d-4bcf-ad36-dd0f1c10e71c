package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.AreaCode;
import org.springframework.data.jpa.repository.JpaRepository;

public interface AreaCodeRepository extends JpaRepository<AreaCode, String> {
    AreaCode findByNameLike(String name);
    AreaCode findTopByNameAndParentCode(String name, String parentCode);
    AreaCode findTopByNameLikeAndParentCode(String name, String parentCode);
}

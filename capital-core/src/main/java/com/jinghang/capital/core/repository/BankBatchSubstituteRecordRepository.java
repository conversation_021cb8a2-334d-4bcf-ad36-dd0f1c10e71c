package com.jinghang.capital.core.repository;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.entity.BankBatchSubstituteRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.PushStatus;

import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface BankBatchSubstituteRecordRepository extends JpaRepository<BankBatchSubstituteRecord, String> {

    Optional<BankBatchSubstituteRecord> findByLoanIdAndPeriod(String loanId, Integer period);

    List<BankBatchSubstituteRecord> findByChannelAndGuaranteeCompanyAndPushDateAndPushStatus(BankChannel channel,
                                                                                             GuaranteeCompany guaranteeCompany,
                                                                                             LocalDate pushDate,
                                                                                             PushStatus pushStatus);
    int countByLoanIdAndSubstituteStatusIn(String loanId, List<ProcessStatus> statuses);
}

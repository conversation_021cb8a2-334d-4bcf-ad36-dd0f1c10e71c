package com.jinghang.capital.core.repository;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.entity.BankConfig;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.WhetherState;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface BankConfigRepository extends JpaRepository<BankConfig, String> {

    Optional<BankConfig> findByChannelAndGuaranteeCompanyAndEnabled(BankChannel channel, GuaranteeCompany guaranteeCompany, WhetherState enabled);

}

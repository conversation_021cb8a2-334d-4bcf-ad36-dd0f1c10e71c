package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BankLoanReplanRepository extends JpaRepository<BankLoanReplan, String> {
    Optional<BankLoanReplan> findByLoanIdAndPeriod(String loanId, Integer period);

    List<BankLoanReplan> findByLoanId(String loanId);


    List<BankLoanReplan> findByLoanIdAndRepayStatusAndRepayType(String loanId, RepayStatus repayStatus, RepayType repayType);

    boolean existsByLoanIdAndPeriodAndRepayType(String loanId, Integer period, RepayType repayType);

    boolean existsByLoanIdAndPeriodAndRepayTypeIn(String loanId, Integer period, List<RepayType> repayTypes);

    boolean existsByLoanIdAndRepayType(String loanId, RepayType repayType);

    boolean existsByLoanIdAndRepayTypeAndRepayPurpose(String loanId, RepayType repayType, RepayPurpose repayPurpose);

    /**
     * 查询资方还款结清-按计划还清
     * return  放款id
     */
    @Query("select distinct l.id from Loan l join BankLoanReplan blr on l.periods = blr.period and l.id = blr.loanId"
        + " where  l.channel = ?1 and blr.repayStatus = 'REPAID'"
        + " and blr.repayType = ?2 and blr.actRepayTime between ?3 and ?4")
    List<String> findNormalClear(BankChannel channel, RepayType repayType, LocalDateTime actRepayTimeStart, LocalDateTime actRepayTimeEnd);


    List<BankLoanReplan> findByChannelAndRepayTypeAndRepayPurposeAndActRepayTimeBetween(BankChannel channel, RepayType repayType, RepayPurpose repayPurpose,
                                                                                        LocalDateTime actRepayTimeStart,
                                                                                        LocalDateTime actRepayTimeEnd);

    /**
     * 查询资方还款结清-提前结清
     *
     * @param channel
     * @param repayType
     * @param actRepayTimeStart
     * @param actRepayTimeEnd
     * @return
     */
    @Query("select distinct blr.loanId from BankLoanReplan blr "
        + " where  blr.channel = ?1 "
        + " and blr.repayStatus = 'REPAID' "
        + " and blr.repayPurpose = 'CLEAR'"
        + " and blr.repayType = ?2 "
        + " and blr.actRepayTime between ?3 and ?4")
    List<String> findAdvancedClear(BankChannel channel, RepayType repayType, LocalDateTime actRepayTimeStart, LocalDateTime actRepayTimeEnd);

    /**
     * 查询资方还款结清-提前结清(包含一期一期还完结清的)
     *
     * @param channel
     * @param repayType
     * @param actRepayTimeStart
     * @param actRepayTimeEnd
     * @return
     */
    @Query("select distinct blr.loanId from BankLoanReplan blr "
        + " where  blr.channel = ?1 "
        + " and blr.repayStatus = 'REPAID' "
        + "and blr.repayPurpose = 'CLEAR'"
        + " and blr.repayType = ?2 "
        + " and blr.actRepayTime between ?3 and ?4")
    List<String> findClearPlan(BankChannel channel, RepayType repayType, LocalDateTime actRepayTimeStart, LocalDateTime actRepayTimeEnd);

    /**
     * 查询还款时间在指定时间范围内 对资代偿的对资实还计划
     *
     * @param channel
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select b from BankLoanReplan b where b.repayType = 'CLAIM' and b.channel = ?1 and b.actRepayTime >= ?2 and b.actRepayTime < ?3")
    List<BankLoanReplan> findClaimBankLoanReplans(BankChannel channel, LocalDateTime dayStart, LocalDateTime nextDayStart);

    /**
     * 查询还款时间在指定时间范围内 对资代偿的对资实还计划（仅代偿，不包含回购）
     *
     * @param channel
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select b from BankLoanReplan b where b.repayType = 'CLAIM' and b.repayPurpose = 'CURRENT' "
        + "and b.channel = ?1 and b.actRepayTime >= ?2 and b.actRepayTime < ?3")
    List<BankLoanReplan> findCurrentClaimBankPlans(BankChannel channel, LocalDateTime dayStart, LocalDateTime nextDayStart);

    /**
     * 查询还款时间在指定时间范围内 对资代偿的对资实还计划（仅回购）
     *
     * @param channel
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select b from BankLoanReplan b where b.repayType = 'CLAIM' and b.repayPurpose = 'CLEAR' "
        + "and b.channel = ?1 and b.actRepayTime >= ?2 and b.actRepayTime < ?3")
    List<BankLoanReplan> findClearClaimBankPlans(BankChannel channel, LocalDateTime dayStart, LocalDateTime nextDayStart);


    List<BankLoanReplan> findByRepayStatusAndChannelAndActRepayTimeIsGreaterThanEqualAndActRepayTimeIsLessThan(RepayStatus repayStatus, BankChannel bankChannel,
                                                                                                               LocalDateTime startDate, LocalDateTime endDate);

    List<BankLoanReplan> findByIdIn(List<String> ids);

    List<BankLoanReplan> findListByChannelAndRepayStatusAndRepayTypeAndActRepayTimeIsGreaterThanEqualAndActRepayTimeIsLessThan(BankChannel bankChannel,
                                                                                                                               RepayStatus repayStatus,
                                                                                                                               RepayType repayType,
                                                                                                                               LocalDateTime startOfDay,
                                                                                                                               LocalDateTime nextDayStart);
    List<BankLoanReplan> findByLoanIdAndRepayStatusAndRepayTypeAndPeriodLessThan(String loanId, RepayStatus repayStatus, RepayType repayType, Integer period);
}

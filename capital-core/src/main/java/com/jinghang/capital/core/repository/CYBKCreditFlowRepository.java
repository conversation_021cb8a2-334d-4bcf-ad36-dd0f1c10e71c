package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.CYBKCreditFlow;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR> gale
 * @Classname CycfcCreditFlowRepository
 * @Description 长银直连 授信流水
 * @Date 2024/5/28 19:56
 */
public interface CYBKCreditFlowRepository extends JpaRepository<CYBKCreditFlow, String> {

    Optional<CYBKCreditFlow> findByCreditId(String creditId);

    Optional<CYBKCreditFlow> findByCreditNo(String creditNo);

}

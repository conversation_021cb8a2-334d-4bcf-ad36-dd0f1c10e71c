package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.CYBKReccClaim;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CYBKReccClaimRepository extends JpaRepository<CYBKReccClaim, String> {

    List<CYBKReccClaim> findByReccId(String reccId);

    @Transactional
    @Modifying
    @Query("update CYBKReccClaim set reccStatus = ?1 where id = ?2")
    void updateReccStatus(String reccStatus, String id);

}

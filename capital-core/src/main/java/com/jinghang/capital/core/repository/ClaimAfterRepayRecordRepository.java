package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.ClaimAfterRepayRecord;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ClaimAfterRepayRecordRepository extends JpaRepository<ClaimAfterRepayRecord, String> {

    ClaimAfterRepayRecord findByLoanIdAndPeriodAndIsSubstituteNotifyAndNotifyStatus(String loanId,
                                                                                    Integer period,
                                                                                    WhetherState isSubstituteNotify,
                                                                                    ProcessStatus notifyStatus);

    ClaimAfterRepayRecord findByLoanIdAndPeriodAndAndNotifyStatus(String loanId,
                                                                  Integer period,
                                                                  ProcessStatus notifyStatus);

    boolean existsByLoanIdAndPeriodAndIsSubstituteNotifyAndNotifyStatusIn(String loanId,
                                                                          Integer period,
                                                                          WhetherState isSubstituteNotify,
                                                                          List<ProcessStatus> notifyStatus);

    boolean existsByLoanIdAndPeriodAndNotifyStatus(String loanId, Integer period, ProcessStatus notifyStatus);

    boolean existsByLoanIdAndPeriodAndNotifyStatusIn(String loanId, Integer period, List<ProcessStatus> notifyStatus);
}

package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.enums.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ClaimRecordRepository extends JpaRepository<FlowClaimRecord, String> {

    FlowClaimRecord findByOuterClaimId(String outerClaimId);

    FlowClaimRecord findByLoanIdAndPeriodAndClaimStatus(String outerClaimId, Integer period, ProcessStatus claimStatus);
}

package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.CreditStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CreditRepository extends JpaRepository<Credit, String> {

    Credit findCreditById(String id);

    List<Credit> findByIdIn(List<String> ids);

    Credit findCreditByOuterCreditIdAndChannel(String outerCreditId, BankChannel channel);

    Optional<Credit> findCreditByOuterCreditId(String outerCreditId);

    @Query("select c from Credit c where c.channel = ?1 and  c.creditStatus = ?2 and c.applyTime >= ?3 and c.applyTime < ?4 ")
    List<Credit> findCreditListByApplyTimeAndStatus(BankChannel channel, CreditStatus status,
                                                    LocalDateTime creditApplyDayStart, LocalDateTime nextDayStart);

    @Query("select c from Credit c where c.channel = ?1 and  c.creditStatus = ?2 and c.custCertNo = ?3 and c.applyTime >= ?4 ")
    List<Credit> queryThirtyDayCreditFailRecord(BankChannel channel, CreditStatus status,
                                                String custCertNo, LocalDateTime creditFailDate);

    @Query(value = "select * from credit where cust_cert_no = ?1 and credit_status = ?2 and channel = ?3 and credit_type=?4 order by created_time desc limit 1",
        nativeQuery = true)
    Optional<Credit> getValidCredit(String idCard, String status, String bankChannel, String creditType);

    @Query(value = "select * from credit where cust_cert_no = ?1 and credit_status = ?2 and channel = ?3 order by created_time desc limit 1",
        nativeQuery = true)
    Optional<Credit> getValidCredit(String idCard, String status, String bankChannel);

    List<Credit> findAllByChannelAndCustCertNoOrderByCreatedTimeDesc(BankChannel bankChannel, String custCertNo);

    Credit findByAccountId(String accountId);

    Credit findCreditByAccountIdAndCustCertNo(String acountId, String custCertNo);

    /**
     * 查询授信申请时间在指定时间范围的授信记录列表（所有授信状态）
     *
     * @param channel
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query("select c from Credit c where c.channel = ?1 and c.applyTime >= ?2 and c.applyTime < ?3 ")
    List<Credit> findCreditsByChannelAndApplyTime(BankChannel channel, LocalDateTime dayStart, LocalDateTime nextDayStart);

    Credit findCreditByCreditNo(String creditNo);

    long countByChannelAndCustCertNoAndCreditStatusIn(BankChannel bankChannel, String certNo, List<CreditStatus> creditStatusList);
}

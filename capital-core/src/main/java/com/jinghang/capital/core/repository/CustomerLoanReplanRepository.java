package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.CustomerLoanReplan;
import com.jinghang.capital.core.entity.LoanReplan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CustomerLoanReplanRepository extends JpaRepository<CustomerLoanReplan, String> {

    Optional<CustomerLoanReplan> findByLoanIdAndPeriod(String loanId, Integer period);

    List<CustomerLoanReplan> findByLoanId(String loanId);

    /**
     * 查询还款时间在指定时间之前 对客实还计划
     *
     * @param channel
     * @param loanId
     * @param dayStart
     * @return
     */
    List<CustomerLoanReplan> findByChannelAndLoanIdAndActRepayTimeBefore(String channel, String loanId, LocalDateTime dayStart);

    List<CustomerLoanReplan> findByLoanIdAndActRepayTimeBefore(String loanId, LocalDateTime dayStart);

    @Query("select l from CustomerLoanReplan l where l.channel = ?1 and l.createdTime >= ?2 and l.createdTime < ?3")
    List<CustomerLoanReplan> findByChannelAndCreateDate(String channel, LocalDateTime startOfDay, LocalDateTime nextDayStart);
}

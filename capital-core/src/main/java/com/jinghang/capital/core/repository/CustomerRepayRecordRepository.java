package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CustomerRepayRecordRepository extends JpaRepository<CustomerRepayRecord, String> {
    Optional<CustomerRepayRecord> findByOuterRepayId(String outerRepayId);

    Optional<CustomerRepayRecord> findByLoanIdAndPeriodAndRepayStatus(String loanId, Integer peroid, ProcessStatus repayStatus);

    Optional<CustomerRepayRecord> findByLoanIdAndPeriodAndRepayStatusIn(String loanId, Integer peroid, List<ProcessStatus> repayStatus);

    List<CustomerRepayRecord> findListByLoanIdAndPeriodAndRepayStatus(String loanId, Integer peroid, ProcessStatus repayStatus);


    int countByLoanIdAndRepayStatusIn(String loanId, List<ProcessStatus> repayStatus);

    List<CustomerRepayRecord> findByLoanIdAndRepayStatus(String loanId, ProcessStatus repayStatus);

    List<CustomerRepayRecord> findByLoanIdAndPeriod(String loanId, Integer period);

    // 查询追偿还款记录，对客还款记录（已经代偿的对资还款，且对客已还）
    @Query(value = "select c from CustomerRepayRecord c left join BankRepayRecord b on c.loanId = b.loanId and c.period = b.period "
            + "where c.channel = ?1 and c.repayTime >= ?2 and c.repayTime <= ?3 and c.repayStatus = ?4 and b.repayType = 'CLAIM'")
    List<CustomerRepayRecord> findRecoveryCustomerRepayRecords(
            BankChannel channel,
            LocalDateTime dayStart,
            LocalDateTime nextDayStart,
            ProcessStatus repayStatus);


    @Query(value = "select c from CustomerRepayRecord c join Loan l on c.loanId = l.id "
            + " where l.channel = ?1 and l.flowChannel = ?2 and c.repayStatus = ?3 "
            + " and c.repayTime >= ?4 and c.repayTime <= ?5 ")
    List<CustomerRepayRecord> findCustomerRepayRecords(BankChannel channel, FlowChannel flowChannel, ProcessStatus repayStatus,
                                                       LocalDateTime dayStart, LocalDateTime nextDayStart);

    @Query(value = "select c from CustomerRepayRecord c join Loan l on c.loanId = l.id "
            + " where l.channel = ?1  and c.repayStatus = ?2 "
            + " and c.repayTime >= ?3 and c.repayTime <= ?4 ")
    List<CustomerRepayRecord> findCustomerRepayRecordsLyx(BankChannel channel, ProcessStatus repayStatus,
                                                          LocalDateTime dayStart, LocalDateTime nextDayStart);


    /**
     * 长银直连 还款成功记录
     *
     * @param channel
     * @param repayStatus
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = " select crr from CustomerRepayRecord crr "
            + " where crr.channel = ?1 and crr.repayStatus = ?2 and crr.repayMode = ?3 and crr.repayTime >= ?4 and crr.repayTime < ?5 ")
    List<CustomerRepayRecord> findCustRepaysOffline(BankChannel channel, ProcessStatus repayStatus, RepayMode repayMode,
                                                    LocalDateTime dayStart, LocalDateTime nextDayStart);

    /**
     * 长银直连 还款成功记录
     *
     * @param channel
     * @param repayStatus
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = " select crr from CustomerRepayRecord crr "
            + " where crr.channel = ?1 and crr.repayStatus = ?2 and crr.repayTime >= ?3 and crr.repayTime < ?4 ")
    List<CustomerRepayRecord> findCustReccRepay(BankChannel channel, ProcessStatus repayStatus, LocalDateTime dayStart, LocalDateTime nextDayStart);

    /**
     * 查询代偿后 还款成功数据
     *
     * @param channel
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select c from CustomerRepayRecord c left join BankLoanReplan b on c.loanId = b.loanId and c.period = b.period"
            + " where c.channel = ?1 and c.repayTime >= ?2 and c.repayTime < ?3 and c.repayStatus = 'SUCCESS' and b.repayType = 'CLAIM'")
    List<CustomerRepayRecord> findClaimAfterRepaySuccess(BankChannel channel, LocalDateTime dayStart, LocalDateTime nextDayStart);


    // 查询还款记录，对客还款记录（代偿前的对资还款，且对客已还）
    @Query(value = "select c from CustomerRepayRecord c left join BankRepayRecord b on c.loanId = b.loanId and c.period = b.period "
            + "where c.channel = ?1 and c.repayTime >= ?2 and c.repayTime <= ?3 and c.repayStatus = ?4 and c.repayMode = 'OFFLINE' and b.repayType != 'CLAIM'")
    List<CustomerRepayRecord> findOfflineCustomerRepayRecords(BankChannel channel,
                                                              LocalDateTime dayStart,
                                                              LocalDateTime nextDayStart,
                                                              ProcessStatus repayStatus);


    List<CustomerRepayRecord> findListByChannelAndRepayTypeAndRepayModeAndRepayStatusAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(
            BankChannel channel,
            RepayType repayType,
            RepayMode repayMode,
            ProcessStatus processStatus,
            LocalDateTime startOfDay,
            LocalDateTime nextDayStart);

    List<CustomerRepayRecord> findByIdIn(List<String> relatedIds);

    List<CustomerRepayRecord> findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(
            ProcessStatus processStatus,
            BankChannel channel,
            LocalDateTime startOfDay,
            LocalDateTime nextDayStart
    );


    List<CustomerRepayRecord> findListByOuterRepayIdOrderByPeriodAsc(String outerRepayId);


    List<CustomerRepayRecord> findByLoanIdInAndRepayStatus(List<String> list, ProcessStatus processStatus);
}

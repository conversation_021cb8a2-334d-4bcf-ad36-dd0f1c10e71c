package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.DownloadFileLog;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.DownloadFileStatusEnum;
import com.jinghang.capital.core.enums.FileType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface DownloadFileLogRepository extends JpaRepository<DownloadFileLog, String> {

    @Query("select l from DownloadFileLog l where l.bankChannel = ?1 and l.status = ?2 and l.createTime >= ?3")
    List<DownloadFileLog> findByBankChannelAndStatusAndCreateTime(BankChannel bankChannel, DownloadFileStatusEnum status, LocalDateTime startDate);

    @Query("select l from DownloadFileLog l where l.bankChannel = ?1 and l.fileType = ?2 and l.createTime between ?3 and ?4")
    List<DownloadFileLog> findByFileType(BankChannel bankChannel, FileType fileType, LocalDateTime startDate, LocalDateTime endDate);

    @Query("select l from DownloadFileLog l where l.bankChannel = ?1 and l.fileType = ?2 and l.status = ?3 and l.createTime between ?4 and ?5")
    List<DownloadFileLog> findByFileTypeAndStatus(BankChannel bankChannel, FileType fileType, DownloadFileStatusEnum status, LocalDateTime startDate,
                                                  LocalDateTime endDate);

    DownloadFileLog findFirstByBizIdAndFileTypeOrderByCreateTimeDesc(String bizId, FileType fileType);

    DownloadFileLog findFirstByBizIdAndFileTypeAndStatusOrderByCreateTimeDesc(String bizId, FileType fileType, DownloadFileStatusEnum status);

    /**
     * 查询指定单号、指定文件类型的下载记录
     * 使用此方法需确保指定单号、指定文件类型的下载记录最多只有一条
     *
     * @param bizId    指定关联ID
     * @param fileType 指定文件类型
     * @return
     */
    Optional<DownloadFileLog> findByBizIdAndFileType(String bizId, FileType fileType);
}

package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.FailedLoanFile;
import com.jinghang.capital.core.enums.FileType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FailedLoanFileRepository extends JpaRepository<FailedLoanFile, String> {
    List<FailedLoanFile> findByCreditId(String creditId);



    @Query(value = "select l from FailedLoanFile l where l.creditId = ?1 and l.stage = ?2 and l.fileType in ?3")
    List<FailedLoanFile> findByCreditIdAndFileTypeList(String creditId, String stage, List<FileType> fileTypeList);



}

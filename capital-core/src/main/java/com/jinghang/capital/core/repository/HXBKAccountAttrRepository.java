package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.HXBKAccountAttr;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.Optional;

/**
 * HXBK账户属性Repository
 * @Author: System
 * @CreateTime: 2025/7/9
 */
public interface HXBKAccountAttrRepository extends JpaRepository<HXBKAccountAttr, String> {

    /**
     * 根据身份证号查找账户属性
     * @param certNo 身份证号
     * @return 账户属性
     */
    Optional<HXBKAccountAttr> findByCertNo(String certNo);

    /**
     * 根据openId查找账户属性（实际上openId就是主键id）
     * @param openId openId
     * @return 账户属性
     */
    Optional<HXBKAccountAttr> findByOpenId(String openId);

    /**
     * 根据姓名和身份证号查找账户属性
     * @param name 姓名
     * @param certNo 身份证号
     * @return 账户属性
     */
    Optional<HXBKAccountAttr> findByNameAndCertNo(String name, String certNo);
}

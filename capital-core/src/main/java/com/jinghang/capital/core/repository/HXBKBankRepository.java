package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.HXBKBankList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface HXBKBankRepository extends JpaRepository<HXBKBankList, Integer> {

    /**
     * 湖消直连通过银行编码获取银行名称
     * @param bankCode 银行编码
     * @return 银行信息
     */
    HXBKBankList findBankByBankCode(String bankCode);

    /**
     * 湖消直连通过还款银行名称模糊查询湖消的银行信息
     * @param bankName 银行名称
     * @return 银行信息
     */
    @Query("select b from HXBKBankList b where b.bankName like CONCAT('%', ?1, '%')")
    HXBKBankList findBankByBankName(String bankName);

    // 通过bankName精确匹配查询
    List<HXBKBankList> findByBankName(String bankName);

    List<HXBKBankList> findByBankCode(String bankCode);

    // 通过bankName模糊查询并忽略大小写
    List<HXBKBankList> findByBankNameContainingIgnoreCase(String bankName);
}

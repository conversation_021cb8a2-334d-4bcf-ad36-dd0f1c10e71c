package com.jinghang.capital.core.repository;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.RepayStatus;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface LoanReplanRepository extends JpaRepository<LoanReplan, String> {

    List<LoanReplan> findByLoanIdOrderByPeriod(String loanId);

    List<LoanReplan> findByLoanIdAndCustRepayStatusOrderByPeriod(String loanId, RepayStatus repayStatus);

    List<LoanReplan> findByLoanIdAndBankRepayStatusOrderByPeriod(String loanId, RepayStatus repayStatus);

    List<LoanReplan> findByRepayDateAndBankRepayStatusAndChannel(LocalDate repayDate, RepayStatus bankRepayStatus, String channel);

    Optional<LoanReplan> findByLoanIdAndPeriod(String loanId, Integer period);

    List<LoanReplan> findByCustRepayStatusAndLoanIdIn(RepayStatus repayStatus, List<String> loanIds);

    List<LoanReplan> findByChannelAndRepayDateBeforeAndCustRepayStatus(String bankChannel, LocalDate localDate, RepayStatus repayStatus,
                                                                       PageRequest pageRequest);


    /**
     * 长银直连 放款成功
     *
     * @param channel
     * @param status
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select "
        + "     lr.loan_id loanId, lr.period, 0 overdueDays, "
        + "     lr.repay_date repayDate,lr.interest_amt interestAmt,lr.principal_amt principalAmt,lr.cust_repay_status custRepayStatus, "
        + "     lr.bank_repay_status bankRepayStatus,lr.penalty_amt penaltyAmt,lr.guarantee_amt guaranteeAmt,l.guarantee_contract_no as guaranteeContractNo, "
        + "     l.loan_time loanTime,l.credit_id creditId,l.periods,l.loan_amt loanAmt,l.account_id as accountId "
        + " from loan_replan lr left join loan l on l.id = lr.loan_id "
        + " where l.channel = ?1 and l.loan_status = ?2 and l.loan_time >= ?3 and l.loan_time < ?4", nativeQuery = true)
    List<CYBKLoanReplanDTO> findCYBKLoanReplanByChannelAndLoanStatusAndLoanTime(
        String channel, String status, LocalDateTime dayStart, LocalDateTime nextDayStart);



    /**
     * 长银直连
     * 对客日终文件数据
     * 放款、还款、到期结息、转逾期、调整逾期分类
     *
     * @param channel
     * @param reccDay
     * @return
     */
    @Query(value = "select "
        // 放款成功
        + "          lr.loan_id loanId, lr.period, 0 overdueDays, "
        + "          lr.repay_date repayDate,lr.cust_repay_status custRepayStatus, "
        + "          lr.bank_repay_status bankRepayStatus, "
        + "          0 interestAmt,lr.principal_amt principalAmt,0 penaltyAmt,lr.guarantee_amt guaranteeAmt, "
        + "          l.loan_time loanTime,l.credit_id creditId,l.periods,l.loan_amt loanAmt,l.account_id as accountId, "
        + "         l.guarantee_contract_no as guaranteeContractNo "
        + "      from loan_replan lr left join loan l on l.id = lr.loan_id "
        + "      where l.channel = ?1 and l.loan_status = 'SUCCESS' and l.loan_time >= ?3 and l.loan_time < ?4 "
        + " union "
        // 到期结息
        + " select "
        + "          lr.loan_id loanId, lr.period, 0 overdueDays, "
        + "          lr.repay_date repayDate,lr.cust_repay_status custRepayStatus, "
        + "          lr.bank_repay_status bankRepayStatus, "
        + "          lr.interest_amt interestAmt,lr.principal_amt principalAmt,0 penaltyAmt,lr.guarantee_amt guaranteeAmt, "
        + "          l.loan_time loanTime,l.credit_id creditId,l.periods,l.loan_amt loanAmt,l.account_id as accountId, "
        + "         l.guarantee_contract_no as guaranteeContractNo "
        + " from loan_replan lr "
        + "         left join loan l on l.id = lr.loan_id "
        + " where l.channel = ?1 and lr.repay_date = ?2 and lr.cust_repay_status = 'NORMAL' "
        + " union "
        //  发生还款
        + " select "
        + "          lr.loan_id loanId, lr.period, datediff(?2, lr.repay_date) overdueDays, "
        + "          lr.repay_date repayDate,lr.cust_repay_status custRepayStatus, "
        + "          lr.bank_repay_status bankRepayStatus, "
        + "          crr.interest_amt interestAmt,crr.principal_amt principalAmt,crr.penalty_amt penaltyAmt,crr.guarantee_amt guaranteeAmt, "
        + "          l.loan_time loanTime,l.credit_id creditId,l.periods,l.loan_amt loanAmt,l.account_id as accountId, "
        + "         l.guarantee_contract_no as guaranteeContractNo "
        + "       from fin_service.customer_repay_record crr "
        + "        left join fin_service.loan_replan lr on lr.loan_id = crr.loan_id and lr.period = crr.period "
        + "        left join fin_service.loan l on l.id = lr.loan_id "
        + "       where crr.channel = ?1 and crr.repay_status='SUCCESS' and crr.repay_time >= ?3 and crr.repay_time < ?4 "
        + " union "
        // 逾期1天、31天
        + " select "
        + "          lr.loan_id loanId, lr.period, datediff(?2, lr.repay_date) overdueDays, "
        + "          lr.repay_date repayDate,lr.cust_repay_status custRepayStatus, "
        + "          lr.bank_repay_status bankRepayStatus, "
        + "          lr.interest_amt interestAmt,lr.principal_amt principalAmt,lr.penalty_amt penaltyAmt,lr.guarantee_amt guaranteeAmt, "
        + "          l.loan_time loanTime,l.credit_id creditId,l.periods,l.loan_amt loanAmt,l.account_id as accountId, "
        + "         l.guarantee_contract_no as guaranteeContractNo "
        + "       from fin_service.loan_replan lr "
        + "       inner join fin_service.loan l on l.id = lr.loan_id "
        + "       where lr.channel = ?1 and (lr.repay_date = DATE_ADD(?2, INTERVAL -1 DAY) or lr.repay_date = DATE_ADD(?2, INTERVAL -31 DAY))",
        nativeQuery = true)
    List<CYBKLoanReplanDTO> findCYBKLoanPlanDailyHasChanged(String channel, LocalDate reccDay, LocalDateTime dayStart, LocalDateTime nextDayStart);


    List<LoanReplan> findByRepayDateAndCustRepayStatusAndBankRepayStatusAndChannel(LocalDate repayDate, RepayStatus bankRepayStatus,
                                                                                   RepayStatus custRepayStatus, String channel);


    @Query("select l from LoanReplan l left join Loan lo on l.loanId = lo.id  "
        + "where l.bankRepayStatus = ?1 and l.repayDate = ?2 and l.channel = ?3 and lo.guaranteeCompany = ?4")
    List<LoanReplan> findByBankRepayStatusAndRepayDateAndChannel(RepayStatus bankRepayStatus, LocalDate repayDate,
                                                                 String channel, GuaranteeCompany guaranteeCompany);

    int countByLoanIdAndCustRepayStatus(String loanId, RepayStatus repayStatus);

    int countByLoanIdAndCustRepayStatusAndRepayDateIsBefore(String loanId, RepayStatus custRepayStatus, LocalDate reccDay);

    List<LoanReplan> findByCustRepayStatusAndBankRepayStatusAndChannelAndRepayDateLessThanEqual(RepayStatus bankRepayStatus, RepayStatus custRepayStatus,
                                                                                           String channel, LocalDate repayDate);

    /**
     * 查询指定期数对资已代偿，对客未还的还款计划
     *
     * @return
     */
    @Query("SELECT lr FROM BankLoanReplan blr join LoanReplan lr ON blr.loanId = lr.loanId "
        + "AND blr.period = lr.period "
        + "AND blr.loanId = ?1 "
        + "AND lr.channel = ?2 "
        + "AND blr.repayStatus = 'REPAID' "
        + "AND blr.repayType = 'CLAIM' "
        + "AND lr.custRepayStatus = 'NORMAL' "
        + "AND lr.period < ?3")
    List<LoanReplan> findClaimedAndCustomNotPaid(String loanId, String channel, Integer period);


    @Query("select l from LoanReplan l where l.channel = ?1 and l.createdTime >= ?2 and l.createdTime < ?3")
    List<LoanReplan> findByChannelAndCreateDate(String channel, LocalDateTime startOfDay, LocalDateTime nextDayStart);
}


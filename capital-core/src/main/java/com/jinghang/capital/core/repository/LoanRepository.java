package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.BankChannelType;
import com.jinghang.capital.core.enums.LoanStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface LoanRepository extends JpaRepository<Loan, String> {

    Optional<Loan> findLoanByCreditIdAndOuterLoanId(String creditId, String outerLoanId);

    Optional<Loan> findByOuterLoanId(String outerLoanId);

    Optional<Loan> findByCreditId(String creditId);

    List<Loan> findByIdIn(List<String> loanIds);

    /**
     * 时间段范围内是否存在指定状态、指定资方的数据
     *
     * @param channel      资方
     * @param status       借据状态
     * @param dayStart     开始时间
     * @param nextDayStart 结束时间
     * @return
     */
    boolean existsByChannelAndLoanStatusAndLoanTimeBetween(BankChannel channel,
                                                           LoanStatus status,
                                                           LocalDateTime dayStart,
                                                           LocalDateTime nextDayStart);

    @Query("select l from Loan l where l.channel = ?1 and l.loanStatus = ?2 and l.loanTime >= ?3 and l.loanTime < ?4")
    List<Loan> findByChannelAndLoanStatusAndLoanTime(BankChannel channel,
                                                     LoanStatus status,
                                                     LocalDateTime dayStart,
                                                     LocalDateTime nextDayStart);


    @Query("select l.id from Loan l where l.channel = ?1 and l.loanStatus = ?2 and l.loanTime >= ?3 and l.loanTime < ?4")
    List<String> findIdsByChannelAndLoanStatusAndLoanTime(BankChannel channel,
                                                          LoanStatus status,
                                                          LocalDateTime dayStart,
                                                          LocalDateTime nextDayStart);


    @Query("select l from Loan l where l.channel = ?1 and l.loanStatus = ?2 and l.loanTime >= ?3 and l.loanTime < ?4")
    List<Loan> findByChannelAndLoanStatusAndFlowChannelAndLoanTime(BankChannel channel,
                                                                   LoanStatus status,
                                                                   LocalDateTime dayStart,
                                                                   LocalDateTime nextDayStart);





    /**
     * 长银直连
     *
     * @param channel
     * @param status
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query(value = "select l.id id from Loan l join account_bank_card abc on l.loan_card_id = abc.id "
        + " where l.channel = ?1 and l.loan_status = ?2 and l.loan_time >= ?3 and l.loan_time < ?4", nativeQuery = true)
    List<String> findCYBKLoanByChannelAndLoanStatusAndLoanTime(String channel, String status, LocalDateTime dayStart, LocalDateTime nextDayStart);

    /**
     * 富民查询区分新旧模式
     *
     * @param channel
     * @param status
     * @param dayStart
     * @param nextDayStart
     * @param channelType  富民固收新旧模式类型
     * @return
     */
    @Query("select l from Loan l where l.channel = ?1 and l.loanStatus = ?2 and l.loanTime >= ?3 and l.loanTime < ?4 and l.channelType = ?5")
    List<Loan> findByChannelAndLoanStatusAndFlowChannelAndLoanTimeAndChannelType(BankChannel channel,
                                                                                 LoanStatus status,
                                                                                 LocalDateTime dayStart,
                                                                                 LocalDateTime nextDayStart, BankChannelType channelType);


    List<Loan> findByChannelAndLoanTimeBetween(BankChannel channel, LocalDateTime loanTimeStart, LocalDateTime loanTimeEnd);

    List<Loan> findByLoanTimeBetween(LocalDateTime loanTimeStart, LocalDateTime loanTimeEnd);

    Optional<Loan> findByLoanNo(String loanNo);



    /**
     * 查询时间范围内结清的借据（对客结清，包含代偿后结清）
     *
     * @param bankChannel 资方
     * @param timeStart   时间范围开始
     * @param timeEnd     时间范围结束
     * @return
     */
    @Query("select distinct l from Loan l join CustomerLoanReplan clr on l.id = clr.loanId "
        + "where l.channel = ?1 and clr.actRepayTime >= ?2  and clr.actRepayTime < ?3  and (clr.repayPurpose = 'CLEAR' or l.periods = clr.period)")
    List<Loan> findCustomerSettleLoans(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    /**
     * 查询时间范围内发生代偿的借据
     *
     * @param bankChannel 资方
     * @param timeStart   时间范围开始
     * @param timeEnd     时间范围结束
     * @return
     */
    @Query("select l from Loan l "
        + " where l.channel = ?1 "
        + "   and exists(select 1 "
        + "             from BankRepayRecord "
        + "             where l.id = loanId "
        + "               and repayTime >= ?2 "
        + "               and repayTime < ?3 "
        + "               and repayType = 'CLAIM' "
        + "               and repayStatus = 'SUCCESS')")
    List<Loan> findClaimLoans(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    long countByIdInAndLoanStatusAndLoanTimeIsGreaterThanEqualAndLoanTimeIsLessThan(List<String> loanIds, LoanStatus loanStatus,
                                                                                    LocalDateTime startOfDay, LocalDateTime nextDayStart);


    /**
     * 校验对于同一资方、同一用户，是否存在某个状态的放款
     *
     * @param channel    资方
     * @param custCertNo 用户身份证号
     * @param loanStatus 状态
     * @return 存在 true 不存在 false
     */
    boolean existsByChannelAndCustCertNoAndLoanStatus(BankChannel channel, String custCertNo, LoanStatus loanStatus);

    @Query("select l"
        + " from Loan l"
        + " where l.channel = ?1"
        + "   and l.loanStatus = 'SUCCESS'"
        + "   and ((l.loanTime >= ?2 and l.loanTime < ?3)"
        + "     or exists(select 1 from CustomerRepayRecord crr"
        + "                where crr.loanId = l.id"
        + "                  and crr.repayStatus = 'SUCCESS'"
        + "                  and crr.repayTime >= ?2"
        + "                  and crr.repayTime < ?3)"
        + "     or exists(select 1"
        + "                from LoanReplan lr"
        + "                where lr.repayDate < ?3"
        + "                  and lr.loanId = l.id"
        + "                  and not exists(select 1"
        + "                                 from CustomerRepayRecord crr"
        + "                                 where lr.loanId = crr.loanId"
        + "                                   and lr.period = crr.period"
        + "                                   and crr.repayStatus = 'SUCCESS'"
        + "                                   and crr.repayTime < ?3)))")
    List<Loan> findZzrZbLoanList(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    /**
     * 根据放款合同编号查询
     *
     * @param loanContractNo 放款合同编号
     * @return
     */
    Optional<Loan> findByChannelAndLoanContractNo(BankChannel bankChannel, String loanContractNo);

    List<Loan> findByChannelAndCustCertNoAndLoanStatus(BankChannel channel, String certNo, LoanStatus status);

    List<Loan> findByCreditIdAndChannelAndLoanStatus(String creditId, BankChannel channel, LoanStatus status);

    /**
     * 查询第一期还款日在某一天的所有借据
     *
     * @param bankChannel 资方
     * @param repayDate   第一期还款日
     * @return loans
     */
    @Query("select l from Loan l join LoanReplan lr on l.id = lr.loanId "
        + "where l.channel = ?1 and lr.repayDate = ?2  and lr.period = 1 ")
    List<Loan> findFpdTotalLoans(BankChannel bankChannel, LocalDate repayDate);

    /**
     * 查询第一期还款日在某一天的所有逾期借据
     *
     * @param bankChannel 资方
     * @param repayDate   第一期还款日
     * @return loans
     */
    @Query("select l from Loan l join LoanReplan lr on l.id = lr.loanId "
        + "where l.channel = ?1 and lr.repayDate = ?2  and lr.period = 1 and lr.bankRepayStatus = 'NORMAL' ")
    List<Loan> findFpdLoans(BankChannel bankChannel, LocalDate repayDate);


    List<Loan> findByCustCertNoAndLoanStatus(String custCertNo, LoanStatus loanStatus);

    long countByChannelAndCustCertNoAndLoanStatusIn(BankChannel bankChannel, String certNo, List<LoanStatus> loanStatusList);
}

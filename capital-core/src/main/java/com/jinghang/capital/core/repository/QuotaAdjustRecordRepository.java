package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.QuotaAdjustRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
public interface QuotaAdjustRecordRepository extends JpaRepository<QuotaAdjustRecord, String> {

    /**
     * 校验对于同一资方、同一用户，是否存在某个状态的调额记录
     *
     * @param channel    资方
     * @param userIdCard 用户身份证号
     * @param status     状态
     * @return 存在 true 不存在 false
     */
    boolean existsByChannelAndUserIdCardAndStatus(BankChannel channel, String userIdCard, ProcessStatus status);

}

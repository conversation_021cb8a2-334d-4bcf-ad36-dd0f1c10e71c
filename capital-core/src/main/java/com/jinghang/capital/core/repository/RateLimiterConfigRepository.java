package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.RateLimiterConfig;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.service.limiter.enums.RateLimiterFunction;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface RateLimiterConfigRepository extends JpaRepository<RateLimiterConfig, String> {

    Optional<RateLimiterConfig> findByChannelAndLimiterFunction(BankChannel channel, RateLimiterFunction limiterFunction);

}

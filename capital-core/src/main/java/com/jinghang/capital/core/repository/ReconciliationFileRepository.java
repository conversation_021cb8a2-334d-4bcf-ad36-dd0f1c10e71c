package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.ReconciliationFile;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.vo.ProductVo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
public interface ReconciliationFileRepository extends JpaRepository<ReconciliationFile, String> {

    ReconciliationFile findFirstByFileTypeAndProductAndFileDateAndBankChannelOrderByCreatedTimeDesc(FileType fileType, ProductVo productVo,
                                                                                                    LocalDate fileDate, BankChannel bankChannel);

    ReconciliationFile findFirstByFileTypeAndProductAndFileDateOrderByCreatedTimeDesc(FileType fileType, ProductVo productVo, LocalDate fileDate);
}

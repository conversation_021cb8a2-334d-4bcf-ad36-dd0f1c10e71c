package com.jinghang.capital.core.service;

import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务
 */
@Component
public class CacheService {

    private final RedissonClient redisson;

    public CacheService(RedissonClient redisson) {
        this.redisson = redisson;
    }

    /**
     * 缓存对象
     *
     * @param key      键值
     * @param value    缓存对象
     * @param ttl      过期时间
     * @param timeUnit 过期单位
     */
    public void put(String key, Object value, long ttl, TimeUnit timeUnit) {
        redisson.getBucket(key).set(value, ttl, timeUnit);
    }

    /**
     * 缓存对象
     *
     * @param key      键值
     * @param value    缓存对象
     * @param duration 缓存时间
     */
    public void put(String key, Object value, Duration duration) {
        redisson.getBucket(key).set(value, duration);
    }

    /**
     * 获取缓存对象
     *
     * @param key 键值
     * @return 缓存对象
     */
    public Object get(String key) {
        return redisson.getBucket(key).get();
    }


    /**
     * 键值是否存在
     *
     * @param key
     * @return
     */
    public boolean existKey(String key) {
        return redisson.getBucket(key).isExists();
    }

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    public void delete(String key) {
        redisson.getBucket(key).delete();
    }

}

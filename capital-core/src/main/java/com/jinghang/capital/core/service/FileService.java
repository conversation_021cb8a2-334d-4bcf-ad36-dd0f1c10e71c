package com.jinghang.capital.core.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.IOUtils;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.jinghang.common.util.DateUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.util.Date;
import java.util.function.Consumer;

@Service
public class FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileService.class);

    private static final String[] BEIJING_BUCKET_NAMES = {"test-jh01"};

    private static final Long EXPIRATION_DAY = 7L;

    private OSS ossClient;

    private OSS switchOss(String bucketName) {

        return ossClient;
    }

    public String getOssUrl(String bucket, String key) {
        return getOssUrl(bucket, key, DateUtil.toDate(LocalDate.now().plusDays(EXPIRATION_DAY)));
    }
    public String getOssUrl(String bucket, String key, LocalDate expireDate) {
        return getOssUrl(bucket, key, DateUtil.toDate(expireDate));
    }

    public String getOssUrl(String bucket, String key, Date expireDate) {
        URL url = switchOss(bucket).generatePresignedUrl(bucket, key, expireDate);
        return url.toString();
    }
    /**
     * 获取流需要关闭
     */
    public InputStream getOssFile(String bucket, String key) {
        return switchOss(bucket).getObject(bucket, key).getObjectContent();
    }

    public void getOssFile(String bucket, String key, Consumer<InputStream> streamConsumer) {
        try (OSSObject object = switchOss(bucket).getObject(bucket, key)) {
            streamConsumer.accept(object.getObjectContent());
        } catch (IOException e) {
            logger.error("关闭ossObject异常:", e);
        }
    }

    public String getOssFileBase64String(String bucket, String key) {
        try (InputStream inputStream = getOssFile(bucket, key)) {
            return Base64.encodeBase64String(IOUtils.readStreamAsByteArray(inputStream));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public byte[] getOssFileBytes(String bucket, String key) {
        try (InputStream inputStream = getOssFile(bucket, key)) {
            return IOUtils.readStreamAsByteArray(inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public long getOssFileSize(String bucket, String key) {
        ObjectMetadata objectMeta = switchOss(bucket).getObjectMetadata(bucket, key);
        return objectMeta.getContentLength();
    }

    public void uploadOss(String bucket, String key, String base64) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(Base64.decodeBase64(base64));
        uploadOss(bucket, key, inputStream);
    }

    public void uploadOss(String bucket, String key, InputStream inputStream) {
        switchOss(bucket).putObject(bucket, key, inputStream);
    }

    public static String convertOssUrlToBase64(String ossUrl) throws Exception {
        URL url = new URL(ossUrl);
        HttpURLConnection connection = ( HttpURLConnection ) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream()) {
            byte[] fileBytes = readStreamToByteArray(inputStream);
            return Base64.encodeBase64String(fileBytes);
        }
    }

    private static byte[] readStreamToByteArray(InputStream inputStream) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;

        while ((len = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, len);
        }

        return byteArrayOutputStream.toByteArray();
    }

    @Autowired
    public void setOssClient(OSS ossClient) {
        this.ossClient = ossClient;
    }
}

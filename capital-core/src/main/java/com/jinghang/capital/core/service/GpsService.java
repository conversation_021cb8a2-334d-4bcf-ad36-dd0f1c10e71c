package com.jinghang.capital.core.service;


import com.jinghang.capital.core.convert.GpsDistrictConvert;
import com.jinghang.capital.core.dto.DistrictDto;
import com.jinghang.capital.core.entity.GpsDistrict;
import com.jinghang.capital.core.repository.GpsDistrictRepository;
import org.apache.commons.rng.UniformRandomProvider;
import org.apache.commons.rng.simple.RandomSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

@Service
public class GpsService {

    public static final int GPS_SCALE = 6;
    @Autowired
    private GpsDistrictRepository gpsDistrictRepository;

    private static final UniformRandomProvider RANDOM = RandomSource.XO_RO_SHI_RO_128_PP.create();

    public DistrictDto findByProvince(String code) {
        List<GpsDistrict> districts = gpsDistrictRepository.findByProvinceCode(code);
        return extractGps(districts);
    }

    /**
     * 查询省code并排除市codes
     * @param provinceCode
     * @param cityCodes
     * @return
     */
    public DistrictDto findByProvinceExcludeCity(String provinceCode, String... cityCodes) {
        List<String> cityCodeList = Arrays.stream(cityCodes).toList();
        if (cityCodeList.isEmpty()) {
            return findByProvince(provinceCode);
        }
        List<GpsDistrict> districts = gpsDistrictRepository.findByProvinceCodeAndCityCodeNotIn(provinceCode, cityCodeList);
        return extractGps(districts);
    }

    /**
     * 查询省code并排除市codes
     * @param provinceCode
     * @param cityCodes
     * @return
     */
    public DistrictDto findByProvinceExcludeCity(String provinceCode, List<String> cityCodes) {
        if (null == cityCodes || cityCodes.isEmpty()) {
            return findByProvince(provinceCode);
        }
        List<GpsDistrict> districts = gpsDistrictRepository.findByProvinceCodeAndCityCodeNotIn(provinceCode, cityCodes);
        return extractGps(districts);
    }

    public DistrictDto findByCity(String code) {
        List<GpsDistrict> districts = gpsDistrictRepository.findByCityCode(code);
        return extractGps(districts);
    }

    public DistrictDto findByCityCodes(List<String> cityCodes) {
        List<GpsDistrict> districts = gpsDistrictRepository.findByCityCodeIn(cityCodes);
        return extractGps(districts);
    }

    public DistrictDto findByDistricts(List<String> districtCodes) {
        List<GpsDistrict> districts = gpsDistrictRepository.findByDistrictCodeIn(districtCodes);
        return extractGps(districts);
    }

    private DistrictDto extractGps(List<GpsDistrict> districts) {
        if (districts == null || districts.isEmpty()) {
            return null;
        }
        GpsDistrict district = districts.get(RANDOM.nextInt(districts.size()));
        BigDecimal longitude = BigDecimal.valueOf(RANDOM.nextDouble(district.getLongitudeMin().doubleValue(), district.getLongitudeMax().doubleValue()));
        BigDecimal latitude = BigDecimal.valueOf(RANDOM.nextDouble(district.getLatitudeMin().doubleValue(), district.getLatitudeMax().doubleValue()));
        DistrictDto districtDto = GpsDistrictConvert.INSTANCE.convertGps(district);
        districtDto.setLongitude(longitude.setScale(GPS_SCALE, RoundingMode.HALF_UP));
        districtDto.setLatitude(latitude.setScale(GPS_SCALE, RoundingMode.HALF_UP));
        return districtDto;
    }


}

package com.jinghang.capital.core.service;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
public class LockService {

    private final RedissonClient redisson;

    public LockService(RedissonClient redisson) {
        this.redisson = redisson;
    }

    public void lock(String key, Duration leaseTime, Runnable runnable) {
        Locker lock = getLock(key);
        try {
            lock.lock(leaseTime);
            runnable.run();
        } finally {
            lock.unlock();
        }
    }

    public boolean tryLock(String key, Duration waitTime, Duration leaseTime, Runnable runnable) throws InterruptedException {
        Locker lock = getLock(key);
        boolean isLocked = false;
        if (lock.tryLock(waitTime, leaseTime)) {
            isLocked = true;
            try {
                runnable.run();
            } finally {
                lock.unlock();
            }
        }
        return isLocked;
    }

    public Locker getLock(String key) {
        RLock rLock = redisson.getLock(key);
        return new Locker(rLock);
    }
}

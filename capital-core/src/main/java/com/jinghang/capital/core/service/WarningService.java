package com.jinghang.capital.core.service;

import brave.Tracer;
import com.jinghang.common.notice.NotifyMsgSender;
import com.jinghang.common.util.StringUtil;


import java.util.function.Consumer;

public class WarningService {

    private final String wxWarningKey;

    private final Tracer tracer;

    public WarningService(String wxWarningKey, Tracer tracer) {
        this.wxWarningKey = wxWarningKey;
        this.tracer = tracer;
    }

    public void warn(String msg, Consumer<String> consumer) {
        consumer.accept(msg);
        warn(msg);
    }

    public void warn(String msg) {
        String traceId = null;
        try {
            traceId = tracer == null ? null : tracer.currentSpan().context().traceIdString();
        } catch (Exception e) {
            // IGNORE
        }
        String msg2Send = StringUtil.isBlank(traceId) ? msg : traceId + ":" + msg;
        NotifyMsgSender.useWeWork(this.wxWarningKey).withText(msg2Send).send();
    }

    public void warn(Exception e) {
        warn(e.getMessage());
    }

    public void warn(Exception e, Consumer<Exception> consumer) {
        consumer.accept(e);
        warn(e);
    }
}

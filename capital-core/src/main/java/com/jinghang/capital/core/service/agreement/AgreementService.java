package com.jinghang.capital.core.service.agreement;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.core.entity.AgreementSignature;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AgreementSignatureRepository;
import com.jinghang.capital.core.repository.LoanFileRepository;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.remote.nfsp.sign.SignService;
import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import com.jinghang.capital.core.service.remote.nfsp.sign.res.ResultMsg;
import com.jinghang.capital.core.util.StreamUtil;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 签章服务
 */
@Service
public class AgreementService {

    private static final Logger logger = LoggerFactory.getLogger(AgreementService.class);

    @Autowired
    private ManageService manageService;

    @Autowired
    private AgreementSignatureRepository agreementSignatureRepository;


    @Autowired
    private FileService fileService;

    @Autowired
    private LoanFileRepository loanFileRepository;

    @Autowired
    private WarningService warningService;

    @Value(value = "${agreement.oss.key.path}")
    private String ossPath;

    @Value(value = "${oss.bucket.name}")
    private String signOssBucket;

    @Autowired
    private SignService signService;

    @Autowired
    private MqService mqService;

    public static final int MAX_FAIL_LENGTH = 50;


    /**
     * 签章申请， 监听入口
     *
     * @param signId 签章id
     */
    public void sinApply(String signId) {

        Optional<AgreementSignature> agreementSignatureOptional = agreementSignatureRepository.findById(signId);
        if (agreementSignatureOptional.isEmpty()) {
            warningService.warn("签章申请， id 不存在: " + signId);
            return;
        }
        AgreementSignature agreementSignature = agreementSignatureOptional.get();
        SignApplyReq signApplyReq = assembleTemplateParam(agreementSignature);
        logger.info("资金签章请求: signApplyReq: {}", JsonUtil.toJsonString(signApplyReq));
        ResultMsg sign = signService.sign(signApplyReq);
        if (sign.getCode().equals("200") && sign.getObject() != null) {
            agreementSignature.setSignState(ProcessStatus.PROCESSING);
            agreementSignature.setCommonTaskId(sign.getObject().toString());
            agreementSignatureRepository.save(agreementSignature);
            mqService.signatureQueryDelay(signId);
        } else {
            throw new BizException(BizErrorCode.SIGN_APPLY_ERROR);
        }

    }


    private SignApplyReq assembleTemplateParam(AgreementSignature signature) {
        LoanStage stage = signature.getLoanStage();
        BankChannel channel = signature.getChannel();
        var agrType = AgreementType.getAgreement(channel, stage, signature.getSignatureType(), signature.getFileType());
        return manageService.getContractInfoTemplate(channel, signature.getBusinessId(), agrType);
    }


    /**
     * 签章协议查询, 监听入口
     */
    public void signQuery(String signId) {
        Optional<AgreementSignature> agreementSignatureOptional = agreementSignatureRepository.findById(signId);
        if (agreementSignatureOptional.isEmpty()) {
            warningService.warn("签章申请， id 不存在: " + signId);
            return;
        }
        AgreementSignature agreementSignature = agreementSignatureOptional.get();
        // 已经成功不需要再去查询结果
        if (ProcessStatus.SUCCESS == agreementSignature.getSignState()) {
            return;
        }


        logger.info("资金签章地址获取请求: taskID: {}", JsonUtil.toJsonString(agreementSignature.getCommonTaskId()));
        // 查询协议签署结果
        ResultMsg resultMsg = signService.getSignUrl(agreementSignature.getCommonTaskId());
        if ("200".equals(resultMsg.getCode())) {
            agreementSignature.setOssFileUrl(resultMsg.getObject().toString());
            agreementSignature.setSignState(ProcessStatus.SUCCESS);
            transferSignatureToLoanFile(agreementSignature);
            agreementSignatureRepository.save(agreementSignature);
        }else {
            ProcessStatus signStatus = agreementSignature.getSignState();
            if (ProcessStatus.PROCESSING == signStatus) {
                mqService.signatureQueryDelay(signId);
                return;
            }
        }
    }


    /**
     * 新签章协议查询, 监听入口
     */
    public void signNewQuery(String signId) {

    }


    /**
     * 将签章内容同步到loanFile表
     *
     * @param signature 签章
     */
    private void transferSignatureToLoanFile(AgreementSignature signature) {
        LoanStage stage = signature.getLoanStage();
        String bizId = signature.getBusinessId();
        String bizCreditId = bizId;
        LoanFile loanFile = null;
        AgreementType agrType = null;
        switch (stage) {
            case CREDIT -> {
                Credit credit = manageService.getCommonService().findCreditById(bizId);
                agrType = AgreementType.getAgreement(credit.getChannel(), LoanStage.CREDIT, signature.getSignatureType(), signature.getFileType());
                if (agrType == null) {
                    warningService.warn("签章异常, AgreementType未找到, signId: " + signature.getId());
                    return;
                }
                loanFile = fetchCreditStageLoanFile(credit.getId(), agrType.getFileType());
                if (loanFile == null) {
                    loanFile = createCreditStageLoanFile(credit, agrType);

                }
            }
            case LOAN -> {
                Loan loan = manageService.getCommonService().findLoanById(bizId);
                bizCreditId = loan.getCreditId();
                agrType = AgreementType.getAgreement(loan.getChannel(), LoanStage.LOAN, signature.getSignatureType(), signature.getFileType());
                if (agrType == null) {
                    warningService.warn("签章异常, AgreementType未找到, signId: " + signature.getId());
                    return;
                }
                loanFile = fetchLoanStageLoanFile(loan.getCreditId(), loan.getId(), agrType.getFileType(), LoanStage.LOAN);
                if (loanFile == null) {
                    loanFile = createLoanStageLoanFile(loan, agrType);
                }
            }
            case AFTER_LOAN -> {
                Loan loan = manageService.getCommonService().findLoanById(bizId);
                bizCreditId = loan.getCreditId();
                agrType = AgreementType.getAgreement(loan.getChannel(), LoanStage.AFTER_LOAN, signature.getSignatureType(), signature.getFileType());
                if (agrType == null) {
                    warningService.warn("签章异常, AgreementType未找到, signId: " + signature.getId());
                    return;
                }
                loanFile = fetchLoanStageLoanFile(loan.getCreditId(), loan.getId(), agrType.getFileType(), LoanStage.AFTER_LOAN);
                if (loanFile == null) {
                    loanFile = createLoanStageAfterLoanFile(loan, agrType);
                }
            }
            case AFTER_WEEK -> {
                agrType = AgreementType.getAgreement(LoanStage.AFTER_WEEK, signature.getSignatureType(), signature.getFileType());
                if (agrType == null) {
                    warningService.warn("签章异常, AgreementType未找到, signId: " + signature.getId());
                    return;
                }
                loanFile = fetchOtherStageLoanFile(bizId, stage, signature.getFileType());
                if (loanFile == null) {
                    loanFile = createOtherStageAfterLoanFile(signature);
                }
            }
            default -> {
            }
        }

        if (loanFile != null) {
            LoanFile finalLoanFile = loanFile;
            logger.info("签署成功后,协议转存oss, agreement_sign_id: {},{}", signature.getId(), signature.getFileType());
            finalLoanFile.setOssBucket(signOssBucket);
            finalLoanFile.setOssKey(signature.getOssFileUrl());
            boolean needCaptionSign = agrType.getNeedSignAgain();
            finalLoanFile.setSignStatus(needCaptionSign ? ProcessStatus.PROCESSING : ProcessStatus.SUCCESS);
            logger.info("上传成功后,保存loanFile, {}", JSON.toJSONString(finalLoanFile));
            String ossFile = fileService.getOssUrl(signOssBucket, signature.getOssFileUrl());
            logger.info("协议文件, {}", ossFile);
            loanFileRepository.save(finalLoanFile);
//            String dirStr = LocalDate.now().format(DateTimeFormatter.ofPattern("/yyyMMdd/")) + bizCreditId + "/"
//                    + bizId + "_" + loanFile.getFileType() + agrType.getExtension();
//            boolean needCaptionSign = agrType.getNeedSignAgain();
//            try {
//                LoanFile finalLoanFile = loanFile;
//                HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, signature.getOssFileUrl(), inputStream -> {
//                    StringBuilder builderKey = new StringBuilder().append(ossPath).append(dirStr);
//                    //
//                    logger.info("签署成功后,协议转存oss, agreement_sign_id: {},{}", signature.getId(), signature.getFileType());
//                    fileService.uploadOss(signOssBucket, builderKey.toString(), inputStream);
//                    finalLoanFile.setOssBucket(signOssBucket);
//                    finalLoanFile.setOssKey(builderKey.toString());
//
//                    finalLoanFile.setSignStatus(needCaptionSign ? ProcessStatus.PROCESSING : ProcessStatus.SUCCESS);
//                    //
//                    logger.info("上传成功后,保存loanFile, {}", JSON.toJSONString(finalLoanFile));
//                    loanFileRepository.save(finalLoanFile);
//                    StreamUtil.safeClose(inputStream);
//                });
//            } catch (HttpException e) {
//                logger.error("协议文件根据地址下载转存失败:", e);
//                throw new RuntimeException(e);
//            }
        }

    }

    private LoanFile fetchCreditStageLoanFile(String creditId, FileType fileType) {
        return loanFileRepository.findTopByCreditIdAndStageAndFileType(creditId, LoanStage.CREDIT.name(), fileType);
    }

    private LoanFile fetchLoanStageLoanFile(String creditId, String loanId, FileType fileType, LoanStage loanStage) {
        return loanFileRepository.findTopByCreditIdAndRelatedIdAndStageAndFileType(creditId, loanId, loanStage.name(), fileType);
    }

    private LoanFile fetchOtherStageLoanFile(String relatedId, LoanStage loanStage, FileType fileType) {
        return loanFileRepository.findTopByRelatedIdAndStageAndFileType(relatedId, loanStage.name(), fileType);
    }


    private LoanFile createCreditStageLoanFile(Credit credit, AgreementType agrType) {
        LoanFile loanFile = new LoanFile();
        loanFile.setChannel(credit.getChannel());
        loanFile.setCreditId(credit.getId());
        loanFile.setStage(LoanStage.CREDIT.name());
        loanFile.setFileType(agrType.getFileType());
        loanFile.setSignStatus(ProcessStatus.INIT);
        loanFile.setFileName(agrType.getFileType().getDesc());
        loanFile.setRelatedId(credit.getId());

        return loanFile;
    }


    private LoanFile createLoanStageLoanFile(Loan loan, AgreementType agrType) {
        LoanFile loanFile = new LoanFile();
        loanFile.setChannel(loan.getChannel());
        loanFile.setRelatedId(loan.getId());
        loanFile.setCreditId(loan.getCreditId());
        loanFile.setStage(LoanStage.LOAN.name());
        loanFile.setFileType(agrType.getFileType());
        loanFile.setSignStatus(ProcessStatus.INIT);
        loanFile.setFileName(agrType.getFileType().getDesc());
        loanFile.setRelatedId(loan.getId());

        return loanFile;
    }

    private LoanFile createLoanStageAfterLoanFile(Loan loan, AgreementType agrType) {
        LoanFile loanFile = new LoanFile();
        loanFile.setChannel(loan.getChannel());
        loanFile.setRelatedId(loan.getId());
        loanFile.setCreditId(loan.getCreditId());
        loanFile.setStage(LoanStage.AFTER_LOAN.name());
        loanFile.setFileType(agrType.getFileType());
        loanFile.setSignStatus(ProcessStatus.INIT);
        loanFile.setFileName(agrType.getFileType().getDesc());
        loanFile.setRelatedId(loan.getId());

        return loanFile;
    }

    private LoanFile createOtherStageAfterLoanFile(AgreementSignature signature) {
        LoanFile loanFile = new LoanFile();
        loanFile.setRelatedId(signature.getBusinessId());
        loanFile.setChannel(signature.getChannel());
        loanFile.setStage(LoanStage.AFTER_WEEK.name());
        loanFile.setFileType(signature.getFileType());
        loanFile.setSignStatus(ProcessStatus.INIT);
        loanFile.setFileName(signature.getFileType().getDesc());
        return loanFile;
    }


    /**
     * 截取错误消息
     *
     * @param message 消息
     * @return
     */
    private String cutOut(String message) {
        return message.length() > MAX_FAIL_LENGTH ? message.substring(0, MAX_FAIL_LENGTH - 1) : message;
    }
}

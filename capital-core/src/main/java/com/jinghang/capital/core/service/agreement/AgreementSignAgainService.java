package com.jinghang.capital.core.service.agreement;


import com.jinghang.capital.core.entity.AgreementSignAgain;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.repository.AgreementSignAgainRepository;
import com.jinghang.capital.core.service.MqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 签章重签服务服务
 */
@Service
public class AgreementSignAgainService {

    private static final Logger logger = LoggerFactory.getLogger(AgreementSignAgainService.class);


    @Autowired
    private AgreementSignAgainRepository agreementSignAgainRepository;

    @Autowired
    private MqService mqService;


    private static final int SLEEP_MILLIS = 500;


    public void signApplyAgain(LocalDate processDate) {

        List<AgreementSignAgain> records = agreementSignAgainRepository.findBySignStatusAndProcessDate(ProcessStatus.INIT, processDate);

        records.forEach(record -> {
            String signId = record.getAgreementSignatureId();
            mqService.signatureApplyDelay(signId);

            record.setSignStatus(ProcessStatus.SUCCESS);
            agreementSignAgainRepository.save(record);

            try {
                Thread.sleep(SLEEP_MILLIS);
            } catch (InterruptedException e) {
                logger.error("sinApplyAgain", e);
            }
        });

    }


}

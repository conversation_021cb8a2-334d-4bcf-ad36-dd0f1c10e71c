package com.jinghang.capital.core.service.event.listener;


import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.event.QuotaQueryResultEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class QuotaQueryResultEventListener {

    @Autowired
    private ManageService manageService;

    @EventListener(QuotaQueryResultEvent.class)
    public void onApplicationEvent(QuotaQueryResultEvent event) {
        manageService.quotaQueryResultCallback(event.dto(), event.resultDto());
    }
}

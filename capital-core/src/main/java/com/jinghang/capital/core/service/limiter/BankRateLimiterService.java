package com.jinghang.capital.core.service.limiter;


import com.jinghang.capital.core.entity.RateLimiterConfig;
import com.jinghang.capital.core.repository.RateLimiterConfigRepository;
import com.jinghang.capital.core.service.limiter.dto.RateLimiterFunctionDto;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 限流器限流功能
 */
@Service
public class BankRateLimiterService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RateLimiterConfigRepository rateLimiterConfigRepository;

    // 默认限制速率 60次/秒
    private static final Long SIXTY = 60L;

    /**
     * 限流器Map
     */
    private static final Map<String, RRateLimiter> RATE_LIMITER_MAP = new ConcurrentHashMap<>();

    /**
     * 限流
     * 使用限流方法时，请仔细察看基础限流器getBaseRateLimiter的描述
     */
    public void rateLimit(RateLimiterFunctionDto dto) {
        getBaseRateLimiter(dto).acquire();
    }

    /**
     * 基础限流器
     * 限流器的key与限流速率，维护在rate_limiter_config表中
     * 1.若数据未维护，则自动新增一条默认数据
     * 2.若需要为多个资方共用同一个限流器，则需要将配置表中的name与rate两个字段维护为相同的值
     *
     * @param dto 限流器名称
     * @return 限流器
     */
    private RRateLimiter getBaseRateLimiter(RateLimiterFunctionDto dto) {
        String mapKey = dto.channel().name() + dto.rateLimiterFunction().name();
        return RATE_LIMITER_MAP.computeIfAbsent(mapKey, k -> {
            // 配置信息
            RateLimiterConfig config = getRateLimiterConfig(dto);
            // 获取限流器
            RRateLimiter rateLimiter = redissonClient.getRateLimiter(config.getName());
            // 设置限流频率
            rateLimiter.setRate(RateType.OVERALL, config.getRate(), 1, RateIntervalUnit.SECONDS);
            return rateLimiter;
        });
    }

    /**
     * 获取限流器配置信息（若不存在配置，则新增一条默认配置数据）
     *
     * @param dto 参数
     * @return 限流器配置信息
     */
    private RateLimiterConfig getRateLimiterConfig(RateLimiterFunctionDto dto) {
        return rateLimiterConfigRepository.findByChannelAndLimiterFunction(dto.channel(), dto.rateLimiterFunction())
                .orElseGet(() -> {
                    RateLimiterConfig config = new RateLimiterConfig();
                    config.setChannel(dto.channel());
                    config.setLimiterFunction(dto.rateLimiterFunction());
                    config.setName(getName(dto));
                    config.setRate(SIXTY);
                    String desc = dto.channel().getDesc() + "-" + dto.rateLimiterFunction().getReteLimiterClass().getDesc()
                            + ": " + dto.rateLimiterFunction().getDesc();
                    config.setLimiterDesc(desc);
                    return rateLimiterConfigRepository.save(config);
                });
    }

    /**
     * 更新限制器限制频率
     *
     * @param configId 限流器配置信息ID
     */
    public void updateRate(String configId) {
        RateLimiterConfig config = rateLimiterConfigRepository.findById(configId).orElseThrow();
        String mapKey = config.getChannel().name() + config.getLimiterFunction().name();
        RRateLimiter rateLimiter = RATE_LIMITER_MAP.computeIfAbsent(mapKey, k -> {
            // 获取限流器
            return redissonClient.getRateLimiter(config.getName());
        });
        // 设置限流频率
        rateLimiter.setRate(RateType.OVERALL, config.getRate(), 1, RateIntervalUnit.SECONDS);
    }

    /**
     * 默认限流器名称（限流器的key）
     *
     * @param dto 参数
     * @return key
     */
    private String getName(RateLimiterFunctionDto dto) {
        return dto.rateLimiterFunction().getReteLimiterClass().getPrefix()
                + ":rate:" + dto.channel() + "_" + dto.rateLimiterFunction().getSuffix();
    }

}

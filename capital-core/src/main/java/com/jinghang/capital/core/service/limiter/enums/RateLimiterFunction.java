package com.jinghang.capital.core.service.limiter.enums;

/**
 * 限流器功能
 */
public enum RateLimiterFunction {

    CREDIT_APPLY(ReteLimiterClass.BANK_POST, "credit-apply", "授信申请");

    RateLimiterFunction(ReteLimiterClass reteLimiterClass, String suffix, String desc) {
        this.reteLimiterClass = reteLimiterClass;
        this.suffix = suffix;
        this.desc = desc;
    }

    private final ReteLimiterClass reteLimiterClass;

    private final String suffix;

    private final String desc;

    public ReteLimiterClass getReteLimiterClass() {
        return reteLimiterClass;
    }

    public String getSuffix() {
        return suffix;
    }

    public String getDesc() {
        return desc;
    }

}

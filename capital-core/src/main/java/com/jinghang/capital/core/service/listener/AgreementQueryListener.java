package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.agreement.AgreementService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class AgreementQueryListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(AgreementQueryListener.class);

    public AgreementQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private AgreementService agreementService;

    @RabbitListener(queues = RabbitConfig.Queues.SIGNATURE_QUERY)
    public void listenSignQuery(Message message, Channel channel) {
        String signId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听查询签章:{}", signId);
            agreementService.signQuery(signId);
        } catch (Exception e) {
            logger.error("old sign query error", e);
            processException(signId, message, e, "查询签章异常", getMqService()::signatureQueryDelay);
        } finally {
            ackMsg(signId, message, channel);
        }
    }
}

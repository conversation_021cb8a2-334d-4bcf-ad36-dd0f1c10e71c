package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class BatchRepayResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(BatchRepayResultListener.class);

    public BatchRepayResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.BATCH_REPAY_QUERY)
    public void listenRepayResult(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听批量还款结果查询:{}", repayId);
            manageService.batchRepayQuery(repayId);
        } catch (Exception e) {
            logger.error("批量还款异常，repayId: {}", repayId, e);
            processException(repayId, message, e, "查询批量还款结果异常", getMqService()::submitBatchRepayQueryDelay);
        } finally {
            ackMsg(repayId, message, channel);
        }
    }
}

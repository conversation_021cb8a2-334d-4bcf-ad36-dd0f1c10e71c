package com.jinghang.capital.core.service.listener;

import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 代偿后还款通知
 * 目前临商手动重推
 */
@Component
public class ClaimAfterRepayNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(ClaimAfterRepayNotifyListener.class);

    public ClaimAfterRepayNotifyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    private ManageService manageService;


    @RabbitListener(queues = RabbitConfig.Queues.CLAIM_AFTER_NOTIFY)
    public void listenClaimAfterNotify(Message message, Channel channel) {
        String claimAfterRecordId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听代偿后还款通知: {}", claimAfterRecordId);
            manageService.claimAfterNotify(claimAfterRecordId);
        } catch (Exception e) {
            logger.error("claim after notify error. claimAfterRecordId: {}", claimAfterRecordId, e);
            processException(claimAfterRecordId, message, e, "代偿后还款通知异常", getMqService()::submitClaimAfterNotifyDelay);
        } finally {
            ackMsg(claimAfterRecordId, message, channel);
        }
    }

    @Autowired
    public void setManageService(ManageService manageService) {
        this.manageService = manageService;
    }


}

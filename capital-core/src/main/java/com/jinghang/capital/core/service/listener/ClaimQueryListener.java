package com.jinghang.capital.core.service.listener;

import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class ClaimQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(ClaimQueryListener.class);

    public ClaimQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.CLAIM_QUERY)
    public void listenClaimResult(Message message, Channel channel) {
        String bankRepayRecordId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听代偿申请结果:{}", bankRepayRecordId);
            // manageService
            manageService.claimQuery(bankRepayRecordId);
        } catch (Exception e) {
            processException(bankRepayRecordId, message, e, "代偿申请结果异常", getMqService()::submitClaimQueryDelay);
        } finally {
            ackMsg(bankRepayRecordId, message, channel);
        }
    }
}

package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 合同下载监听
 */
@Component
public class ContractDownloadListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(ContractDownloadListener.class);

    public ContractDownloadListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.CONTRACT_DOWNLOAD)
    public void listenContractDownload(Message message, Channel channel) {
        String contractMsg = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("下载资方合同, 资方授信id:{}", contractMsg);
            var bizDto = JsonUtil.convertToObject(contractMsg, ContractBizDto.class);
            // manageService
            manageService.contractDownload(bizDto.getBusinessId(), bizDto.getStage());
        } catch (Exception e) {
            processException(contractMsg, message, e, "下载资方合同", getMqService()::submitContractDownloadDelay);
        } finally {
            ackMsg(contractMsg, message, channel);
        }
    }
}

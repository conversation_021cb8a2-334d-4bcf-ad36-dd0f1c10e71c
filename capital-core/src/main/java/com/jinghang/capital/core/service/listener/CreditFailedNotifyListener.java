package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 风控失败，拒量推送资方
 */
@Component
public class CreditFailedNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditFailedNotifyListener.class);

    public CreditFailedNotifyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_FAILED_NOTIFY)
    public void listenCreditApply(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("风控失败的推送资方授信 failed_credit_id: {}", creditId);
            // manageService
            manageService.creditFailedNotify(creditId);
        } catch (Exception e) {
            // processException(creditId, message, e, "授信申请异常", getMqService()::submitCreditResultQueryDelay);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }
}

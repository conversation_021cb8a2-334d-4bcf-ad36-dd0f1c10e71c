package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.AdjustLimitBizDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 新的调额查询结果监听
 * 暂时是 分期乐-富民
 */
@Component
public class CreditNewAdjustQueryListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(CreditNewAdjustQueryListener.class);

    @Autowired
    private ManageService manageService;

    public CreditNewAdjustQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    /**
     * 监听新的调额申请队列
     */
    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_NEW_ADJUST_QUERY)
    public void listenCreditNewAdjustResultQuery(Message message, Channel channel) {
        String adjustCreditMsg = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听调额申请:{}", adjustCreditMsg);

            AdjustLimitBizDto bizDto = JsonUtil.convertToObject(adjustCreditMsg, AdjustLimitBizDto.class);

            manageService.adjustLimit(bizDto);

        } catch (Exception e) {
            processException(adjustCreditMsg, message, e, "调额申请异常", getMqService()::submitCreditNewAdjustQueryDelay);
        } finally {
            ackMsg(adjustCreditMsg, message, channel);
        }
    }


}

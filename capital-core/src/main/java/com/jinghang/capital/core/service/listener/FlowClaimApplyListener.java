package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 流量方代偿申请监听
 */
@Component
public class FlowClaimApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(FlowClaimApplyListener.class);

    public FlowClaimApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.FLOW_CLAIM_APPLY)
    public void listenFlowClaimApply(Message message, Channel channel) {
        String claimId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听流量方代偿申请:{}", claimId);
            // manageService
            manageService.flowClaimApply(claimId);
        } catch (Exception e) {
            logger.error("流量方代偿申请异常", e);
            processException(claimId, message, e, "流量方代偿申请异常", getMqService()::submitFlowClaimQueryDelay);
        } finally {
            ackMsg(claimId, message, channel);
        }
    }
}

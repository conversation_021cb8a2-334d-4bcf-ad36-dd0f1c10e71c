package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class LoanApplyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanApplyListener.class);

    public LoanApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_APPLY)
    public void listenLoanApply(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听放款申请:{}", loanId);
            // manageService
            manageService.loanApply(loanId);
        } catch (Exception e) {
            processException(loanId, message, e, "放款申请异常", getMqService()::submitLoanResultQueryDelay);
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}

package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class RepayApplyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(RepayApplyListener.class);

    /*@Autowired
    private HtbkRepayService htbkRepayService;*/

    @Autowired
    private ManageService manageService;

    public RepayApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_APPLY)
    public void listenRepayApply(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听还款申请:{}", repayId);
            //htbkRepayService.repayApply(repayId);
            manageService.repayApply(repayId);
        } catch (Exception e) {
            logger.error("还款申请结果异常: ", e);
            processException(repayId, message, e, "还款申请结果异常", getMqService()::submitRepayApply);
        } finally {
            ackMsg(repayId, message, channel);
        }
    }
}

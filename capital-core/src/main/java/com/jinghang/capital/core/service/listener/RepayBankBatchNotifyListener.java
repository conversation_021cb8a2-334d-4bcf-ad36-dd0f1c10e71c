package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.BankProcessDTO;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 线下还款成功，批量推送资方
 * 处理分期乐-富民线下还款重推问题
 */
@Component
public class RepayBankBatchNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayBankBatchNotifyListener.class);

    private ManageService manageService;

    public RepayBankBatchNotifyListener(MqService mqService, WarningService mqWarningService, ManageService manageService) {
        super(mqService, mqWarningService);
        this.manageService = manageService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_BANK_BATCH_NOTIFY_QUEUE)
    public void repayBankNotify(Message message, Channel channel) {
        String processMsg = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("线下对客已成功,批量通知银行还款:{}", processMsg);

        try {
            BankProcessDTO bankProcessDTO = JsonUtil.convertToObject(processMsg, BankProcessDTO.class);

            manageService.repayBankBatchNotify(bankProcessDTO);
        } catch (Exception e) {
            logger.error("通知银行还款异常, processMsg: {} ", processMsg, e);
        } finally {
            ackMsg(processMsg, message, channel);
        }
    }


}

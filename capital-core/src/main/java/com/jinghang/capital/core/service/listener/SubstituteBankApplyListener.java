package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2024/6/22
 */
@Component
public class SubstituteBankApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(SubstituteBankApplyListener.class);

    private ManageService manageService;

    public SubstituteBankApplyListener(MqService mqService, WarningService mqWarningService, ManageService manageService) {
        super(mqService, mqWarningService);
        this.manageService = manageService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.SUBSTITUTE_BANK_APPLY_QUEUE)
    public void substituteBankNotify(Message message, Channel channel) {
        String substituteId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("请求资方代还申请, 代还记录Id: {}", substituteId);
            manageService.bankSubstituteApply(substituteId);
        } catch (Exception e) {
            logger.error("请求资方代还申请, bank_Batch_substitute_id: {} ", substituteId, e);
        } finally {
            ackMsg(substituteId, message, channel);
        }
    }


}

package com.jinghang.capital.core.service.listener.covenant;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.CovenantDownApplyDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 下载需申请下载的协议文件--申请
 */
@Component
public class CovenantDownloadAsyncApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CovenantDownloadAsyncApplyListener.class);

    @Autowired
    private ManageService manageService;

    public CovenantDownloadAsyncApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.COVENANT_DOWNLOAD_ASYNC_APPLY)
    public void covenantAsyncApply(Message message, Channel channel) {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("下载需申请下载的协议文件--申请: {}", json);

        try {
            CovenantDownApplyDto dto = JsonUtil.convertToObject(json, CovenantDownApplyDto.class);
            manageService.covenantDownloadAsyncApply(dto);
        } catch (Exception e) {
            logger.error("下载需申请下载的协议文件--申请 异常: {} ", json, e);
        } finally {
            ackMsg(json, message, channel);
        }
    }
}

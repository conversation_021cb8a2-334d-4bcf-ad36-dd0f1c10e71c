package com.jinghang.capital.core.service.listener.covenant;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 放款后下载借款合同
 */
@Component
public class CovenantDownloadLoanContractListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CovenantDownloadLoanContractListener.class);

    @Autowired
    private ManageService manageService;

    public CovenantDownloadLoanContractListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.COVENANT_DOWNLOAD_LOAN_CONTRACT)
    public void downloadLoanContract(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("放款后下载借款合同: {}", loanId);

        try {
            manageService.covenantDownloadLoanContract(loanId);
        } catch (Exception e) {
            logger.error("放款后下载借款合同 异常: {} ", loanId, e);
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}

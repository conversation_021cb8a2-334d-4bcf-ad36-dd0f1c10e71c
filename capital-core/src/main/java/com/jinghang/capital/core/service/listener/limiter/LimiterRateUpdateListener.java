package com.jinghang.capital.core.service.listener.limiter;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.limiter.BankRateLimiterService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class LimiterRateUpdateListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(LimiterRateUpdateListener.class);

    public LimiterRateUpdateListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private BankRateLimiterService limiterService;

    @RabbitListener(queues = RabbitConfig.Queues.LIMITER_RATE_UPDATE)
    public void limiterRateUpdate(Message message, Channel channel) {
        String configId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("限流器限制频率更新: {}", configId);
            limiterService.updateRate(configId);
        } catch (Exception e) {
            logger.error("限流器限制频率更新异常，configId: {}", configId, e);
        } finally {
            ackMsg(configId, message, channel);
        }
    }
}

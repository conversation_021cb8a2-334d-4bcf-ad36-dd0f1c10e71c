package com.jinghang.capital.core.service.remote.nfsp;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class NfspBizException extends Exception {

    public static final NfspBizException NOT_EXIST = new NfspBizException("02001", "订单不存在");

    private String code;

    private String msg;

    public NfspBizException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

package com.jinghang.capital.core.service.remote.nfsp;

/**
 * 定义公共平台调用
 */
public enum NfspCommonRequestType {

    AGREEMENT_SIGN_APPLY("协议签署", "trustSign/allSignContract"),

    AGREEMENT_DYNAMIC_SIGN_APPLY("动态签章", "trustSign/signContractDynamicFile"),

    AGREEMENT_SIGN_RESULT_QUERY("签署结果查询", "trustSign/getContractResult"),

    AGREEMENT_WITHHOLD_APPLY("协议扣款", "pay/agreement/withholdUseThirdPartyAgrNo"),

    AGREEMENT_WITHHOLD_RESULT_QUERY("扣款结果查询", "pay/queryOrder");


    private final String desc;

    private final String path;

    NfspCommonRequestType(String desc, String path) {
        this.desc = desc;
        this.path = path;
    }

    public String getDesc() {
        return desc;
    }

    public String getPath() {
        return path;
    }
}

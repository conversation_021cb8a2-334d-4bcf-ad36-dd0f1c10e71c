package com.jinghang.capital.core.service.remote.nfsp.sign;


import com.jinghang.capital.core.service.remote.nfsp.sign.req.SignApplyReq;
import com.jinghang.capital.core.service.remote.nfsp.sign.res.ResultMsg;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "sing-service")
public interface SignService {

    //个人认证+签章
    @PostMapping({"/esign/sign"})
    ResultMsg sign(@RequestBody SignApplyReq contractNo);

    //esign
    @GetMapping({"/esign/getSignUrl"})
    ResultMsg getSignUrl(@RequestParam("contractNo") String contractNo);
}

/**
 * Copyright 2025 json.cn
 */
package com.jinghang.capital.core.service.remote.nfsp.sign.req;

/**
 * Auto-generated: 2025-05-28 19:55:45
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/
 */
public class AuthDto {
    private String eviName;
    private String realNameMethod;
    private long timeStamp;
    private String name;
    private String cardNo;
    private String cardType;
    private String cardPortrait;
    private String cardNationalEmblem;
    private Face face;


    public String getCardNationalEmblem() {
        return cardNationalEmblem;
    }

    public void setCardNationalEmblem(String cardNationalEmblem) {
        this.cardNationalEmblem = cardNationalEmblem;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardPortrait() {
        return cardPortrait;
    }

    public void setCardPortrait(String cardPortrait) {
        this.cardPortrait = cardPortrait;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getEviName() {
        return eviName;
    }

    public void setEviName(String eviName) {
        this.eviName = eviName;
    }

    public Face getFace() {
        return face;
    }

    public void setFace(Face face) {
        this.face = face;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealNameMethod() {
        return realNameMethod;
    }

    public void setRealNameMethod(String realNameMethod) {
        this.realNameMethod = realNameMethod;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }
}
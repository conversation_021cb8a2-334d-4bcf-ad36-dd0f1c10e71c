/**
  * Copyright 2025 json.cn 
  */
package com.jinghang.capital.core.service.remote.nfsp.sign.req;

/**
 * Auto-generated: 2025-05-28 19:53:28
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/
 */
public class SignApplyReq {
    private String authNo;
    private String acctName;
    private String address;
    private String amount;
    private AuthDto authDto;
    private String bankCardNo;
    private String bankName;
    private String capitalRmb;
    private String contractCode;
  /**
   * 流量标识 绿信 LVXIN 拍拍 PPCJDL
   */
  private String trafficCode;
    private String creditId;
    private String dueDay;
    private String dueMonth;
    private String dueYear;
    private String guarOdIntRate;
    private String intRate;
    private String loanActvDay;
    private String loanActvMonth;
    private String loanActvYear;
    private String lowercaseRmb;
    private String mtdCde;
    private String phone;
    private String priceRate;
    private String purpose;
    private String repayAmount;
    private String repayDate;
    private String stagesNumber;

    private String  serviceFee;

    public String getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(String serviceFee) {
        this.serviceFee = serviceFee;
    }

    public void setAcctName(String acctName) {
         this.acctName = acctName;
     }
     public String getAcctName() {
         return acctName;
     }

    public void setAddress(String address) {
         this.address = address;
     }
     public String getAddress() {
         return address;
     }

    public void setAmount(String amount) {
         this.amount = amount;
     }
     public String getAmount() {
         return amount;
     }

    public void setAuthDto(AuthDto authDto) {
         this.authDto = authDto;
     }
     public AuthDto getAuthDto() {
         return authDto;
     }

    public void setBankCardNo(String bankCardNo) {
         this.bankCardNo = bankCardNo;
     }
     public String getBankCardNo() {
         return bankCardNo;
     }

    public void setBankName(String bankName) {
         this.bankName = bankName;
     }
     public String getBankName() {
         return bankName;
     }

    public void setCapitalRmb(String capitalRmb) {
         this.capitalRmb = capitalRmb;
     }
     public String getCapitalRmb() {
         return capitalRmb;
     }

    public void setContractCode(String contractCode) {
         this.contractCode = contractCode;
     }
     public String getContractCode() {
         return contractCode;
     }

  public String getTrafficCode() {
    return trafficCode;
  }

  public void setTrafficCode( String trafficCode ) {
    this.trafficCode = trafficCode;
  }

  public void setCreditId( String creditId ) {
         this.creditId = creditId;
     }
     public String getCreditId() {
         return creditId;
     }

    public void setDueDay(String dueDay) {
         this.dueDay = dueDay;
     }
     public String getDueDay() {
         return dueDay;
     }

    public void setDueMonth(String dueMonth) {
         this.dueMonth = dueMonth;
     }
     public String getDueMonth() {
         return dueMonth;
     }

    public void setDueYear(String dueYear) {
         this.dueYear = dueYear;
     }
     public String getDueYear() {
         return dueYear;
     }

    public void setGuarOdIntRate(String guarOdIntRate) {
         this.guarOdIntRate = guarOdIntRate;
     }
     public String getGuarOdIntRate() {
         return guarOdIntRate;
     }

    public void setIntRate(String intRate) {
         this.intRate = intRate;
     }
     public String getIntRate() {
         return intRate;
     }

    public void setLoanActvDay(String loanActvDay) {
         this.loanActvDay = loanActvDay;
     }
     public String getLoanActvDay() {
         return loanActvDay;
     }

    public void setLoanActvMonth(String loanActvMonth) {
         this.loanActvMonth = loanActvMonth;
     }
     public String getLoanActvMonth() {
         return loanActvMonth;
     }

    public void setLoanActvYear(String loanActvYear) {
         this.loanActvYear = loanActvYear;
     }
     public String getLoanActvYear() {
         return loanActvYear;
     }

    public void setLowercaseRmb(String lowercaseRmb) {
         this.lowercaseRmb = lowercaseRmb;
     }
     public String getLowercaseRmb() {
         return lowercaseRmb;
     }

    public void setMtdCde(String mtdCde) {
         this.mtdCde = mtdCde;
     }
     public String getMtdCde() {
         return mtdCde;
     }

    public void setPhone(String phone) {
         this.phone = phone;
     }
     public String getPhone() {
         return phone;
     }

    public void setPriceRate(String priceRate) {
         this.priceRate = priceRate;
     }
     public String getPriceRate() {
         return priceRate;
     }

    public void setPurpose(String purpose) {
         this.purpose = purpose;
     }
     public String getPurpose() {
         return purpose;
     }

    public void setRepayAmount(String repayAmount) {
         this.repayAmount = repayAmount;
     }
     public String getRepayAmount() {
         return repayAmount;
     }

    public void setRepayDate(String repayDate) {
         this.repayDate = repayDate;
     }
     public String getRepayDate() {
         return repayDate;
     }

    public void setStagesNumber(String stagesNumber) {
         this.stagesNumber = stagesNumber;
     }
     public String getStagesNumber() {
         return stagesNumber;
     }

    public String getAuthNo() {
        return authNo;
    }

    public void setAuthNo(String authNo) {
        this.authNo = authNo;
    }
}
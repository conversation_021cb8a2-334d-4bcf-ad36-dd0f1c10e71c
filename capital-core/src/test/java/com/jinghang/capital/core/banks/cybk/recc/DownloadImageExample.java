package com.jinghang.capital.core.banks.cybk.recc;

import com.cycfc.base.constant.ResponseCodeEnum;
import com.cycfc.base.vo.Header;
import com.cycfc.base.vo.Image;
import com.cycfc.base.vo.Response;
import com.cycfc.client.api.CycfcAPI;
import com.cycfc.client.util.Base64;
import org.junit.jupiter.api.Test;

import javax.imageio.stream.FileImageOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/24 9:44
 **/
public class DownloadImageExample {

    private static String aesKey = "****************";
    private static String signPrivateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMzQ98Ptl9OZ6gzJqNdNTJktb6Vy4IXTVZe+f81QSETfjt7o7xEXXZ215T3hiP7+USHRLJdpL9WoFbXnOJf9Ep44QzuQsRtyF/RM67tAfqxWuVwHZ69GXi8N9q1HuJn62HZDRaBdY8YG+ZVxMEqpBCH8Tju/VsNaioy3GWi6VEnpAgMBAAECgYBtDcsOwKA5o9cyLeG/mcHyQXYT4wHXX7pQTLdhy6BfQ6Wf3OkF8aoAAkzoUQEPvLvYRLb4e6JjodbFfzLsAmz5a+HHeGHTb/uPBB1rmriSwXSYpH1Mghj9W71ErF7tLRNnRFzWTWHnINBkYh/Qr1CUhWulx6aT8WkomN4iHsi8sQJBAOdbmzR0ef4dmh3cajfgWmwhIOpUU9qjXlVRHlSBcCoX9LYaesV+fTNB1u5TSoZqlHKU2OpDx8pMnhBZfg/JmaUCQQDioahXTB5xYYPoYPbXGN6JzAgiDrD1h/MOn5zrx4e/1In5qCrHSz917T8PsHh6kT/YvB0bIhVwSqc/PI2VeRP1AkEAmS91b2Dj2Va+UaE8jyy0kj3JLn6RWwWqkzD3tRpTKvvboqubuKJMwaCJcBmkEaYGDmvxyAMDCTZ7vfWTW16vpQJBAJSJs9YTs9hIO7Zw5xgv2puiOKppyp4CztGE18rJajXWzd/t0qvwq9VU/AICfdSHorshAEqqWjbRFzEhNQME6okCQQCmy/60tH5P+tOhWOlKeJaEerrV+qRyIY2llklwbUxhR0OnXXm7DXZW0M6vVVNPXmisO59tQ1Icayr+lF64eyJp";
    String url = "**************";
    int port = 18382;

    public static boolean downLoadFile(byte[] dataFile, String localPath, String fileName) {
        FileImageOutputStream imageOutput = null;
        try {
            File file = new File(localPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            File file2 = new File(localPath + "/" + fileName);
            imageOutput = new FileImageOutputStream(file2);
            imageOutput.write(dataFile, 0, dataFile.length);
            return true;
        } catch (Exception e) {
            return false;
        } finally {
            if (imageOutput != null) {
                try {
                    imageOutput.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Test
    public void download() {
        CycfcAPI.instance(url, port, aesKey, signPrivateKey);
        Header header = new Header();
        header.setChannel("900001");//渠道编号--由长银分配
        header.setAppID("C900001T1V1");//接入方系统ID-有长银分配
        header.setReqNo(UUID.randomUUID().toString());//请求方流水号-保证唯一

        Image image = new Image();
        image.setChannelId("900001");//渠道编号-由长银分配

        //长银流水号applCde和外部流水号outApplSeq不可同时为空，若均上送则以长银流水号为准
        image.setApplCde("13626010515110711014");//长银流水号--与外部流水号不可同时为空
        //image.setOutApplSeq("out202012301225");//外部流水号--与长银流水号不可同时为空

        image.setBusinessStage("1");//业务阶段(1 授信阶段 2 放款阶段)-使用外部流水号下载时必输
        image.setImgType("1");//影像类型-见分类表
        image.setIdNo("330201199304140139");//身份证号
        //下载影像文件
        Response response = CycfcAPI.downLoadImage(header, image);
        if (response != null && response.getRespCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            //处理成功
            String filename = response.getRespBody().get("fileName");
            byte[] fileContent = Base64.decodeBase64(response.getRespBody().get("data"));
            downLoadFile(fileContent, "D:/test/xx/", filename);
            //具体的文件业务处理逻辑
        } else {
            //错误消息
            String errorMsg = response.getRespMsg();
            //具体的错误处理逻辑
        }
    }


}

package com.jinghang.capital.core.service.loan;
import com.jinghang.capital.api.dto.EntrustedPay;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.api.dto.ProtocolChannel;
import com.jinghang.capital.core.vo.FileInfoVo;
import com.jinghang.capital.core.vo.credit.BankCardInfoVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.ppd.api.enums.PayChannel;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-11 16:19
 */
public class LoanApplyVoBuilder {
    private String sysId;
    private String sysCreditId;
    private String creditId;
    private String cardId;
    private String repayAgreementNo;
    private BigDecimal loanAmt;
    private Integer periods;
    private String loanApplyContractNo;
    private String guaranteeContractNo;
    private BigDecimal customRate;
    private LoanPurpose loanPurpose;
    private GuaranteeCompany guaranteeCompany;
    private String assistMode;
    private List<FileInfoVo> fileInfoVoList = new ArrayList<>();
    private BankCardInfoVo bankCardInfo;
    private String orderId;
    private String goodsName;
    private EntrustedPay entrustedPay;

    public static LoanApplyVoBuilder create() {
        return new LoanApplyVoBuilder();
    }

    public LoanApplyVoBuilder withSysId(String sysId) {
        this.sysId = sysId;
        return this;
    }

    public LoanApplyVoBuilder withSysCreditId(String sysCreditId) {
        this.sysCreditId = sysCreditId;
        return this;
    }

    public LoanApplyVoBuilder withCreditId(String creditId) {
        this.creditId = creditId;
        return this;
    }

    public LoanApplyVoBuilder withCardId(String cardId) {
        this.cardId = cardId;
        return this;
    }

    public LoanApplyVoBuilder withRepayAgreementNo(String repayAgreementNo) {
        this.repayAgreementNo = repayAgreementNo;
        return this;
    }

    public LoanApplyVoBuilder withLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
        return this;
    }

    public LoanApplyVoBuilder withLoanAmt(double loanAmt) {
        return withLoanAmt(BigDecimal.valueOf(loanAmt));
    }

    public LoanApplyVoBuilder withPeriods(Integer periods) {
        this.periods = periods;
        return this;
    }

    public LoanApplyVoBuilder withLoanApplyContractNo(String loanApplyContractNo) {
        this.loanApplyContractNo = loanApplyContractNo;
        return this;
    }

    public LoanApplyVoBuilder withGuaranteeContractNo(String guaranteeContractNo) {
        this.guaranteeContractNo = guaranteeContractNo;
        return this;
    }

    public LoanApplyVoBuilder withCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
        return this;
    }

    public LoanApplyVoBuilder withCustomRate(double customRate) {
        return withCustomRate(BigDecimal.valueOf(customRate));
    }

    public LoanApplyVoBuilder withLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
        return this;
    }

    public LoanApplyVoBuilder withGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
        return this;
    }

    public LoanApplyVoBuilder withAssistMode(String assistMode) {
        this.assistMode = assistMode;
        return this;
    }

    public LoanApplyVoBuilder withFileInfoVo(FileInfoVo fileInfoVo) {
        this.fileInfoVoList.add(fileInfoVo);
        return this;
    }

    public LoanApplyVoBuilder withFileInfoVoList(List<FileInfoVo> fileInfoVoList) {
        this.fileInfoVoList = fileInfoVoList;
        return this;
    }

    public LoanApplyVoBuilder withBankCardInfo(BankCardInfoVo bankCardInfo) {
        this.bankCardInfo = bankCardInfo;
        return this;
    }

    public LoanApplyVoBuilder withOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    public LoanApplyVoBuilder withGoodsName(String goodsName) {
        this.goodsName = goodsName;
        return this;
    }

    public LoanApplyVoBuilder withEntrustedPay(EntrustedPay entrustedPay) {
        this.entrustedPay = entrustedPay;
        return this;
    }

    public LoanApplyVo build() {
        LoanApplyVo vo = new LoanApplyVo();
        vo.setSysId(sysId);
        vo.setSysCreditId(sysCreditId);
        vo.setCreditId(creditId);
        vo.setCardId(cardId);
        vo.setRepayAgreementNo(repayAgreementNo);
        vo.setLoanAmt(loanAmt);
        vo.setPeriods(periods);
        vo.setLoanApplyContractNo(loanApplyContractNo);
        vo.setGuaranteeContractNo(guaranteeContractNo);
        vo.setCustomRate(customRate);
        vo.setLoanPurpose(loanPurpose);
        vo.setGuaranteeCompany(guaranteeCompany);
        vo.setAssistMode(assistMode);
        vo.setFileInfoVoList(fileInfoVoList);
        vo.setBankCardInfo(bankCardInfo);
        vo.setOrderId(orderId);
        vo.setGoodsName(goodsName);
        vo.setEntrustedPay(entrustedPay);
        return vo;
    }

    // 创建一个默认的测试对象
    public static LoanApplyVo defaultTestVo() {
        BankCardInfoVo bankCardInfoVo = new BankCardInfoVo();
        bankCardInfoVo.setBankCode("CMB");
        bankCardInfoVo.setBankName("招商银行");
        bankCardInfoVo.setCardName("贡真");
        bankCardInfoVo.setCardNo("****************");
        bankCardInfoVo.setCertNo("530621199311202793");//后四位脱敏
        bankCardInfoVo.setPayChannel(ProtocolChannel.BF);
        bankCardInfoVo.setPhone("***********");


        return LoanApplyVoBuilder.create()
                .withSysId("LOR25063017055788774781111574730")// loan_record.id
                .withSysCreditId("CR250630165723876743204529640117")// loan_record.credit_id
                .withGuaranteeCompany(GuaranteeCompany.CJRD)
                .withLoanAmt(10000.00)
                .withPeriods(12)
                .withLoanApplyContractNo("HT2620812612140930314102001")// loan_record.loan_contract_no
                .withLoanPurpose(LoanPurpose.SHOPPING)
                .withOrderId("OR250630165719447460640435344856")// loan_record.order_id
                .withBankCardInfo(bankCardInfoVo)
                .build();
    }

}


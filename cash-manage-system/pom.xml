<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jinghang.cash</groupId>
        <artifactId>loan-cash-manage</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>cash-manage-system</artifactId>
    <name>cash-manage-system</name>

    <properties>
        <jjwt.version>0.11.5</jjwt.version>
        <!-- oshi监控需要指定jna版本, 问题详见 https://github.com/oshi/oshi/issues/1040 -->
        <jna.version>5.8.0</jna.version>
    </properties>

    <dependencies>

        <!-- 代码生成模块 -->
        <dependency>
            <groupId>com.jinghang.cash</groupId>
            <artifactId>cash-manage-generator</artifactId>
            <version>1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.jinghang.cash</groupId>
                    <artifactId>cash-manage-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- API、common 和 logging 模块 -->
        <dependency>
            <groupId>com.jinghang.cash</groupId>
            <artifactId>cash-manage-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>com.jinghang.cash</groupId>
            <artifactId>cash-manage-common</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.jinghang.cash</groupId>
            <artifactId>cash-manage-logging</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- Spring boot websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- jwt -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jinghang.fin.api</groupId>
            <artifactId>loan-cash-flow-api</artifactId>
            <version>25.06.04</version>
        </dependency>

        <!-- 获取系统信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.1.4</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.7</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.3</version>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
    </dependencies>

    <!-- 打包 -->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>

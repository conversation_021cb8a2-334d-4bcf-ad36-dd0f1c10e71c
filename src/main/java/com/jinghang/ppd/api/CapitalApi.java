package com.jinghang.ppd.api;


import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.ResultCode;
import com.jinghang.ppd.api.dto.route.BankChannelResponseDTO;
import com.jinghang.ppd.api.dto.route.CapitalConfigDTO;
import com.jinghang.ppd.api.dto.route.FlowChannelResponseDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface CapitalApi {
    @PostMapping({"/queryCapitalConfigPage"})
    RestResult<PageInfo<CapitalConfigDTO>> queryCapitalConfigPage(@RequestParam("pageNum") Integer var1, @RequestParam("pageSize") Integer var2);

    @GetMapping({"/getCapitalConfigById"})
    RestResult<CapitalConfigDTO> getCapitalConfigById(@RequestParam("id") String var1);

    @PostMapping({"/saveOrUpdateCapitalConfig"})
    RestResult<ResultCode> saveOrUpdateCapitalConfig(@RequestBody CapitalConfigDTO var1);

    @GetMapping({"/queryCapital"})
    RestResult<List<BankChannelResponseDTO>> queryCapital();

    @GetMapping({"/getFlowConfig"})
    RestResult<List<FlowChannelResponseDTO>> getFlowConfig();

    @PostMapping({"/updateEnabled"})
    RestResult<ResultCode> updateEnabled(@RequestBody CapitalConfigDTO var1);
}
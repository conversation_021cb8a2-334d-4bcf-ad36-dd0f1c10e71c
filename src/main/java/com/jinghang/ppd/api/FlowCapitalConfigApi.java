package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.route.CapitalConfigDTO;
import com.jinghang.ppd.api.dto.route.EnableFlowConfigDTO;
import com.jinghang.ppd.api.dto.route.FlowRouteConfigDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface FlowCapitalConfigApi {
    @PostMapping({"/saveCapitalConfig"})
    void saveCapitalConfig(@RequestBody CapitalConfigDTO var1);

    @PostMapping({"/saveAllFlowRouteConfig"})
    RestResult<Boolean> saveAllFlowRouteConfig(@RequestBody FlowRouteConfigDTO var1);

    @PostMapping({"/enableFlowRouteConfig"})
    RestResult<Boolean> enableFlowConfig(@RequestBody EnableFlowConfigDTO var1);
}
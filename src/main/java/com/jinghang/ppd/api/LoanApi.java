package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.RestResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface LoanApi {
    @PostMapping({"loanFail"})
    RestResult<Void> loanFail(@RequestBody List<String> var1);

    @PostMapping({"loanRoute"})
    RestResult<Void> loanRoute(@RequestBody List<String> var1);

    @PostMapping({"reapplyLoan"})
    RestResult<Void> reapplyLoan(@RequestBody List<String> var1);
}

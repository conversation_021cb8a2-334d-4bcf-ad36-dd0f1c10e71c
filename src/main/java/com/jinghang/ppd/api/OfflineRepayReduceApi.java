package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.AuditDTO;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.UpdateOfflineRepayReduceDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface OfflineRepayReduceApi {

    /**
     * 后台减免订单-审核
     */
    @PostMapping("/audit")
    RestResult<String> audit(@RequestBody AuditDTO reqDTO);

    @PostMapping("/updateBatch")
    RestResult<String> updateBatch(@RequestBody UpdateOfflineRepayReduceDTO reqDTO);
}

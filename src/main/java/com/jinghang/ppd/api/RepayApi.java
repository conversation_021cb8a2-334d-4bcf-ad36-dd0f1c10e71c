package com.jinghang.ppd.api;


import com.jinghang.ppd.api.dto.RepayTrailDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 还款api
 */
public interface RepayApi {

    @PostMapping("trial")
    RestResult<TrailResultDto> trial(@RequestBody RepayTrailDto trailDto);

    /**
     * 减免申请
     * @param reduceApplyDto
     * @return
     */
    @PostMapping("reduceApply")
    RestResult<Void> reduceApply(@RequestBody ReduceApplyDto reduceApplyDto);

    /**
     * 还款销账
     * @param reduceApplyDto
     * @return
     */
    @PostMapping("repayApply")
    RestResult<Void> repayApply(@RequestBody RepayApplyDto reduceApplyDto);
}

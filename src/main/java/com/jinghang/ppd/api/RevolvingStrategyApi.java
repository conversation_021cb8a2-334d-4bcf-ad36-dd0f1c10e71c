package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategyChangeStatusDTO;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategyRelateSaveDTO;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategySaveDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface RevolvingStrategyApi {
    @PostMapping({"/save"})
    RestResult<Boolean> save(@RequestBody RevolvingStrategySaveDTO var1);

    @PostMapping({"/changeStatus"})
    RestResult<Boolean> changeStatus(@RequestBody RevolvingStrategyChangeStatusDTO var1);

    @PostMapping({"/relate/save"})
    RestResult<Boolean> relateSave(@RequestBody RevolvingStrategyRelateSaveDTO var1);
}

package com.jinghang.ppd.api.dto;

public class PartnerConfigResDTO {
    private String flowChannel;
    private String partnerDomainName;
    private String partnerOrgCode;
    private String partnerProductCode;
    private String partnerName;
    private String partnerPublicKey;
    private String partnerPrivateKey;
    private String serverPublicKey;
    private String serverPrivateKey;
    private String enable;

    public PartnerConfigResDTO() {
    }

    public String getEnable() {
        return this.enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getFlowChannel() {
        return this.flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getPartnerDomainName() {
        return this.partnerDomainName;
    }

    public void setPartnerDomainName(String partnerDomainName) {
        this.partnerDomainName = partnerDomainName;
    }

    public String getPartnerOrgCode() {
        return this.partnerOrgCode;
    }

    public void setPartnerOrgCode(String partnerOrgCode) {
        this.partnerOrgCode = partnerOrgCode;
    }

    public String getPartnerProductCode() {
        return this.partnerProductCode;
    }

    public void setPartnerProductCode(String partnerProductCode) {
        this.partnerProductCode = partnerProductCode;
    }

    public String getPartnerName() {
        return this.partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerPublicKey() {
        return this.partnerPublicKey;
    }

    public void setPartnerPublicKey(String partnerPublicKey) {
        this.partnerPublicKey = partnerPublicKey;
    }

    public String getPartnerPrivateKey() {
        return this.partnerPrivateKey;
    }

    public void setPartnerPrivateKey(String partnerPrivateKey) {
        this.partnerPrivateKey = partnerPrivateKey;
    }

    public String getServerPublicKey() {
        return this.serverPublicKey;
    }

    public void setServerPublicKey(String serverPublicKey) {
        this.serverPublicKey = serverPublicKey;
    }

    public String getServerPrivateKey() {
        return this.serverPrivateKey;
    }

    public void setServerPrivateKey(String serverPrivateKey) {
        this.serverPrivateKey = serverPrivateKey;
    }
}
package com.jinghang.ppd.api.dto;

public enum ResultCode {
    PARAM_ILLEGAL("100000", "参数不合法"),
    BIZ_ERROR("100001", "业务异常"),
    SYS_ERROR("999999", "系统异常"),
    SUCCESS("000000", "success");

    private final String code;
    private final String msg;

    private ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }
}
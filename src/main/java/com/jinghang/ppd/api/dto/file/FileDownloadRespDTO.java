package com.jinghang.ppd.api.dto.file;

public class FileDownloadRespDTO {
    private String ossBucket;
    private String ossPath;
    private String fileUrl;
    private String fileName;
    private String fileStatus;

    public FileDownloadRespDTO() {
    }

    public String getOssBucket() {
        return this.ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssPath() {
        return this.ossPath;
    }

    public void setOssPath(String ossPath) {
        this.ossPath = ossPath;
    }

    public String getFileUrl() {
        return this.fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileStatus() {
        return this.fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }
}
package com.jinghang.ppd.api.dto.loan;

import com.jinghang.ppd.api.dto.PageParam;

import java.time.LocalDateTime;

public class LoanQueryRequestDTO extends PageParam {
    private String mobile;
    private String loanId;
    private String certNo;
    private String flowChannel;
    private LocalDateTime startTime;
    private LocalDateTime endTime;

    public LoanQueryRequestDTO() {
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getCertNo() {
        return this.certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getFlowChannel() {
        return this.flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public LocalDateTime getStartTime() {
        return this.startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return this.endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}

package com.jinghang.ppd.api.dto.loan;

import java.math.BigDecimal;
import java.util.Date;

public class LoanQueryResponseDTO {
    private String loanId;
    private String mobile;
    private Integer periods;
    private BigDecimal interestRate;
    private BigDecimal approvedAmount;
    private BigDecimal loanAmount;
    private String funder;
    private String flowChannel;
    private String loanStatus;
    private Date applyDate;
    private Date requestFundsDate;
    private Date loanDate;
    private String isClear;
    private String isLoanState;

    public LoanQueryResponseDTO() {
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getPeriods() {
        return this.periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public BigDecimal getInterestRate() {
        return this.interestRate;
    }

    public void setInterestRate(BigDecimal interestRate) {
        this.interestRate = interestRate;
    }

    public BigDecimal getApprovedAmount() {
        return this.approvedAmount;
    }

    public void setApprovedAmount(BigDecimal approvedAmount) {
        this.approvedAmount = approvedAmount;
    }

    public BigDecimal getLoanAmount() {
        return this.loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getFunder() {
        return this.funder;
    }

    public void setFunder(String funder) {
        this.funder = funder;
    }

    public String getFlowChannel() {
        return this.flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getLoanStatus() {
        return this.loanStatus;
    }

    public void setLoanStatus(String loanStatus) {
        this.loanStatus = loanStatus;
    }

    public Date getApplyDate() {
        return this.applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getRequestFundsDate() {
        return this.requestFundsDate;
    }

    public void setRequestFundsDate(Date requestFundsDate) {
        this.requestFundsDate = requestFundsDate;
    }

    public Date getLoanDate() {
        return this.loanDate;
    }

    public void setLoanDate(Date loanDate) {
        this.loanDate = loanDate;
    }

    public String getIsClear() {
        return this.isClear;
    }

    public void setIsClear(String isClear) {
        this.isClear = isClear;
    }

    public String getIsLoanState() {
        return this.isLoanState;
    }

    public void setIsLoanState(String isLoanState) {
        this.isLoanState = isLoanState;
    }
}

package com.jinghang.ppd.api.dto.loan;

import com.jinghang.ppd.api.dto.PageParam;

import java.util.Date;

public class ReduceLoanQueryRequestDTO extends PageParam {
    private String mobile;
    private String loanReqNo;
    private String certNo;
    private String flowChannel;
    private Date startTime;
    private Date endTime;

    public ReduceLoanQueryRequestDTO() {
    }

    public String getMobile() {
        return this.mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLoanReqNo() {
        return this.loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getCertNo() {
        return this.certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getFlowChannel() {
        return this.flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}

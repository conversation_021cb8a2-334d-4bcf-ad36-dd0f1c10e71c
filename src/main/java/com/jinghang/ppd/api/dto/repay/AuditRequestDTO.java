package com.jinghang.ppd.api.dto.repay;

import com.jinghang.ppd.api.enums.AuditStatus;

import java.io.Serializable;

public class AuditRequestDTO implements Serializable {
    private static final long serialVersionUID = 35682504665199402L;
    private String reduceId;
    private AuditStatus auditState;
    private String auditor;

    public AuditRequestDTO() {
    }

    public String getAuditor() {
        return this.auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getReduceId() {
        return this.reduceId;
    }

    public void setReduceId(String reduceId) {
        this.reduceId = reduceId;
    }

    public AuditStatus getAuditState() {
        return this.auditState;
    }

    public void setAuditState(AuditStatus auditState) {
        this.auditState = auditState;
    }
}
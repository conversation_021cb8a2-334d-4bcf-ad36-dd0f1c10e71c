package com.jinghang.ppd.api.dto.repay;


import com.jinghang.ppd.api.enums.RepayPurpose;

public class OfflineRepayApplyRequestDTO {
    private RepayPurpose repayPurpose;
    private Integer period;
    private String loanId;
    private String remark;
    private String operator;

    public OfflineRepayApplyRequestDTO() {
    }

    public RepayPurpose getRepayPurpose() {
        return this.repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

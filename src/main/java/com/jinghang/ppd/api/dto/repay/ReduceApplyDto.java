package com.jinghang.ppd.api.dto.repay;


import com.jinghang.ppd.api.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ReduceApplyDto {

    /**
     * 还款方式 结清 或者当期
     */
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 减免备注
     */
    private String remark;


    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

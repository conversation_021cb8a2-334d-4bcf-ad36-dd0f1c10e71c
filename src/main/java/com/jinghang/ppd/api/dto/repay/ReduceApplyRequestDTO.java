package com.jinghang.ppd.api.dto.repay;

import com.jinghang.ppd.api.enums.RepayPurpose;

import java.math.BigDecimal;

public class ReduceApplyRequestDTO {
    private RepayPurpose repayPurpose;
    private Integer period;
    private String loanId;
    private BigDecimal reduceAmount;
    private String remark;
    private String operator;

    public ReduceApplyRequestDTO() {
    }

    public RepayPurpose getRepayPurpose() {
        return this.repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getReduceAmount() {
        return this.reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

package com.jinghang.ppd.api.dto.repay;


import com.jinghang.ppd.api.enums.AuditStatus;
import com.jinghang.ppd.api.enums.RepayPurpose;
import com.jinghang.ppd.api.enums.UseState;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class ReduceOrderQueryResponseDTO implements Serializable {
    private static final long serialVersionUID = 6896392764010374387L;
    private String reduceId;
    private String loanId;
    private BigDecimal principalAmt;
    private Integer period;
    private BigDecimal reduceAmount;
    private RepayPurpose repayPurpose;
    private UseState useState;
    private AuditStatus auditState;
    private String createdBy;
    private String updatedBy;
    private String remark;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;

    public ReduceOrderQueryResponseDTO() {
    }

    public String getReduceId() {
        return this.reduceId;
    }

    public void setReduceId(String reduceId) {
        this.reduceId = reduceId;
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getPrincipalAmt() {
        return this.principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getReduceAmount() {
        return this.reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public RepayPurpose getRepayPurpose() {
        return this.repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public UseState getUseState() {
        return this.useState;
    }

    public void setUseState(UseState useState) {
        this.useState = useState;
    }

    public AuditStatus getAuditState() {
        return this.auditState;
    }

    public void setAuditState(AuditStatus auditState) {
        this.auditState = auditState;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return this.updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}

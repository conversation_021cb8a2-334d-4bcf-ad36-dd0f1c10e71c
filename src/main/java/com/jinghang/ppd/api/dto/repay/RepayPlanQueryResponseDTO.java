package com.jinghang.ppd.api.dto.repay;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

public class RepayPlanQueryResponseDTO {
    private String loanId;
    private String userName;
    private String certNo;
    private List<RepayPlanDto> repayPlans;

    public RepayPlanQueryResponseDTO() {
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCertNo() {
        return this.certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public List<RepayPlanDto> getRepayPlans() {
        return this.repayPlans;
    }

    public void setRepayPlans(List<RepayPlanDto> repayPlans) {
        this.repayPlans = repayPlans;
    }

    public static class RepayPlanDto {
        private Integer period;
        private String repayStatus;
        private LocalDate dueDate;
        private BigDecimal totalAmount;
        private BigDecimal principal;
        private BigDecimal interest;
        private BigDecimal penalty;
        private BigDecimal zhibao;
        private BigDecimal consultFee;
        private LocalDateTime paidTime;
        private BigDecimal actAmount;
        private BigDecimal paidPrincipal;
        private BigDecimal paidInterest;
        private BigDecimal paidPenalty;
        private BigDecimal paidZhibao;
        private BigDecimal paidConsultFee;
        private BigDecimal deductionInterest;
        private BigDecimal deductionOverdueInterest;
        private BigDecimal deductionGuaranteeAmt;
        private BigDecimal deductionConsultFee;

        public RepayPlanDto() {
        }

        public BigDecimal getTotalAmount() {
            return this.totalAmount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public Integer getPeriod() {
            return this.period;
        }

        public void setPeriod(Integer period) {
            this.period = period;
        }

        public String getRepayStatus() {
            return this.repayStatus;
        }

        public void setRepayStatus(String repayStatus) {
            this.repayStatus = repayStatus;
        }

        public LocalDate getDueDate() {
            return this.dueDate;
        }

        public void setDueDate(LocalDate dueDate) {
            this.dueDate = dueDate;
        }

        public BigDecimal getPrincipal() {
            return this.principal;
        }

        public void setPrincipal(BigDecimal principal) {
            this.principal = principal;
        }

        public BigDecimal getInterest() {
            return this.interest;
        }

        public void setInterest(BigDecimal interest) {
            this.interest = interest;
        }

        public BigDecimal getPenalty() {
            return this.penalty;
        }

        public void setPenalty(BigDecimal penalty) {
            this.penalty = penalty;
        }

        public BigDecimal getZhibao() {
            return this.zhibao;
        }

        public void setZhibao(BigDecimal zhibao) {
            this.zhibao = zhibao;
        }

        public BigDecimal getConsultFee() {
            return this.consultFee;
        }

        public void setConsultFee(BigDecimal consultFee) {
            this.consultFee = consultFee;
        }

        public LocalDateTime getPaidTime() {
            return this.paidTime;
        }

        public void setPaidTime(LocalDateTime paidTime) {
            this.paidTime = paidTime;
        }

        public BigDecimal getPaidPrincipal() {
            return this.paidPrincipal;
        }

        public void setPaidPrincipal(BigDecimal paidPrincipal) {
            this.paidPrincipal = paidPrincipal;
        }

        public BigDecimal getPaidInterest() {
            return this.paidInterest;
        }

        public void setPaidInterest(BigDecimal paidInterest) {
            this.paidInterest = paidInterest;
        }

        public BigDecimal getPaidPenalty() {
            return this.paidPenalty;
        }

        public void setPaidPenalty(BigDecimal paidPenalty) {
            this.paidPenalty = paidPenalty;
        }

        public BigDecimal getPaidZhibao() {
            return this.paidZhibao;
        }

        public void setPaidZhibao(BigDecimal paidZhibao) {
            this.paidZhibao = paidZhibao;
        }

        public BigDecimal getPaidConsultFee() {
            return this.paidConsultFee;
        }

        public void setPaidConsultFee(BigDecimal paidConsultFee) {
            this.paidConsultFee = paidConsultFee;
        }

        public BigDecimal getDeductionInterest() {
            return this.deductionInterest;
        }

        public void setDeductionInterest(BigDecimal deductionInterest) {
            this.deductionInterest = deductionInterest;
        }

        public BigDecimal getDeductionOverdueInterest() {
            return this.deductionOverdueInterest;
        }

        public void setDeductionOverdueInterest(BigDecimal deductionOverdueInterest) {
            this.deductionOverdueInterest = deductionOverdueInterest;
        }

        public BigDecimal getDeductionGuaranteeAmt() {
            return this.deductionGuaranteeAmt;
        }

        public void setDeductionGuaranteeAmt(BigDecimal deductionGuaranteeAmt) {
            this.deductionGuaranteeAmt = deductionGuaranteeAmt;
        }

        public BigDecimal getDeductionConsultFee() {
            return this.deductionConsultFee;
        }

        public void setDeductionConsultFee(BigDecimal deductionConsultFee) {
            this.deductionConsultFee = deductionConsultFee;
        }

        public BigDecimal getActAmount() {
            return this.actAmount;
        }

        public void setActAmount(BigDecimal actAmount) {
            this.actAmount = actAmount;
        }
    }
}
package com.jinghang.ppd.api.dto.repay;

import com.jinghang.ppd.api.enums.RepayPurpose;

import java.io.Serializable;

public class RepayTrailRequestDTO implements Serializable {
    private static final long serialVersionUID = 35682504665199402L;
    private String loanId;
    private Integer period;
    private RepayPurpose repayPurpose;

    public RepayTrailRequestDTO() {
    }

    public String getLoanId() {
        return this.loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return this.repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }
}

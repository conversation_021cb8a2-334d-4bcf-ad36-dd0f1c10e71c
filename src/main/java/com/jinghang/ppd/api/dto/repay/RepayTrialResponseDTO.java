package com.jinghang.ppd.api.dto.repay;

import java.math.BigDecimal;

public class RepayTrialResponseDTO {
    private BigDecimal totalAmt;
    private BigDecimal principalAmt;
    private BigDecimal interestAmt;
    private BigDecimal guaranteeAmt;
    private BigDecimal consultAmt;
    private BigDecimal penaltyAmt;
    private Integer period;
    private String planId;

    public RepayTrialResponseDTO() {
        this.totalAmt = BigDecimal.ZERO;
        this.principalAmt = BigDecimal.ZERO;
        this.interestAmt = BigDecimal.ZERO;
        this.guaranteeAmt = BigDecimal.ZERO;
        this.consultAmt = BigDecimal.ZERO;
        this.penaltyAmt = BigDecimal.ZERO;
    }

    public BigDecimal getTotalAmt() {
        return this.totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return this.principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return this.interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return this.guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return this.consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return this.penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getPlanId() {
        return this.planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }
}

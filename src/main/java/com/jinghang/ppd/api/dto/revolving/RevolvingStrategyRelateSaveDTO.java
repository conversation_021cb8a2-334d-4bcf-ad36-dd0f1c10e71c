package com.jinghang.ppd.api.dto.revolving;

import java.math.BigDecimal;
import java.util.List;

public class RevolvingStrategyRelateSaveDTO {
    private String strategyId;
    private List<Relate> relateDetail;
    private String operator;

    public RevolvingStrategyRelateSaveDTO() {
    }

    public String getStrategyId() {
        return this.strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public List<Relate> getRelateDetail() {
        return this.relateDetail;
    }

    public void setBinningDetail(List<Relate> relateDetail) {
        this.relateDetail = relateDetail;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public static class Relate {
        private String id;
        private String relateCode;
        private BigDecimal frontAmount;
        private BigDecimal ratio;

        public Relate() {
        }

        public String getId() {
            return this.id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getRelateCode() {
            return this.relateCode;
        }

        public void setRelateCode(String relateCode) {
            this.relateCode = relateCode;
        }

        public BigDecimal getFrontAmount() {
            return this.frontAmount;
        }

        public void setFrontAmount(BigDecimal frontAmount) {
            this.frontAmount = frontAmount;
        }

        public BigDecimal getRatio() {
            return this.ratio;
        }

        public void setRatio(BigDecimal ratio) {
            this.ratio = ratio;
        }
    }
}
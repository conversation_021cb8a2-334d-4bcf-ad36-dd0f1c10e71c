package com.jinghang.ppd.api.dto.revolving;

public class RevolvingStrategySaveDTO {
    private String id;
    private String strategyName;
    private String strategyCode;
    private Integer amountUpper;
    private Integer amountLower;
    private String operator;

    public RevolvingStrategySaveDTO() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStrategyName() {
        return this.strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public String getStrategyCode() {
        return this.strategyCode;
    }

    public void setStrategyCode(String strategyCode) {
        this.strategyCode = strategyCode;
    }

    public Integer getAmountUpper() {
        return this.amountUpper;
    }

    public void setAmountUpper(Integer amountUpper) {
        this.amountUpper = amountUpper;
    }

    public Integer getAmountLower() {
        return this.amountLower;
    }

    public void setAmountLower(Integer amountLower) {
        this.amountLower = amountLower;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
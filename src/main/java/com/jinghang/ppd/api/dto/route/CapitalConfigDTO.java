
package com.jinghang.ppd.api.dto.route;
import com.jinghang.ppd.api.enums.AbleStatus;
import com.jinghang.ppd.api.enums.ProtocolChannel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class CapitalConfigDTO implements Serializable {
    private static final long serialVersionUID = 1191105899990594453L;
    private String id;
    private String enabled;
    private String periodsRange;
    private String bankChannel;
    private String guaranteeCompany;
    private BigDecimal bankRate;
    private String supportIrrLevel;
    private BigDecimal creditDayLimit;
    private BigDecimal loanDayLimit;
    private String agesRange;
    private String singleAmtRange;
    private AbleStatus creditTimeStatus;
    private AbleStatus loanTimeStatus;
    private AbleStatus repayTimeStatus;
    private String creditStartTime;
    private String creditEndTime;
    private String loanStartTime;
    private String loanEndTime;
    private String repayStartTime;
    private String repayEndTime;
    private String renewedFlag;
    private ProtocolChannel protocolChannel;
    private String remark;
    private Integer revision;
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;

    public CapitalConfigDTO() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEnabled() {
        return this.enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getBankChannel() {
        return this.bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public BigDecimal getBankRate() {
        return this.bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public String getSupportIrrLevel() {
        return this.supportIrrLevel;
    }

    public void setSupportIrrLevel(String supportIrrLevel) {
        this.supportIrrLevel = supportIrrLevel;
    }

    public BigDecimal getCreditDayLimit() {
        return this.creditDayLimit;
    }

    public void setCreditDayLimit(BigDecimal creditDayLimit) {
        this.creditDayLimit = creditDayLimit;
    }

    public BigDecimal getLoanDayLimit() {
        return this.loanDayLimit;
    }

    public void setLoanDayLimit(BigDecimal loanDayLimit) {
        this.loanDayLimit = loanDayLimit;
    }

    public String getAgesRange() {
        return this.agesRange;
    }

    public void setAgesRange(String agesRange) {
        this.agesRange = agesRange;
    }

    public String getSingleAmtRange() {
        return this.singleAmtRange;
    }

    public void setSingleAmtRange(String singleAmtRange) {
        this.singleAmtRange = singleAmtRange;
    }

    public String getCreditStartTime() {
        return this.creditStartTime;
    }

    public void setCreditStartTime(String creditStartTime) {
        this.creditStartTime = creditStartTime;
    }

    public String getCreditEndTime() {
        return this.creditEndTime;
    }

    public void setCreditEndTime(String creditEndTime) {
        this.creditEndTime = creditEndTime;
    }

    public String getLoanStartTime() {
        return this.loanStartTime;
    }

    public void setLoanStartTime(String loanStartTime) {
        this.loanStartTime = loanStartTime;
    }

    public String getLoanEndTime() {
        return this.loanEndTime;
    }

    public void setLoanEndTime(String loanEndTime) {
        this.loanEndTime = loanEndTime;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return this.revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return this.updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getRepayStartTime() {
        return this.repayStartTime;
    }

    public void setRepayStartTime(String repayStartTime) {
        this.repayStartTime = repayStartTime;
    }

    public String getRepayEndTime() {
        return this.repayEndTime;
    }

    public void setRepayEndTime(String repayEndTime) {
        this.repayEndTime = repayEndTime;
    }

    public String getPeriodsRange() {
        return this.periodsRange;
    }

    public void setPeriodsRange(String periodsRange) {
        this.periodsRange = periodsRange;
    }

    public AbleStatus getCreditTimeStatus() {
        return this.creditTimeStatus;
    }

    public void setCreditTimeStatus(AbleStatus creditTimeStatus) {
        this.creditTimeStatus = creditTimeStatus;
    }

    public AbleStatus getLoanTimeStatus() {
        return this.loanTimeStatus;
    }

    public void setLoanTimeStatus(AbleStatus loanTimeStatus) {
        this.loanTimeStatus = loanTimeStatus;
    }

    public AbleStatus getRepayTimeStatus() {
        return this.repayTimeStatus;
    }

    public void setRepayTimeStatus(AbleStatus repayTimeStatus) {
        this.repayTimeStatus = repayTimeStatus;
    }

    public String getGuaranteeCompany() {
        return this.guaranteeCompany;
    }

    public void setGuaranteeCompany(String guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getRenewedFlag() {
        return this.renewedFlag;
    }

    public void setRenewedFlag(String renewedFlag) {
        this.renewedFlag = renewedFlag;
    }

    public ProtocolChannel getProtocolChannel() {
        return this.protocolChannel;
    }

    public void setProtocolChannel(ProtocolChannel protocolChannel) {
        this.protocolChannel = protocolChannel;
    }
}

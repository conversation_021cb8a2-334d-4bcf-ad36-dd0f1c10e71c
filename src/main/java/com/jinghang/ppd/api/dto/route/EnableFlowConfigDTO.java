package com.jinghang.ppd.api.dto.route;

import java.io.Serializable;

public class EnableFlowConfigDTO implements Serializable {
    private static final long serialVersionUID = 4556630746545626669L;
    private String flowConfigId;
    private String enabled;
    private String operator;

    public EnableFlowConfigDTO() {
    }

    public String getFlowConfigId() {
        return this.flowConfigId;
    }

    public void setFlowConfigId(String flowConfigId) {
        this.flowConfigId = flowConfigId;
    }

    public String getEnabled() {
        return this.enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}

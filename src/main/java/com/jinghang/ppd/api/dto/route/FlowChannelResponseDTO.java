package com.jinghang.ppd.api.dto.route;

import java.io.Serializable;

public class FlowChannelResponseDTO implements Serializable {
    private static final long serialVersionUID = 7985390078191296379L;
    private String id;
    private String name;
    private String desc;
    private Boolean exist;

    public FlowChannelResponseDTO() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Boolean getExist() {
        return this.exist;
    }

    public void setExist(Boolean exist) {
        this.exist = exist;
    }
}
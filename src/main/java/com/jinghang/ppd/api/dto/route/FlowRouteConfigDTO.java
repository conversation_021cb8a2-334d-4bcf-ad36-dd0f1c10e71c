//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.jinghang.ppd.api.dto.route;

import com.jinghang.ppd.api.enums.ValidStatus;

import java.io.Serializable;
import java.util.List;

public class FlowRouteConfigDTO implements Serializable {
    private static final long serialVersionUID = -1977045335946483162L;
    private String flowId;
    private List<FlowRouteConfigReq> list;

    public FlowRouteConfigDTO() {
    }

    public String getFlowId() {
        return this.flowId;
    }

    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    public List<FlowRouteConfigReq> getList() {
        return this.list;
    }

    public void setList(List<FlowRouteConfigReq> list) {
        this.list = list;
    }

    public static class FlowRouteConfigReq {
        private String id;
        private String capitalId;
        private String enabled;
        private Integer priority;
        private String operator;
        private ValidStatus valid;

        public FlowRouteConfigReq() {
        }

        public String getOperator() {
            return this.operator;
        }

        public void setOperator(String operator) {
            this.operator = operator;
        }

        public String getId() {
            return this.id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCapitalId() {
            return this.capitalId;
        }

        public void setCapitalId(String capitalId) {
            this.capitalId = capitalId;
        }

        public String getEnabled() {
            return this.enabled;
        }

        public Integer getPriority() {
            return this.priority;
        }

        public void setPriority(Integer priority) {
            this.priority = priority;
        }

        public void setEnabled(String enabled) {
            this.enabled = enabled;
        }

        public ValidStatus getValid() {
            return this.valid;
        }

        public void setValid(ValidStatus valid) {
            this.valid = valid;
        }
    }
}

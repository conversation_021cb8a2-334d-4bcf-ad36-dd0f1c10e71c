package com.jinghang.ppd.api.enums;

public enum ResultCode {
    SUCCESS("1000", "成功"),
    DECRYPT_FAIL("1001", "参数解密异常"),
    ENCRYPT_FAIL("1002", "响应加密异常"),
    SIGN_FAIL("1003", "验签失败"),
    SYS_FAIL("1004", "系统异常"),
    PARAM_FAIL("1006", "请求异常"),
    BUSINESS_FAIL("1005", "业务异常");

    private final String code;
    private final String msg;

    private ResultCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }
}
package com.maguo.loan.cash.flow.aop;

import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.maguo.loan.cash.flow.entity.BatchTaskMonitoring;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.BatchTaskMonitoringRepository;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class AlarmAspect {
    @Pointcut("@annotation( com.maguo.loan.cash.flow.annotation.AlarmAnno)")
    public void pointCut() {
    }
    private static final Logger logger = LoggerFactory.getLogger(AlarmAspect.class);

    @Autowired
    BatchTaskMonitoringRepository batchTaskMonitoringRepository;


    /**
     * 方法调用成功返回后，将信息保存到表中
     */
    @AfterReturning(value = "pointCut()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            AlarmAnno annotation = getMethodAnnotation(joinPoint);
            JobParam jobParam = extractJobParam(joinPoint);
            saveTaskMonitoringData(jobParam, annotation, null, true);
        } catch (Exception e) {
            logger.error("AfterReturning advice processing failed", e);
        }
    }

    /**
     * 异常处理：
     * ①如果为正常请求调用，则记录一条请求调用失败记录
     * ②如果为重试调用异常，则记录重试调用失败记录
     */
    @AfterThrowing(value = "pointCut()", throwing = "exception")
    public void afterThrowing(JoinPoint joinPoint, Exception exception) {
        try {
            AlarmAnno annotation = getMethodAnnotation(joinPoint);
            JobParam jobParam = extractJobParam(joinPoint);
            saveTaskMonitoringData(jobParam, annotation, exception, false);
        } catch (Exception e) {
            logger.error("AfterThrowing advice processing failed", e);
        }
    }

    /**
     * 从JoinPoint中获取AlarmAnno注解
     */
    private AlarmAnno getMethodAnnotation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getMethod().getAnnotation(AlarmAnno.class);
    }

    /**
     * 从JoinPoint参数中提取JobParam
     */
    private JobParam extractJobParam(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0 || !(args[0] instanceof JobParam)) {
            throw new IllegalArgumentException("JobParam not found in method arguments");
        }
        return (JobParam) args[0];
    }

    /**
     * 保存任务监控数据到表中
     */
    private void saveTaskMonitoringData(JobParam jobParam, AlarmAnno annotation,
                                        Exception exception, boolean success) {
        if (jobParam == null || annotation == null) {
            logger.warn("Invalid arguments for saving task monitoring data");
            return;
        }

        logger.debug("Saving task monitoring data - jobParam: {}, annotation: {}, exception: {}",
            jobParam, annotation, exception);

        BatchTaskMonitoring task = new BatchTaskMonitoring();
        task.setTaskHandler(annotation.taskHandler());
        task.setTaskDescription(annotation.taskDescription());
        task.setBankChannel(jobParam.getBankChannel());
        task.setFlowChannel(jobParam.getFlowChannel());
        task.setTaskStartTime(jobParam.getStartDate());
        task.setTaskEndTime(jobParam.getEndDate());
        task.setResultsExecution(success ? "SUCCEED" : "FAIL");
        try {
            batchTaskMonitoringRepository.save(task);
            logger.info("Task monitoring data saved successfully for handler: {}", annotation.taskHandler());
        } catch (Exception e) {
            logger.error("Failed to save task monitoring data for handler: {}", annotation.taskHandler(), e);
        }
    }

}

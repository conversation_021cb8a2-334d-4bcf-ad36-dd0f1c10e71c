package com.maguo.loan.cash.flow.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.zsjz.third.part.LocalCachedPayKeyFetcher;
import com.zsjz.third.part.PayConfig;
import com.zsjz.third.part.PayConfigException;
import com.zsjz.third.part.PayKeyFetcher;
import com.zsjz.third.part.PayMerchantConfig;
import com.zsjz.third.part.baofoo.settlement.BaofooMerchantConfig;
import com.zsjz.third.part.baofoo.util.RsaCodingUtil;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Configuration
public class BaoFuPayMerchantConfigManager {
    @Value("${pay.baofu.paymentUrl}")
    private String paymentUrl;

    @Value("${pay.baofu.unionGatewayUrl}")
    private String unionGatewayUrl;

    @Value("${pay.baofu.defrayUrl}")
    private String defrayUrl;

    private static final Logger logger = LoggerFactory.getLogger(AbstractJobHandler.class);


    private static final String CONFIG_KEY = "pay.baofu.merchants.config";

    private Map<String, BaofooMerchantConfig> merchantConfigMap = new ConcurrentHashMap<>();

    private Map<String ,LocalCachedPayKeyFetcher> payKeyFetcherMap = new ConcurrentHashMap<>();

    @Getter
    private PayConfig payConfig = new PayConfig();

    public PayConfig getPayConfig() {
        return this.payConfig;
    }


    private final Gson gson = new Gson();

    @PostConstruct
    public void init() {
        // 加载商户配置
        loadAll();
        // 注册配置变更监听器
        Config apolloConfig = ConfigService.getAppConfig();

        apolloConfig.addChangeListener(new ConfigChangeListener() {
            @Override
            public void onChange(ConfigChangeEvent event) {
                // 监听变更
                if (event.changedKeys().contains(CONFIG_KEY)) {
                    loadAll();
                }
            }
        });
    }

    private void  loadAll(){
        try {
            loadConfig();
            reloadPayKeyFetcherMap();
        } catch (IOException e) {
            logger.error("加载宝付支付配置异常：",e);
            throw new RuntimeException(e);
        }
    }


    private void loadConfig() {
        Config apolloConfig = ConfigService.getAppConfig();
        String configJson = apolloConfig.getProperty(CONFIG_KEY, "{}");
        Map<String, BaofooMerchantConfig> newConfig = gson.fromJson(
                configJson,
                new TypeToken<Map<String, BaofooMerchantConfig>>(){}.getType()
        );
        merchantConfigMap.clear();
        merchantConfigMap.putAll(newConfig);

        PayConfig config = new PayConfig();
        config.getBaoFu().setPaymentUrl(paymentUrl);
        config.getBaoFu().setUnionGatewayUrl(unionGatewayUrl);
        config.getBaoFu().setDefrayUrl(defrayUrl);
        for (Map.Entry<String, BaofooMerchantConfig> entry : merchantConfigMap.entrySet()) {
            BaofooMerchantConfig value = entry.getValue();
            PayMerchantConfig pmc = new PayMerchantConfig();
            pmc.setTerminalId(value.getTerminalId());
            pmc.setMerchantId(value.getMerchantId());
            pmc.setPrivateKeyPassword(value.getPassword());
            config.addMerchantConfig(pmc);
        }
        this.payConfig = config;
    }

    public BaofooMerchantConfig getMerchantConfig(String merchantId) {
        return merchantConfigMap.get(merchantId);
    }

    public void reloadPayKeyFetcherMap() throws IOException {
        Map<String,LocalCachedPayKeyFetcher> payKeyFetcherMap = new ConcurrentHashMap<>();
        merchantConfigMap.forEach((key, value) -> {
            BaofooMerchantConfig baofooMerchantConfig = merchantConfigMap.get(key);
                payKeyFetcherMap.put(key, new LocalCachedPayKeyFetcher() {
                private PublicKey publicKey;
                private PrivateKey privateKey;

                @Override
                public PublicKey loadPublicKey(PayMerchantConfig merchantConfig) throws IOException {
                    if (this.publicKey == null) {
                        synchronized (this) {
                            if (publicKey == null) {
                                this.publicKey = RsaCodingUtil.getPublicKey(baofooMerchantConfig.getPublicKey());
                            }
                        }
                    }
                    return publicKey;
                }

                @Override
                public PrivateKey loadPrivateKey(PayMerchantConfig merchantConfig) throws IOException {
                    if (this.privateKey == null) {
                        synchronized (this) {
                            if (privateKey == null) {
                                this.privateKey = RsaCodingUtil.getPrivateKey(baofooMerchantConfig.getPrivateKey());
                            }
                        }
                    }
                    return privateKey;
                }
            });
        });
        this.payKeyFetcherMap = payKeyFetcherMap;
    }


    public PayKeyFetcher getPayKeyFetcher(String merchantId) {
        return payKeyFetcherMap.get(merchantId);
    }

}

package com.maguo.loan.cash.flow.config;


import com.maguo.loan.cash.flow.service.MqService;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.ExchangeBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.config.ContainerCustomizer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.DirectMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.autoconfigure.amqp.RabbitTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Configuration
public class RabbitConfig {

    @Bean
    public ContainerCustomizer<SimpleMessageListenerContainer> rabbitTracingSimpleListenerContainerCustomizer() {
        return c -> c.setObservationEnabled(true);
    }

    @Bean
    public ContainerCustomizer<DirectMessageListenerContainer> rabbitTracingDirectistenerContainerCustomizer() {
        return c -> c.setObservationEnabled(true);
    }

    @Bean
    public RabbitTemplateCustomizer rabbitTemplateCustomizer() {
        return c -> c.setObservationEnabled(true);
    }


    /****
     * exchange
     */
    @Bean
    public Exchange smsExchange() {
        return ExchangeBuilder.directExchange(Exchanges.SMS).build();
    }

    @Bean
    public Exchange riskExchange() {
        return ExchangeBuilder.directExchange(Exchanges.RISK).build();
    }

    @Bean
    public Exchange channelExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CHANNEL).build();
    }

    @Bean
    public Exchange offlineExchange() {
        return ExchangeBuilder.directExchange(Exchanges.OFFLINE).build();
    }

    @Bean
    public Exchange signExchange() {
        return ExchangeBuilder.directExchange(Exchanges.SIGN).build();
    }

    @Bean
    public Exchange zySignExchange() {
        return ExchangeBuilder.directExchange(Exchanges.ZY_SIGN).build();
    }

    @Bean
    public Exchange creditExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CREDIT).build();
    }

    @Bean
    public Exchange fqlCreditExchange() {
        return ExchangeBuilder.directExchange(Exchanges.FQL_CREDIT).build();
    }

    @Bean
    public Exchange loanExchange() {
        return ExchangeBuilder.directExchange(Exchanges.LOAN).build();
    }

    @Bean
    public Exchange repayExchange() {
        return ExchangeBuilder.directExchange(Exchanges.REPAY).build();
    }

    @Bean
    public Exchange aggregatePayExchange() {
        return ExchangeBuilder.directExchange(Exchanges.AGGREGATE_PAY).build();
    }

    @Bean
    public Exchange rightsExchange() {
        return ExchangeBuilder.directExchange(Exchanges.RIGHTS).build();
    }

    @Bean
    public Exchange ocrExchange() {
        return ExchangeBuilder.directExchange(Exchanges.OCR).build();
    }

    @Bean
    public Exchange callbackExchange() {
        return ExchangeBuilder.directExchange(Exchanges.CALLBACK).build();
    }

    @Bean
    public Exchange collRepayExchange() {
        return ExchangeBuilder.directExchange(Exchanges.COLL_REPAY).build();
    }

    @Bean
    public Exchange renewedExchange() {
        return ExchangeBuilder.directExchange(Exchanges.RENEWED).build();
    }

    @Bean
    public Exchange serviceMsgSubscriptionExchange() {
        return ExchangeBuilder.directExchange(Exchanges.SERVICE_MSG_SUBSCRIPTION).build();
    }

    @Bean
    public Exchange approvalExchange() {
        return ExchangeBuilder.directExchange(Exchanges.APPROVAL).build();
    }

    @Bean
    public Exchange revolvingExchange() {
        return ExchangeBuilder.directExchange(Exchanges.REVOLVING).build();
    }

    /****
     * queue
     */
    @Bean
    public Queue smsSendQueue() {
        return QueueBuilder.durable(Queues.SMS_SEND).build();
    }

    @Bean
    public Queue smsSendDelayQueue() {
        return QueueBuilder.durable(Queues.SMS_SEND_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SMS_SEND)
            .withArgument("x-dead-letter-exchange", Exchanges.SMS)
            .build();
    }

    @Bean
    public Queue smsSend3HDelayQueue() {
        return QueueBuilder.durable(Queues.SMS_SEND_3H_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SMS_SEND)
            .withArgument("x-dead-letter-exchange", Exchanges.SMS)
            .build();
    }


    @Bean
    public Queue riskApplyQueue() {
        return QueueBuilder.durable(Queues.RISK_APPLY).build();
    }

    @Bean
    public Queue artificialRiskApplyQueue() {
        return QueueBuilder.durable(Queues.ARTIFICIAL_RISK_APPLY).build();
    }

    @Bean
    public Queue riskLoanApplyQueue() { return  QueueBuilder.durable(Queues.RISK_LOAN_APPLY).build(); }

    @Bean
    public Queue channelApplyQueue() {
        return QueueBuilder.durable(Queues.CHANNEL_APPLY).build();
    }


    @Bean
    public Queue riskApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RISK_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }

    @Bean
    public Queue riskApplyOutQueue() {
        return QueueBuilder.durable(Queues.RISK_APPLY_OUT).build();
    }

    @Bean
    public Queue riskApplyOutDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_APPLY_OUT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RISK_APPLY_OUT)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }

    @Bean
    public Queue riskLoanApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_LOAN_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RISK_LOAN_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }

    @Bean
    public Queue baiWeiRiskLoanApplyDelayQueue() {
        return QueueBuilder.durable(Queues.BW_RISK_LOAN_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.BW_RISK_LOAN_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }

    @Bean
    public Queue userFileDownloadDelayQueue() {
        return QueueBuilder.durable(Queues.CREDIT_USER_FILE_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_USER_FILE_DOWNLOAD)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue FqlUserFileDownloadDelayQueue() {
        return QueueBuilder.durable(Queues.FQL_CREDIT_USER_FILE_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.FQL_CREDIT_USER_FILE_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.FQL_CREDIT)
            .build();
    }

    @Bean
    public Queue imgsUserFileDownloadDelayQueue() {
        return QueueBuilder.durable(Queues.CREDIT_IMGS_FILE_DOWNLOAD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_IMGS_FILE_DOWNLOAD)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue riskQueryQueue() {
        return QueueBuilder.durable(Queues.RISK_QUERY).build();
    }

    @Bean
    public Queue baiWeiRiskQueryQueue() {
        return QueueBuilder.durable(Queues.BW_RISK_QUERY).build();
    }

    @Bean
    public Queue baiWeiRiskNotifyQueue() {
        return QueueBuilder.durable(Queues.BW_RISK_NOTIFY).build();
    }

    @Bean
    public Queue baiWeiRiskLoanApplyQueue() {
        return QueueBuilder.durable(Queues.BW_RISK_LOAN_APPLY).build();
    }

    @Bean
    public Queue riskQueryDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RISK_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }

    @Bean
    public Queue riskQueryOutQueue() {
        return QueueBuilder.durable(Queues.RISK_QUERY_OUT).build();
    }

    @Bean
    public Queue riskQueryOutDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_QUERY_OUT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RISK_QUERY_OUT)
            .withArgument("x-dead-letter-exchange", Exchanges.RISK)
            .build();
    }


    @Bean
    public Queue signApplyQueue() {
        return QueueBuilder.durable(Queues.SIGN_APPLY).build();
    }

    @Bean
    public Queue zySignApplyQueue() {
        return QueueBuilder.durable(Queues.ZY_SIGN_APPLY).build();
    }

    @Bean
    public Queue signApplyDelayQueue() {
        return QueueBuilder.durable(Queues.SIGN_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGN_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGN)
            .build();
    }

    @Bean
    public Queue signQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.SIGN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGN)
            .build();
    }

    @Bean
    public Queue signQueryResultQueue() {
        return QueueBuilder.durable(Queues.SIGN_QUERY).build();
    }

    @Bean
    public Queue signNewQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.SIGN_NEW_QUERY_DELAY_QUEUE)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.SIGN_NEW_QUERY_RK)
            .withArgument("x-dead-letter-exchange", Exchanges.SIGN)
            .build();
    }

    @Bean
    public Queue signNewQueryResultQueue() {
        return QueueBuilder.durable(Queues.SIGN_NEW_QUERY_QUEUE).build();
    }

    @Bean
    public Queue creditRouteApplyQueue() {
        return QueueBuilder.durable(Queues.CREDIT_ROUTE_APPLY).build();
    }

    @Bean
    public Queue creditRouteResultQueue() {
        return QueueBuilder.durable(Queues.CREDIT_ROUTE_RESULT).build();
    }

    @Bean
    public Queue creditApplyQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditApplyQueue() {
        return QueueBuilder.durable(Queues.CREDIT_APPLY).build();
    }

    @Bean
    public Queue recreditApplyQueue() {
        return QueueBuilder.durable(Queues.RECREDIT_APPLY).build();
    }

    @Bean
    public Queue creditQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.CREDIT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CREDIT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue creditQueryResultQueue() {
        return QueueBuilder.durable(Queues.CREDIT_QUERY).build();
    }

    @Bean
    public Queue recreditQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.RECREDIT_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RECREDIT_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.CREDIT)
            .build();
    }

    @Bean
    public Queue recreditQueryResultQueue() {
        return QueueBuilder.durable(Queues.RECREDIT_QUERY).build();
    }

    @Bean
    public Queue loanApplyQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue loanApplyQueue() {
        return QueueBuilder.durable(Queues.LOAN_APPLY).build();
    }

    @Bean
    public Queue loanQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue loanQueryResultQueue() {
        return QueueBuilder.durable(Queues.LOAN_QUERY).build();
    }

    @Bean
    public Queue repayPlanSyncResultQueue() {
        return QueueBuilder.durable(Queues.REPAY_PLAN_SYNC).build();
    }

    @Bean
    public Queue loanRecordApplyQueue() {
        return QueueBuilder.durable(Queues.LOAN_RECORD_APPLY).build();
    }

    @Bean
    public Queue loanRecordApplyQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_RECORD_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_RECORD_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue loanRecordQueryResultQueue() {
        return QueueBuilder.durable(Queues.LOAN_RECORD_QUERY).build();
    }

    @Bean
    public Queue loanRecordQueryResultQueueDelay() {
        return QueueBuilder.durable(Queues.LOAN_RECORD_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.LOAN_RECORD_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }


    @Bean
    public Queue chargeApplyQueue() {
        return QueueBuilder.durable(Queues.CHARGE_APPLY).build();
    }

    @Bean
    public Queue chargeQueryDelayQueue() {
        return QueueBuilder.durable(Queues.CHARGE_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CHARGE_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue chargeQueryQueue() {
        return QueueBuilder.durable(Queues.CHARGE_QUERY).build();
    }
    @Bean
    public Queue creditMayiAccessQueue() {
        return QueueBuilder.durable(Queues.CREDIT_MAYI_ACCESS).build();
    }
    @Bean
    public Queue creditUserFileDownloadQueue() {
        return QueueBuilder.durable(Queues.CREDIT_USER_FILE_DOWNLOAD).build();
    }
    @Bean
    public Queue fqlCreditUserFileDownloadQueue() {
        return QueueBuilder.durable(Queues.FQL_CREDIT_USER_FILE_DOWNLOAD).build();
    }
    @Bean
    public Queue creditImgsUserFileDownloadQueue() {
        return QueueBuilder.durable(Queues.CREDIT_IMGS_FILE_DOWNLOAD).build();
    }
    @Bean
    public Queue repayQueryDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayQueryQueue() {
        return QueueBuilder.durable(Queues.REPAY_QUERY).build();
    }

    @Bean
    public Queue repayNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_NOTIFY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_NOTIFY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayNotifyQueue() {
        return QueueBuilder.durable(Queues.REPAY_NOTIFY).build();
    }

    @Bean
    public Queue repayNotifyResultDelayQueue() {
        return QueueBuilder.durable(Queues.REPAY_NOTIFY_RESULT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REPAY_NOTIFY_RESULT)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue repayNotifyResultQueue() {
        return QueueBuilder.durable(Queues.REPAY_NOTIFY_RESULT).build();
    }

    @Bean
    public Queue repayApplyQueue() {
        return QueueBuilder.durable(Queues.REPAY_APPLY).build();
    }

    @Bean
    public Queue rightsDataPushQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_DATA_PUSH).build();
    }

    @Bean
    public Queue rightsDataPushDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_DATA_PUSH_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_DATA_PUSH)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }


    @Bean
    public Queue preRepayFeeQueue() {
        return QueueBuilder.durable(Queues.DUE_BATCH_PRE_REPAY_FEE).build();
    }

    @Bean
    public Queue preRepayFeeDelayQueue() {
        return QueueBuilder.durable(Queues.DUE_BATCH_PRE_REPAY_FEE_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.DUE_BATCH_PRE_REPAY_FEE)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }


    @Bean
    public Queue ocrIdQueue() {
        return QueueBuilder.durable(Queues.OCR_ID).build();
    }


    @Bean
    public Queue ocrFaceQueue() {
        return QueueBuilder.durable(Queues.OCR_FACE).build();
    }

    @Bean
    public Queue rightsQueryResultDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    @Bean
    public Queue rightsQuerRefundDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_REFUND_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    @Bean
    public Queue rightsQueryResultQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_QUERY).build();
    }

    @Bean
    public Queue rightsQuerRefundQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_REFUND_QUERY).build();
    }

    @Bean
    public Queue rightsApplyQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_APPLY).build();
    }

    @Bean
    public Queue rightsApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }


    @Bean
    public Queue rightsDueBatchQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_DUE_BATCH).build();
    }

    @Bean
    public Queue rightsRouteApplyQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_ROUTE_APPLY).build();
    }

    @Bean
    public Queue rightsRouteApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_ROUTE_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_ROUTE_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    /**
     * 权益相关队列-新
     */
    @Bean
    public Queue rightsNewApplyQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_APPLY).build();
    }

    @Bean
    public Queue rightsNewApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_NEW_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    @Bean
    public Queue rightsNewResultQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_RESULT).build();
    }

    @Bean
    public Queue rightsNewResultDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_RESULT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_NEW_RESULT)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    @Bean
    public Queue rightsNewRefundApplyQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_REFUND_APPLY).build();
    }

    @Bean
    public Queue rightsNewRefundApplyDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_REFUND_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_NEW_REFUND_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }

    @Bean
    public Queue rightsNewRefundResultQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_REFUND_RESULT).build();
    }

    @Bean
    public Queue rightsNewRefundResultDelayQueue() {
        return QueueBuilder.durable(Queues.RIGHTS_NEW_REFUND_RESULT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RIGHTS_NEW_REFUND_RESULT)
            .withArgument("x-dead-letter-exchange", Exchanges.RIGHTS)
            .build();
    }


    @Bean
    public Queue chargeChannelApplyQueue() {
        return QueueBuilder.durable(Queues.CHARGE_CHANNEL_APPLY).build();
    }

    @Bean
    public Queue chargeChannelQueryQueue() {
        return QueueBuilder.durable(Queues.CHARGE_CHANNEL_QUERY).build();
    }

    @Bean
    public Queue chargeChannelQueryDelayQueue() {
        return QueueBuilder.durable(Queues.CHARGE_CHANNEL_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CHARGE_CHANNEL_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue callbackNotifyQueue() {
        return QueueBuilder.durable(Queues.CALLBACK_NOTIFY).build();
    }

    @Bean
    public Queue callbackCommonNotifyQueue() {
        return QueueBuilder.durable(Queues.CALLBACK_COMMON_NOTIFY).build();
    }

    @Bean
    public Queue callbackNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.CALLBACK_NOTIFY_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CALLBACK)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CALLBACK_NOTIFY)
            .build();
    }

    @Bean
    public Queue callbackCommonNotifyDelayQueue() {
        return QueueBuilder.durable(Queues.CALLBACK_COMMON_NOTIFY_DELAY)
            .withArgument("x-dead-letter-exchange", Exchanges.CALLBACK)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.CALLBACK_COMMON_NOTIFY)
            .build();
    }

    @Bean
    public Queue dueBatchRepayQueue() {
        return QueueBuilder.durable(Queues.DUE_BATCH_REPAY).build();
    }

    @Bean
    public Queue dueBatchRepayDelayQueue() {
        return QueueBuilder.durable(Queues.DUE_BATCH_REPAY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.DUE_BATCH_REPAY)
            .withArgument("x-dead-letter-exchange", Exchanges.REPAY)
            .build();
    }

    @Bean
    public Queue notLoanApplySmsSendQueue() {
        return QueueBuilder.durable(Queues.NOT_LOAN_APPLY_SMS_SEND).build();
    }

    @Bean
    public Queue notLoanApplySmsSendDelayQueue() {
        return QueueBuilder.durable(Queues.NOT_LOAN_APPLY_SMS_SEND_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.NOT_LOAN_APPLY_SMS_SEND)
            .withArgument("x-dead-letter-exchange", Exchanges.SMS)
            .build();
    }

    @Bean
    public Queue notAmountAppliedSmsSendQueue() {
        return QueueBuilder.durable(Queues.NOT_AMOUNT_APPLIED_SMS_SEND).build();
    }

    @Bean
    public Queue notAmountAppliedSmsSendDelayQueue() {
        return QueueBuilder.durable(Queues.NOT_AMOUNT_APPLIED_SMS_SEND_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.NOT_AMOUNT_APPLIED_SMS_SEND)
            .withArgument("x-dead-letter-exchange", Exchanges.SMS)
            .build();
    }



    @Bean
    public Queue ocrRegisterQueue() {
        return QueueBuilder.durable(Queues.OCR_REGISTER).build();
    }

    @Bean
    public Queue ocrRegisterDelayQueue() {
        return QueueBuilder.durable(Queues.OCR_REGISTER_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.OCR_REGISTER)
            .withArgument("x-dead-letter-exchange", Exchanges.OCR)
            .build();
    }

    @Bean
    public Queue aggregatePayQueryQueue() {
        return QueueBuilder.durable(Queues.AGGREGATE_PAY_QUERY).build();
    }

    @Bean
    public Queue aggregatePayQueryDelayQueue() {
        return QueueBuilder.durable(Queues.AGGREGATE_PAY_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.AGGREGATE_PAY_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.AGGREGATE_PAY)
            .build();
    }

    @Bean
    public Queue offlineApplyQueue() {
        return QueueBuilder.durable(Queues.OFFLINE_APPLY).build();
    }

    @Bean
    public Queue offlineApplyDelayQueue() {
        return QueueBuilder.durable(Queues.OFFLINE_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.OFFLINE_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.OFFLINE)
            .build();
    }


    @Bean
    public Queue preLoanAuditApplyDelayQueue() {
        return QueueBuilder.durable(Queues.PRE_LOAN_AUDIT_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.PRE_LOAN_AUDIT_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue preLoanAuditApplyQueue() {
        return QueueBuilder.durable(Queues.PRE_LOAN_AUDIT_APPLY).build();
    }

    @Bean
    public Queue preLoanAuditResultDelayQueue() {
        return QueueBuilder.durable(Queues.PRE_LOAN_AUDIT_RESULT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.PRE_LOAN_AUDIT_RESULT)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue preLoanAuditResultQueue() {
        return QueueBuilder.durable(Queues.PRE_LOAN_AUDIT_RESULT).build();
    }

    @Bean
    public Queue renewedQueryQueue() {
        return QueueBuilder.durable(Queues.RENEWED_QUERY).build();
    }

    @Bean
    public Queue renewedQueryDelayQueue() {
        return QueueBuilder.durable(Queues.RENEWED_QUERY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.RENEWED_QUERY)
            .withArgument("x-dead-letter-exchange", Exchanges.RENEWED)
            .build();
    }

    @Bean
    public Queue serviceRiskMsgSubscribeSendQueue() {
        return QueueBuilder.durable(Queues.SERVICE_RISK_MSG_SUBSCRIBE_SEND).build();
    }

    @Bean
    public Queue approvalApplyHuoshanrongQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HUOSHANRONG).build();
    }

    @Bean
    public Queue approvalApplyWldQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_WLD).build();
    }

    @Bean
    public Queue approvalApplyHuoshanrongDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HUOSHANRONG_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_HUOSHANRONG)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }

    @Bean
    public Queue approvalApplyWldDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_WLD_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_WLD)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }

    @Bean
    public Queue approvalApplyHengxiaohuaQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HENGXIOHUA).build();
    }

    @Bean
    public Queue approvalApplyHengxiaohuaDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HENGXIOHUA_DELAY).withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_HENGXIOHUA)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL).build();
    }

    @Bean
    public Queue approvalApplyNiWoDaiQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_NIWODAI).build();
    }

    @Bean
    public Queue approvalApplyNiWoDaiDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_NIWODAI_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_NIWODAI)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }

    @Bean
    public Queue approvalApplyYqgQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_YQG).build();
    }

    @Bean
    public Queue approvalApplyYqgDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_YQG_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_YQG)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }
    @Bean
    public Queue approvalApplyHelloQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HELLO).build();
    }

    @Bean
    public Queue approvalApplyHelloDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_HELLO_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_HELLO)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }

    @Bean
    public Queue approvalApplyJuZi() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_JUZI).build();
    }

    @Bean
    public Queue approvalApplyJuZiDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_JUZI_DELAY).withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_JUZI)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL).build();
    }

    @Bean
    public Queue approvalApplyRenPinQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_RENPIN).build();
    }

    @Bean
    public Queue approvalApplyRenPinDelayQueue() {
        return QueueBuilder.durable(Queues.APPROVAL_APPLY_RENPIN_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.APPROVAL_APPLY_RENPIN)
            .withArgument("x-dead-letter-exchange", Exchanges.APPROVAL)
            .build();
    }

    @Bean
    public Queue revolvingAmountUpdateApplyQueue() {
        return QueueBuilder.durable(Queues.REVOLVING_AMOUNT_UPDATE_APPLY).build();
    }

    @Bean
    public Queue revolvingAmountUpdateApplyDelayQueue() {
        return QueueBuilder.durable(Queues.REVOLVING_AMOUNT_UPDATE_APPLY_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REVOLVING_AMOUNT_UPDATE_APPLY)
            .withArgument("x-dead-letter-exchange", Exchanges.REVOLVING)
            .build();
    }

    @Bean
    public Queue revolvingAmountUpdateResultQueue() {
        return QueueBuilder.durable(Queues.REVOLVING_AMOUNT_UPDATE_RESULT).build();
    }

    @Bean
    public Queue revolvingAmountUpdateResultDelayQueue() {
        return QueueBuilder.durable(Queues.REVOLVING_AMOUNT_UPDATE_RESULT_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.REVOLVING_AMOUNT_UPDATE_RESULT)
            .withArgument("x-dead-letter-exchange", Exchanges.REVOLVING)
            .build();
    }

    @Bean
    public Queue orderDelayAutoLoanQueue() {
        return QueueBuilder.durable(Queues.ORDER_DELAY_AUTO_LOAN).build();
    }

    @Bean
    public Queue orderDelayAutoLoanDelayQueue() {
        return QueueBuilder.durable(Queues.ORDER_DELAY_AUTO_LOAN_DELAY)
            .withArgument("x-dead-letter-routing-key", RoutingKeys.ORDER_DELAY_AUTO_LOAN)
            .withArgument("x-dead-letter-exchange", Exchanges.LOAN)
            .build();
    }

    @Bean
    public Queue riskResultNoticeOutQueue() {
        return QueueBuilder.durable(Queues.RISK_RESULT_NOTICE_OUT).build();
    }

    @Bean
    public Queue riskResultNoticeOutDelayQueue() {
        return QueueBuilder.durable(Queues.RISK_RESULT_NOTICE_OUT_DELAY).build();
    }

    /**
     * Binding
     */
    @Bean
    public Binding smsSendBinding(Exchange smsExchange, Queue smsSendQueue) {
        return BindingBuilder.bind(smsSendQueue).to(smsExchange).with(RoutingKeys.SMS_SEND).noargs();
    }

    @Bean
    public Binding channelApplyBinding(Exchange channelExchange, Queue channelApplyQueue) {
        return BindingBuilder.bind(channelApplyQueue).to(channelExchange).with(RoutingKeys.CHANNEL_APPLY).noargs();
    }

    @Bean
    public Binding smsSendDelayBinding(Exchange smsExchange, Queue smsSendDelayQueue) {
        return BindingBuilder.bind(smsSendDelayQueue).to(smsExchange).with(RoutingKeys.SMS_SEND_DELAY).noargs();
    }

    @Bean
    public Binding smsSend3HDelayBinding(Exchange smsExchange, Queue smsSend3HDelayQueue) {
        return BindingBuilder.bind(smsSend3HDelayQueue).to(smsExchange).with(RoutingKeys.SMS_SEND_3H_DELAY).noargs();
    }

    @Bean
    public Binding riskApplyBinding(Exchange riskExchange, Queue riskApplyQueue) {
        return BindingBuilder.bind(riskApplyQueue).to(riskExchange).with(RoutingKeys.RISK_APPLY).noargs();
    }

    @Bean
    public Binding riskApplyOutBinding(Exchange riskExchange, Queue riskApplyOutQueue) {
        return BindingBuilder.bind(riskApplyOutQueue).to(riskExchange).with(RoutingKeys.RISK_APPLY_OUT).noargs();
    }

    @Bean
    public Binding riskApplyOutDelayBinding(Exchange riskExchange, Queue riskApplyOutDelayQueue) {
        return BindingBuilder.bind(riskApplyOutDelayQueue).to(riskExchange).with(RoutingKeys.RISK_APPLY_OUT_DELAY).noargs();
    }

    @Bean
    public Binding riskLoanApplyBinding(Exchange riskExchange, Queue riskLoanApplyQueue){
        return BindingBuilder.bind(riskLoanApplyQueue).to(riskExchange).with(RoutingKeys.RISK_LOAN_APPLY).noargs();
    }

    @Bean
    public Binding creditMayiAccessBinding(Exchange creditExchange, Queue creditMayiAccessQueue) {
        return BindingBuilder.bind(creditMayiAccessQueue).to(creditExchange).with(RoutingKeys.CREDIT_MAYI_ACCESS).noargs();
    }
    @Bean
    public Binding creditUserFileDownloadBinding(Exchange creditExchange, Queue creditUserFileDownloadQueue) {
        return BindingBuilder.bind(creditUserFileDownloadQueue).to(creditExchange).with(RoutingKeys.CREDIT_USER_FILE_DOWNLOAD).noargs();
    }
    @Bean
    public Binding fqlCreditUserFileDownloadBinding(Exchange fqlCreditExchange, Queue fqlCreditUserFileDownloadQueue) {
        return BindingBuilder.bind(fqlCreditUserFileDownloadQueue).to(fqlCreditExchange).with(RoutingKeys.FQL_CREDIT_USER_FILE_DOWNLOAD).noargs();
    }

    @Bean
    public Binding imgsUserFileDownloadBinding(Exchange creditExchange, Queue creditImgsUserFileDownloadQueue) {
        return BindingBuilder.bind(creditImgsUserFileDownloadQueue).to(creditExchange).with(RoutingKeys.CREDIT_IMGS_FILE_DOWNLOAD).noargs();
    }
    @Bean
    public Binding riskApplyDelayBinding(Exchange riskExchange, Queue riskApplyDelayQueue) {
        return BindingBuilder.bind(riskApplyDelayQueue).to(riskExchange).with(RoutingKeys.RISK_APPLY_DELAY).noargs();
    }
    @Bean
    public Binding riskLoanApplyDelayBinding(Exchange riskExchange, Queue riskLoanApplyDelayQueue) {
        return BindingBuilder.bind(riskLoanApplyDelayQueue).to(riskExchange).with(RoutingKeys.RISK_LOAN_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding baiWeiRiskLoanApplyDelayBinding(Exchange riskExchange, Queue baiWeiRiskLoanApplyDelayQueue) {
        return BindingBuilder.bind(baiWeiRiskLoanApplyDelayQueue).to(riskExchange).with(RoutingKeys.BW_RISK_LOAN_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding creditUserFileDownloadDelayBinding(Exchange riskExchange, Queue userFileDownloadDelayQueue) {
        return BindingBuilder.bind(userFileDownloadDelayQueue).to(riskExchange).with(RoutingKeys.CREDIT_USER_FILE_DOWNLOAD_DELAY).noargs();
    }

    @Bean
    public Binding creditImgsUserFileDownloadDelayBinding(Exchange creditExchange, Queue imgsUserFileDownloadDelayQueue) {
        return BindingBuilder.bind(imgsUserFileDownloadDelayQueue).to(creditExchange).with(RoutingKeys.CREDIT_IMGS_FILE_DOWNLOAD_DELAY).noargs();
    }
    @Bean
    public Binding riskQueryBinding(Exchange riskExchange, Queue riskQueryQueue) {
        return BindingBuilder.bind(riskQueryQueue).to(riskExchange).with(RoutingKeys.RISK_QUERY).noargs();
    }

    @Bean
    public Binding baiWeiRiskQueryBinding(Exchange riskExchange, Queue baiWeiRiskQueryQueue) {
        return BindingBuilder.bind(baiWeiRiskQueryQueue).to(riskExchange).with(RoutingKeys.BW_RISK_QUERY).noargs();
    }

    @Bean
    public Binding baiWeiRiskNotifyBinding(Exchange riskExchange, Queue baiWeiRiskNotifyQueue){
        return BindingBuilder.bind(baiWeiRiskNotifyQueue).to(riskExchange).with(RoutingKeys.BW_RISK_NOTIFY).noargs();
    }

    @Bean
    public Binding baiWeiRiskLoanApplyBinding(Exchange riskExchange, Queue baiWeiRiskLoanApplyQueue){
        return BindingBuilder.bind(baiWeiRiskLoanApplyQueue).to(riskExchange).with(RoutingKeys.BW_RISK_LOAN_APPLY).noargs();
    }

    @Bean
    public Binding riskQueryDelayBinding(Exchange riskExchange, Queue riskQueryDelayQueue) {
        return BindingBuilder.bind(riskQueryDelayQueue).to(riskExchange).with(RoutingKeys.RISK_QUERY_DELAY).noargs();
    }
    @Bean
    public Binding riskQueryOutBinding(Exchange riskExchange, Queue riskQueryOutQueue) {
        return BindingBuilder.bind(riskQueryOutQueue).to(riskExchange).with(RoutingKeys.RISK_QUERY_OUT).noargs();
    }

    @Bean
    public Binding riskQueryOutDelayBinding(Exchange riskExchange, Queue riskQueryOutDelayQueue) {
        return BindingBuilder.bind(riskQueryOutDelayQueue).to(riskExchange).with(RoutingKeys.RISK_QUERY_OUT_DELAY).noargs();
    }

    @Bean
    public Binding signApplyBinding(Exchange signExchange, Queue signApplyQueue) {
        return BindingBuilder.bind(signApplyQueue).to(signExchange).with(RoutingKeys.SIGN_APPLY).noargs();
    }

    @Bean
    public Binding zySignApplyBinding(Exchange zySignExchange, Queue zySignApplyQueue) {
        return BindingBuilder.bind(zySignApplyQueue).to(zySignExchange).with(RoutingKeys.ZY_SIGN_APPLY).noargs();
    }

    @Bean
    public Binding signApplyDelayBinding(Exchange signExchange, Queue signApplyDelayQueue) {
        return BindingBuilder.bind(signApplyDelayQueue).to(signExchange).with(RoutingKeys.SIGN_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding signQueryResultDelayBinding(Exchange signExchange, Queue signQueryResultQueueDelay) {
        return BindingBuilder.bind(signQueryResultQueueDelay).to(signExchange).with(RoutingKeys.SIGN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding signQueryResultBinding(Exchange signExchange, Queue signQueryResultQueue) {
        return BindingBuilder.bind(signQueryResultQueue).to(signExchange).with(RoutingKeys.SIGN_QUERY).noargs();
    }

    @Bean
    public Binding signNewQueryResultDelayBinding(Exchange signExchange, Queue signNewQueryResultQueueDelay) {
        return BindingBuilder.bind(signNewQueryResultQueueDelay).to(signExchange).with(RoutingKeys.SIGN_NEW_QUERY_DELAY_RK).noargs();
    }

    @Bean
    public Binding signNewQueryResultBinding(Exchange signExchange, Queue signNewQueryResultQueue) {
        return BindingBuilder.bind(signNewQueryResultQueue).to(signExchange).with(RoutingKeys.SIGN_NEW_QUERY_RK).noargs();
    }

    @Bean
    public Binding creditRouteApplyBinding(Exchange creditExchange, Queue creditRouteApplyQueue) {
        return BindingBuilder.bind(creditRouteApplyQueue).to(creditExchange).with(RoutingKeys.CREDIT_ROUTE_APPLY).noargs();
    }

    @Bean
    public Binding creditRouteResultBinding(Exchange creditExchange, Queue creditRouteResultQueue) {
        return BindingBuilder.bind(creditRouteResultQueue).to(creditExchange).with(RoutingKeys.CREDIT_ROUTE_RESULT).noargs();
    }

    @Bean
    public Binding creditApplyDelayBinding(Exchange creditExchange, Queue creditApplyQueueDelay) {
        return BindingBuilder.bind(creditApplyQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding creditApplyBinding(Exchange creditExchange, Queue creditApplyQueue) {
        return BindingBuilder.bind(creditApplyQueue).to(creditExchange).with(RoutingKeys.CREDIT_APPLY).noargs();
    }

    @Bean
    public Binding recreditApplyBinding(Exchange creditExchange, Queue recreditApplyQueue) {
        return BindingBuilder.bind(recreditApplyQueue).to(creditExchange).with(RoutingKeys.RECREDIT_APPLY).noargs();
    }

    @Bean
    public Binding creditQueryResultDelayBinding(Exchange creditExchange, Queue creditQueryResultQueueDelay) {
        return BindingBuilder.bind(creditQueryResultQueueDelay).to(creditExchange).with(RoutingKeys.CREDIT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding creditQueryResultBinding(Exchange creditExchange, Queue creditQueryResultQueue) {
        return BindingBuilder.bind(creditQueryResultQueue).to(creditExchange).with(RoutingKeys.CREDIT_QUERY).noargs();
    }

    @Bean
    public Binding recreditQueryResultDelayBinding(Exchange creditExchange, Queue recreditQueryResultQueueDelay) {
        return BindingBuilder.bind(recreditQueryResultQueueDelay).to(creditExchange).with(RoutingKeys.RECREDIT_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding recreditQueryResultBinding(Exchange creditExchange, Queue recreditQueryResultQueue) {
        return BindingBuilder.bind(recreditQueryResultQueue).to(creditExchange).with(RoutingKeys.RECREDIT_QUERY).noargs();
    }

    @Bean
    public Binding loanApplyDelayBinding(Exchange loanExchange, Queue loanApplyQueueDelay) {
        return BindingBuilder.bind(loanApplyQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding loanApplyBinding(Exchange loanExchange, Queue loanApplyQueue) {
        return BindingBuilder.bind(loanApplyQueue).to(loanExchange).with(RoutingKeys.LOAN_APPLY).noargs();
    }

    @Bean
    public Binding loanQueryResultDelayBinding(Exchange loanExchange, Queue loanQueryResultQueueDelay) {
        return BindingBuilder.bind(loanQueryResultQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding loanQueryResultBinding(Exchange loanExchange, Queue loanQueryResultQueue) {
        return BindingBuilder.bind(loanQueryResultQueue).to(loanExchange).with(RoutingKeys.LOAN_QUERY).noargs();
    }

    @Bean
    public Binding rapayPlanSyncResultBinding(Exchange loanExchange, Queue repayPlanSyncResultQueue) {
        return BindingBuilder.bind(repayPlanSyncResultQueue).to(loanExchange).with(RoutingKeys.REPAY_PLAN_SYNC).noargs();
    }

    @Bean
    public Binding loanRecordApplyBinding(Exchange loanExchange, Queue loanRecordApplyQueue) {
        return BindingBuilder.bind(loanRecordApplyQueue).to(loanExchange).with(RoutingKeys.LOAN_RECORD_APPLY).noargs();
    }

    @Bean
    public Binding loanRecordApplyDelayBinding(Exchange loanExchange, Queue loanRecordApplyQueueDelay) {
        return BindingBuilder.bind(loanRecordApplyQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_RECORD_APPLY_DELAY).noargs();
    }


    @Bean
    public Binding loanRecordQueryResultBinding(Exchange loanExchange, Queue loanRecordQueryResultQueue) {
        return BindingBuilder.bind(loanRecordQueryResultQueue).to(loanExchange).with(RoutingKeys.LOAN_RECORD_QUERY).noargs();
    }

    @Bean
    public Binding loanRecordQueryResultDelayBinding(Exchange loanExchange, Queue loanRecordQueryResultQueueDelay) {
        return BindingBuilder.bind(loanRecordQueryResultQueueDelay).to(loanExchange).with(RoutingKeys.LOAN_RECORD_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding chargeApplyBinding(Exchange repayExchange, Queue chargeApplyQueue) {
        return BindingBuilder.bind(chargeApplyQueue).to(repayExchange).with(RoutingKeys.CHARGE_APPLY).noargs();
    }

    @Bean
    public Binding chargeQueryDelayBinding(Exchange repayExchange, Queue chargeQueryDelayQueue) {
        return BindingBuilder.bind(chargeQueryDelayQueue).to(repayExchange).with(RoutingKeys.CHARGE_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding chargeQueryBinding(Exchange repayExchange, Queue chargeQueryQueue) {
        return BindingBuilder.bind(chargeQueryQueue).to(repayExchange).with(RoutingKeys.CHARGE_QUERY).noargs();
    }

    @Bean
    public Binding repayQueryDelayBinding(Exchange repayExchange, Queue repayQueryDelayQueue) {
        return BindingBuilder.bind(repayQueryDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding repayQueryBinding(Exchange repayExchange, Queue repayQueryQueue) {
        return BindingBuilder.bind(repayQueryQueue).to(repayExchange).with(RoutingKeys.REPAY_QUERY).noargs();
    }

    @Bean
    public Binding repayNotifyDelayBinding(Exchange repayExchange, Queue repayNotifyDelayQueue) {
        return BindingBuilder.bind(repayNotifyDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_NOTIFY_DELAY).noargs();
    }

    @Bean
    public Binding repayNotifyBinding(Exchange repayExchange, Queue repayNotifyQueue) {
        return BindingBuilder.bind(repayNotifyQueue).to(repayExchange).with(RoutingKeys.REPAY_NOTIFY).noargs();
    }

    @Bean
    public Binding repayNotifyResultDelayBinding(Exchange repayExchange, Queue repayNotifyResultDelayQueue) {
        return BindingBuilder.bind(repayNotifyResultDelayQueue).to(repayExchange).with(RoutingKeys.REPAY_NOTIFY_RESULT_DELAY).noargs();
    }

    @Bean
    public Binding repayNotifyResultBinding(Exchange repayExchange, Queue repayNotifyResultQueue) {
        return BindingBuilder.bind(repayNotifyResultQueue).to(repayExchange).with(RoutingKeys.REPAY_NOTIFY_RESULT).noargs();
    }

    @Bean
    public Binding repayApplyBinding(Exchange repayExchange, Queue repayApplyQueue) {
        return BindingBuilder.bind(repayApplyQueue).to(repayExchange).with(RoutingKeys.REPAY_APPLY).noargs();
    }

    /**
     * 到期批量还款申请
     */
    @Bean
    public Binding dueBatchRepayDelayBinding(Exchange repayExchange, Queue dueBatchRepayDelayQueue) {
        return BindingBuilder.bind(dueBatchRepayDelayQueue).to(repayExchange).with(RoutingKeys.DUE_BATCH_REPAY_DELAY).noargs();
    }


    @Bean
    public Binding preRepayFeeBinding(Exchange repayExchange, Queue preRepayFeeQueue) {
        return BindingBuilder.bind(preRepayFeeQueue).to(repayExchange).with(RoutingKeys.DUE_BATCH_PRE_REPAY_FEE).noargs();
    }


    @Bean
    public Binding preRepayFeeDelayBinding(Exchange repayExchange, Queue preRepayFeeDelayQueue) {
        return BindingBuilder.bind(preRepayFeeDelayQueue).to(repayExchange).with(RoutingKeys.DUE_BATCH_PRE_REPAY_FEE_DELAY).noargs();
    }


    @Bean
    public Binding dueBatchRepayBinding(Exchange repayExchange, Queue dueBatchRepayQueue) {
        return BindingBuilder.bind(dueBatchRepayQueue).to(repayExchange).with(RoutingKeys.DUE_BATCH_REPAY).noargs();
    }

    @Bean
    public Binding rightsApplyBinding(Exchange rightsExchange, Queue rightsApplyQueue) {
        return BindingBuilder.bind(rightsApplyQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_APPLY).noargs();
    }

    @Bean
    public Binding rightsApplyDelayBinding(Exchange rightsExchange, Queue rightsApplyDelayQueue) {
        return BindingBuilder.bind(rightsApplyDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding rightsDataPushBinding(Exchange rightsExchange, Queue rightsDataPushQueue) {
        return BindingBuilder.bind(rightsDataPushQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_DATA_PUSH).noargs();
    }

    @Bean
    public Binding rightsDataPushDelayBinding(Exchange rightsExchange, Queue rightsDataPushDelayQueue) {
        return BindingBuilder.bind(rightsDataPushDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_DATA_PUSH_DELAY).noargs();
    }

    @Bean
    public Binding rightsDueBatchBinding(Exchange rightsExchange, Queue rightsDueBatchQueue) {
        return BindingBuilder.bind(rightsDueBatchQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_DUE_BATCH).noargs();
    }

    @Bean
    public Binding rightsRouteApplyBinding(Exchange rightsExchange, Queue rightsRouteApplyQueue) {
        return BindingBuilder.bind(rightsRouteApplyQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_ROUTE_APPLY).noargs();
    }

    @Bean
    public Binding rightsRouteApplyDelayBinding(Exchange rightsExchange, Queue rightsRouteApplyDelayQueue) {
        return BindingBuilder.bind(rightsRouteApplyDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_ROUTE_APPLY_DELAY).noargs();
    }


    /**
     * 权益相关
     */
    @Bean
    public Binding rightsNewApplyBinding(Exchange rightsExchange, Queue rightsNewApplyQueue) {
        return BindingBuilder.bind(rightsNewApplyQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_APPLY).noargs();
    }

    @Bean
    public Binding rightsNewApplyDelayBinding(Exchange rightsExchange, Queue rightsNewApplyDelayQueue) {
        return BindingBuilder.bind(rightsNewApplyDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding rightsNewResultBinding(Exchange rightsExchange, Queue rightsNewResultQueue) {
        return BindingBuilder.bind(rightsNewResultQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_RESULT).noargs();
    }

    @Bean
    public Binding rightsNewResultDelayBinding(Exchange rightsExchange, Queue rightsNewResultDelayQueue) {
        return BindingBuilder.bind(rightsNewResultDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_RESULT_DELAY).noargs();
    }

    @Bean
    public Binding rightsNewRefundApplyBinding(Exchange rightsExchange, Queue rightsNewRefundApplyQueue) {
        return BindingBuilder.bind(rightsNewRefundApplyQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_REFUND_APPLY).noargs();
    }

    @Bean
    public Binding rightsNewRefundApplyDelayBinding(Exchange rightsExchange, Queue rightsNewRefundApplyDelayQueue) {
        return BindingBuilder.bind(rightsNewRefundApplyDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_REFUND_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding rightsNewRefundResultBinding(Exchange rightsExchange, Queue rightsNewRefundResultQueue) {
        return BindingBuilder.bind(rightsNewRefundResultQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_REFUND_RESULT).noargs();
    }

    @Bean
    public Binding rightsNewRefundResultDelayBinding(Exchange rightsExchange, Queue rightsNewRefundResultDelayQueue) {
        return BindingBuilder.bind(rightsNewRefundResultDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_NEW_REFUND_RESULT_DELAY).noargs();
    }




    @Bean
    public Binding chargeChannelApplyBinding(Exchange repayExchange, Queue chargeChannelApplyQueue) {
        return BindingBuilder.bind(chargeChannelApplyQueue).to(repayExchange).with(RoutingKeys.CHARGE_CHANNEL_APPLY).noargs();
    }

    @Bean
    public Binding chargeChannelQueryBinding(Exchange repayExchange, Queue chargeChannelQueryQueue) {
        return BindingBuilder.bind(chargeChannelQueryQueue).to(repayExchange).with(RoutingKeys.CHARGE_CHANNEL_QUERY).noargs();
    }

    @Bean
    public Binding chargeChannelQueryDelayBinding(Exchange repayExchange, Queue chargeChannelQueryDelayQueue) {
        return BindingBuilder.bind(chargeChannelQueryDelayQueue).to(repayExchange).with(RoutingKeys.CHARGE_CHANNEL_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding ocrIdBinding(Exchange ocrExchange, Queue ocrIdQueue) {
        return BindingBuilder.bind(ocrIdQueue).to(ocrExchange).with(RoutingKeys.OCR_ID).noargs();
    }

    @Bean
    public Binding ocrFaceBinding(Exchange ocrExchange, Queue ocrFaceQueue) {
        return BindingBuilder.bind(ocrFaceQueue).to(ocrExchange).with(RoutingKeys.OCR_FACE).noargs();
    }

    @Bean
    public Binding rightsQueryResultDelayBinding(Exchange rightsExchange, Queue rightsQueryResultDelayQueue) {
        return BindingBuilder.bind(rightsQueryResultDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding rightsQuerRefundDelayBinding(Exchange rightsExchange, Queue rightsQuerRefundDelayQueue) {
        return BindingBuilder.bind(rightsQuerRefundDelayQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_REFUND_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding rightsQueryResultBinding(Exchange rightsExchange, Queue rightsQueryResultQueue) {
        return BindingBuilder.bind(rightsQueryResultQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_QUERY).noargs();
    }

    @Bean
    public Binding rightsQueryRefundBinding(Exchange rightsExchange, Queue rightsQuerRefundQueue) {
        return BindingBuilder.bind(rightsQuerRefundQueue).to(rightsExchange).with(RoutingKeys.RIGHTS_REFUND_QUERY).noargs();
    }


    @Bean
    public Binding callbackNotifyBinding(Exchange callbackExchange, Queue callbackNotifyQueue) {
        return BindingBuilder.bind(callbackNotifyQueue).to(callbackExchange).with(RoutingKeys.CALLBACK_NOTIFY).noargs();
    }

    @Bean
    public Binding callbackNotifyDelayBinding(Exchange callbackExchange, Queue callbackNotifyDelayQueue) {
        return BindingBuilder.bind(callbackNotifyDelayQueue).to(callbackExchange).with(RoutingKeys.CALLBACK_NOTIFY_DELAY).noargs();
    }

    @Bean
    public Binding callbackCommonNotifyBinding(Exchange callbackExchange, Queue callbackCommonNotifyQueue) {
        return BindingBuilder.bind(callbackCommonNotifyQueue).to(callbackExchange).with(RoutingKeys.CALLBACK_COMMON_NOTIFY).noargs();
    }

    @Bean
    public Binding callbackCommonNotifyDelayBinding(Exchange callbackExchange, Queue callbackCommonNotifyDelayQueue) {
        return BindingBuilder.bind(callbackCommonNotifyDelayQueue).to(callbackExchange).with(RoutingKeys.CALLBACK_COMMON_NOTIFY_DELAY).noargs();
    }

    @Bean
    public Binding notLoanApplySmsSendBinding(Exchange smsExchange, Queue notLoanApplySmsSendQueue) {
        return BindingBuilder.bind(notLoanApplySmsSendQueue).to(smsExchange).with(RoutingKeys.NOT_LOAN_APPLY_SMS_SEND).noargs();
    }

    @Bean
    public Binding notLoanApplySmsSendDelayBinding(Exchange smsExchange, Queue notLoanApplySmsSendDelayQueue) {
        return BindingBuilder.bind(notLoanApplySmsSendDelayQueue).to(smsExchange).with(RoutingKeys.NOT_LOAN_APPLY_SMS_SEND_DELAY).noargs();
    }

    @Bean
    public Binding notAmountAppliedSmsSendBinding(Exchange smsExchange, Queue notAmountAppliedSmsSendQueue) {
        return BindingBuilder.bind(notAmountAppliedSmsSendQueue).to(smsExchange).with(RoutingKeys.NOT_AMOUNT_APPLIED_SMS_SEND).noargs();
    }

    @Bean
    public Binding notAmountAppliedSmsSendDelayBinding(Exchange smsExchange, Queue notAmountAppliedSmsSendDelayQueue) {
        return BindingBuilder.bind(notAmountAppliedSmsSendDelayQueue).to(smsExchange).with(RoutingKeys.NOT_AMOUNT_APPLIED_SMS_SEND_DELAY).noargs();
    }



    @Bean
    public Binding ocrRegisterBinding(Exchange ocrExchange, Queue ocrRegisterQueue) {
        return BindingBuilder.bind(ocrRegisterQueue).to(ocrExchange).with(RoutingKeys.OCR_REGISTER).noargs();
    }

    @Bean
    public Binding ocrRegisterDelayBinding(Exchange ocrExchange, Queue ocrRegisterDelayQueue) {
        return BindingBuilder.bind(ocrRegisterDelayQueue).to(ocrExchange).with(RoutingKeys.OCR_REGISTER_DELAY).noargs();
    }

    @Bean
    public Binding aggregatePayQueryBinding(Exchange aggregatePayExchange, Queue aggregatePayQueryQueue) {
        return BindingBuilder.bind(aggregatePayQueryQueue).to(aggregatePayExchange).with(RoutingKeys.AGGREGATE_PAY_QUERY).noargs();
    }

    @Bean
    public Binding aggregatePayQueryDelayBinding(Exchange aggregatePayExchange, Queue aggregatePayQueryDelayQueue) {
        return BindingBuilder.bind(aggregatePayQueryDelayQueue).to(aggregatePayExchange).with(RoutingKeys.AGGREGATE_PAY_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding offlineApplyBinding(Exchange offlineExchange, Queue offlineApplyQueue) {
        return BindingBuilder.bind(offlineApplyQueue).to(offlineExchange).with(RoutingKeys.OFFLINE_APPLY).noargs();
    }

    @Bean
    public Binding offlineApplyDelayBinding(Exchange offlineExchange, Queue offlineApplyDelayQueue) {
        return BindingBuilder.bind(offlineApplyDelayQueue).to(offlineExchange).with(RoutingKeys.OFFLINE_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding renewedQueryBinding(Exchange renewedExchange, Queue renewedQueryQueue) {
        return BindingBuilder.bind(renewedQueryQueue).to(renewedExchange).with(RoutingKeys.RENEWED_QUERY).noargs();
    }

    @Bean
    public Binding renewedQueryDelayBinding(Exchange renewedExchange, Queue renewedQueryDelayQueue) {
        return BindingBuilder.bind(renewedQueryDelayQueue).to(renewedExchange).with(RoutingKeys.RENEWED_QUERY_DELAY).noargs();
    }

    @Bean
    public Binding preLoanAuditApplyDelayBinding(Exchange loanExchange, Queue preLoanAuditApplyDelayQueue) {
        return BindingBuilder.bind(preLoanAuditApplyDelayQueue).to(loanExchange).with(RoutingKeys.PRE_LOAN_AUDIT_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding preLoanAuditApplyBinding(Exchange loanExchange, Queue preLoanAuditApplyQueue) {
        return BindingBuilder.bind(preLoanAuditApplyQueue).to(loanExchange).with(RoutingKeys.PRE_LOAN_AUDIT_APPLY).noargs();
    }

    @Bean
    public Binding preLaonResultDelayBinding(Exchange loanExchange, Queue preLoanAuditResultDelayQueue) {
        return BindingBuilder.bind(preLoanAuditResultDelayQueue).to(loanExchange).with(RoutingKeys.PRE_LOAN_AUDIT_RESULT_DELAY).noargs();
    }

    @Bean
    public Binding preLaonAuditResultBinding(Exchange loanExchange, Queue preLoanAuditResultQueue) {
        return BindingBuilder.bind(preLoanAuditResultQueue).to(loanExchange).with(RoutingKeys.PRE_LOAN_AUDIT_RESULT).noargs();
    }

    @Bean
    public Binding serviceRiskMsgSubscribeSendBinding(Exchange serviceMsgSubscriptionExchange, Queue serviceRiskMsgSubscribeSendQueue) {
        return BindingBuilder.bind(serviceRiskMsgSubscribeSendQueue)
            .to(serviceMsgSubscriptionExchange)
            .with(RoutingKeys.SERVICE_RISK_MSG_SUBSCRIBE_SEND).noargs();
    }


    @Bean
    public Binding approvalApplyHuoshanrongBinding(Exchange approvalExchange, Queue approvalApplyHuoshanrongQueue) {
        return BindingBuilder.bind(approvalApplyHuoshanrongQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HUOSHANRONG).noargs();
    }

    @Bean
    public Binding approvalApplyWldBinding(Exchange approvalExchange, Queue approvalApplyWldQueue) {
        return BindingBuilder.bind(approvalApplyWldQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_WLD).noargs();
    }

    @Bean
    public Binding approvalApplyHengxiaohuaBinding(Exchange approvalExchange, Queue approvalApplyHengxiaohuaQueue) {
        return BindingBuilder.bind(approvalApplyHengxiaohuaQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HENGXIOHUA).noargs();
    }

    @Bean
    public Binding approvalApplyHuoshanrongDelayBinding(Exchange approvalExchange, Queue approvalApplyHuoshanrongDelayQueue) {
        return BindingBuilder.bind(approvalApplyHuoshanrongDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HUOSHANRONG_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyWldDelayBinding(Exchange approvalExchange, Queue approvalApplyWldDelayQueue) {
        return BindingBuilder.bind(approvalApplyWldDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_WLD_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyHengxiaohuaDelayBinding(Exchange approvalExchange, Queue approvalApplyHengxiaohuaDelayQueue) {
        return BindingBuilder.bind(approvalApplyHengxiaohuaDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HENGXIOHUA_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyNiWoDaiBinding(Exchange approvalExchange, Queue approvalApplyNiWoDaiQueue) {
        return BindingBuilder.bind(approvalApplyNiWoDaiQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_NIWODAI).noargs();
    }

    @Bean
    public Binding approvalApplyNiWoDaiDelayBinding(Exchange approvalExchange, Queue approvalApplyNiWoDaiDelayQueue) {
        return BindingBuilder.bind(approvalApplyNiWoDaiDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_NIWODAI_DELAY).noargs();
    }
    @Bean
    public Binding approvalApplyJuZiBinding(Exchange approvalExchange, Queue approvalApplyJuZi) {
        return BindingBuilder.bind(approvalApplyJuZi).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_JUZI).noargs();
    }

    @Bean
    public Binding approvalApplyJuZiDelayBinding(Exchange approvalExchange, Queue approvalApplyJuZiDelayQueue) {
        return BindingBuilder.bind(approvalApplyJuZiDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_JUZI_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyYqgBinding(Exchange approvalExchange, Queue approvalApplyYqgQueue) {
        return BindingBuilder.bind(approvalApplyYqgQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_YQG).noargs();
    }

    @Bean
    public Binding approvalApplyYqgDelayBinding(Exchange approvalExchange, Queue approvalApplyYqgDelayQueue) {
        return BindingBuilder.bind(approvalApplyYqgDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_YQG_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyHelloBinding(Exchange approvalExchange, Queue approvalApplyHelloQueue) {
        return BindingBuilder.bind(approvalApplyHelloQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HELLO).noargs();
    }

    @Bean
    public Binding approvalApplyHelloDelayBinding(Exchange approvalExchange, Queue approvalApplyHelloDelayQueue) {
        return BindingBuilder.bind(approvalApplyHelloDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_HELLO_DELAY).noargs();
    }

    @Bean
    public Binding approvalApplyRenPinBinding(Exchange approvalExchange, Queue approvalApplyRenPinQueue) {
        return BindingBuilder.bind(approvalApplyRenPinQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_RENPIN).noargs();
    }

    @Bean
    public Binding approvalApplyRenPinDelayBinding(Exchange approvalExchange, Queue approvalApplyRenPinDelayQueue) {
        return BindingBuilder.bind(approvalApplyRenPinDelayQueue).to(approvalExchange).with(RoutingKeys.APPROVAL_APPLY_RENPIN_DELAY).noargs();
    }

    @Bean
    public Binding revolvingAmountUpdateApplyBinding(Exchange revolvingExchange, Queue revolvingAmountUpdateApplyQueue) {
        return BindingBuilder.bind(revolvingAmountUpdateApplyQueue).to(revolvingExchange).with(RoutingKeys.REVOLVING_AMOUNT_UPDATE_APPLY).noargs();
    }

    @Bean
    public Binding revolvingAmountUpdateApplyDelayBinding(Exchange revolvingExchange, Queue revolvingAmountUpdateApplyDelayQueue) {
        return BindingBuilder.bind(revolvingAmountUpdateApplyDelayQueue).to(revolvingExchange).with(RoutingKeys.REVOLVING_AMOUNT_UPDATE_APPLY_DELAY).noargs();
    }

    @Bean
    public Binding revolvingAmountUpdateResultBinding(Exchange revolvingExchange, Queue revolvingAmountUpdateResultQueue) {
        return BindingBuilder.bind(revolvingAmountUpdateResultQueue).to(revolvingExchange).with(RoutingKeys.REVOLVING_AMOUNT_UPDATE_RESULT).noargs();
    }

    @Bean
    public Binding revolvingAmountUpdateResultDelayBinding(Exchange revolvingExchange, Queue revolvingAmountUpdateResultDelayQueue) {
        return BindingBuilder.bind(revolvingAmountUpdateResultDelayQueue).to(revolvingExchange).with(RoutingKeys.REVOLVING_AMOUNT_UPDATE_RESULT_DELAY).noargs();
    }

    @Bean
    public Binding orderDelayAutoLoanBinding(Exchange loanExchange, Queue orderDelayAutoLoanQueue) {
        return BindingBuilder.bind(orderDelayAutoLoanQueue).to(loanExchange).with(RoutingKeys.ORDER_DELAY_AUTO_LOAN).noargs();
    }

    @Bean
    public Binding orderDelayAutoLoanDelayBinding(Exchange loanExchange, Queue orderDelayAutoLoanDelayQueue) {
        return BindingBuilder.bind(orderDelayAutoLoanDelayQueue).to(loanExchange).with(RoutingKeys.ORDER_DELAY_AUTO_LOAN_DELAY).noargs();
    }

    @Bean
    public Binding riskResultNoticeOutBinding(Exchange riskExchange, Queue riskResultNoticeOutQueue) {
        return BindingBuilder.bind(riskResultNoticeOutQueue).to(riskExchange).with(RoutingKeys.RISK_RESULT_NOTICE_OUT).noargs();
    }
    @Bean
    public Binding riskResultNoticeOutDelayBinding(Exchange riskExchange, Queue riskResultNoticeOutDelayQueue) {
        return BindingBuilder.bind(riskResultNoticeOutDelayQueue).to(riskExchange).with(RoutingKeys.RISK_RESULT_NOTICE_OUT_DELAY).noargs();
    }

    @Bean
    public Queue shmProcessQueue() {
        return QueueBuilder.durable(Queues.SHM_PUSH_CUS_INFO).build();
    }



    @Bean
    public Exchange shmExchange() {
        return ExchangeBuilder.directExchange(Exchanges.SHM_PUSH).build();
    }

    @Bean
    public Binding shmBinding(Exchange shmExchange, Queue shmProcessQueue) {
        return BindingBuilder.bind(shmProcessQueue)
            .to(shmExchange)
            .with(RoutingKeys.SHM_PUSH_CUS_INFO)
            .noargs();
    }

    /**
     * mqService
     */
    @Bean
    public MqService mqService(RabbitTemplate rabbitTemplate) {
        return new MqService(rabbitTemplate);
    }

    public interface Exchanges {

        String APPROVAL = "approval";

        String RISK = "risk";
        String SIGN = "sign";
        String ZY_SIGN = "zysign";
        String CREDIT = "credit";
        String FQL_CREDIT = "fqlCredit";
        String LOAN = "loan";
        String REPAY = "repay";
        String AGGREGATE_PAY = "aggregate.pay";
        String RIGHTS = "rights";
        String CALLBACK = "callback";
        String SMS = "sms";
        String OCR = "ocr";
        String COLL_REPAY = "coll.repay";
        String CHANNEL = "channel";
        // 续借
        String RENEWED = "renewed";

        String OFFLINE = "offline";
        String SERVICE_MSG_SUBSCRIPTION = "service.msg.subscription";

        String REVOLVING = "revolving";

        String SHM_PUSH = "shm.push";

        void exchange();
    }

    public interface Queues {
        String RISK_APPLY = "risk.apply";
        String ARTIFICIAL_RISK_APPLY = "artificial.risk.apply";
        String RISK_APPLY_DELAY = "risk.apply.delay";
        String RISK_LOAN_APPLY = "risk.loan.apply";
        String RISK_LOAN_APPLY_DELAY = "risk.loan.apply.delay";


        String RISK_APPLY_OUT = "risk.apply.out";
        String RISK_APPLY_OUT_DELAY = "risk.apply.out.delay";

        String RISK_QUERY_DELAY = "risk.result.query.delay";
        String RISK_QUERY = "risk.result.query";
        String RISK_QUERY_OUT = "risk.query.out";
        String RISK_QUERY_OUT_DELAY = "risk.query.out.delay";
        String RISK_RESULT_NOTICE_OUT = "risk.result.notice.out";
        String RISK_RESULT_NOTICE_OUT_DELAY = "risk.result.notice.out.delay";

        String BW_RISK_QUERY = "bw.risk.result.query";
        String BW_RISK_NOTIFY = "bw.risk.notify";
        String BW_RISK_LOAN_APPLY = "bw.risk.loan.apply";
        String BW_RISK_LOAN_APPLY_DELAY = "bw.risk.loan.apply.delay";


        String SIGN_APPLY = "sign.apply";
        String ZY_SIGN_APPLY = "zy.sign.apply";
        String SIGN_APPLY_DELAY = "sign.apply.delay";
        String SIGN_QUERY_DELAY = "sign.result.query.delay";
        String SIGN_QUERY = "sign.result.query";
        String SIGN_NEW_QUERY_DELAY_QUEUE = "sign.new.result.query.delay";
        String SIGN_NEW_QUERY_QUEUE = "sign.new.result.query";

        String CREDIT_ROUTE_APPLY = "credit.route.apply";
        String CREDIT_ROUTE_RESULT = "credit.route.result";

        String CREDIT_APPLY_DELAY = "credit.apply.delay";
        String CREDIT_APPLY = "credit.apply";
        String RECREDIT_APPLY = "recredit.apply";
        String CREDIT_QUERY_DELAY = "credit.result.query.delay";
        String CREDIT_QUERY = "credit.result.query";
        String CREDIT_MAYI_ACCESS = "credit.mayi.access";
        String CREDIT_MAYI_ACCESS_DELAY = "credit.mayi.access.delay";
        String CREDIT_USER_FILE_DOWNLOAD = "credit.userfile.download";
        String CREDIT_USER_FILE_DOWNLOAD_DELAY = "credit.userfile.download.delay";

        String FQL_CREDIT_USER_FILE_DOWNLOAD = "fql.credit.userfile.download";
        String FQL_CREDIT_USER_FILE_DOWNLOAD_DELAY = "fql.credit.userfile.download.delay";



        String CREDIT_IMGS_FILE_DOWNLOAD= "imgs.userfile.download";
        String CREDIT_IMGS_FILE_DOWNLOAD_DELAY = "imgs.userfile.download.delay";
        String RECREDIT_QUERY_DELAY = "recredit.result.query.delay";
        String RECREDIT_QUERY = "recredit.result.query";

        String LOAN_APPLY_DELAY = "loan.apply.delay";
        String LOAN_APPLY = "loan.apply";
        String LOAN_QUERY_DELAY = "loan.result.query.delay";
        String LOAN_QUERY = "loan.result.query";

        String REPAY_PLAN_SYNC = "repay.plan.sync";

        String LOAN_RECORD_APPLY = "loan.record.apply";
        String LOAN_RECORD_APPLY_DELAY = "loan.record.apply.delay";
        String LOAN_RECORD_QUERY = "loan.record.result.query";
        String LOAN_RECORD_QUERY_DELAY = "loan.record.result.query.delay";

        String CHARGE_APPLY = "charge.apply";
        String CHARGE_QUERY_DELAY = "charge.result.query.delay";
        String CHARGE_QUERY = "charge.result.query";
        String CHARGE_CHANNEL_APPLY = "charge.channel.apply";
        String CHARGE_CHANNEL_QUERY = "charge.channel.query";
        String CHARGE_CHANNEL_QUERY_DELAY = "charge.channel.query.delay";

        String REPAY_QUERY_DELAY = "repay.result.query.delay";
        String REPAY_QUERY = "repay.result.query";

        String REPAY_NOTIFY_DELAY = "repay.notify.delay";
        String REPAY_NOTIFY = "repay.notify";

        String REPAY_NOTIFY_RESULT_DELAY = "repay.notify.result.delay";
        String REPAY_NOTIFY_RESULT = "repay.notify.result";

        String REPAY_APPLY = "repay.apply";

        String RIGHTS_DUE_BATCH = "rights.due.batch";

        String RIGHTS_APPLY = "rights.apply";
        String RIGHTS_APPLY_DELAY = "rights.apply.delay";
        String RIGHTS_QUERY = "rights.result.query";
        String RIGHTS_QUERY_DELAY = "rights.result.query.delay";
        String RIGHTS_ROUTE_APPLY = "rights.route.apply";
        String RIGHTS_ROUTE_APPLY_DELAY = "rights.route.apply.delay";

        String RIGHTS_REFUND_QUERY = "rights.refund.query";

        String RIGHTS_REFUND_QUERY_DELAY = "rights.refund.query.delay";

        /**
         * 权益相关队列-新
         */
        String RIGHTS_NEW_APPLY = "rights.new.apply";
        String RIGHTS_NEW_APPLY_DELAY = "rights.new.apply.delay";

        String RIGHTS_NEW_RESULT = "rights.new.result";
        String RIGHTS_NEW_RESULT_DELAY = "rights.new.result.delay";

        String RIGHTS_NEW_REFUND_APPLY = "rights.new.refund.apply";
        String RIGHTS_NEW_REFUND_APPLY_DELAY = "rights.new.refund.apply.delay";

        String RIGHTS_NEW_REFUND_RESULT = "rights.new.refund.result";
        String RIGHTS_NEW_REFUND_RESULT_DELAY = "rights.new.refund.result.delay";


        String RIGHTS_DATA_PUSH = "rights.data.push";
        String RIGHTS_DATA_PUSH_DELAY = "rights.data.push.delay";

        String CALLBACK_NOTIFY = "callback.notify";
        String CALLBACK_NOTIFY_DELAY = "callback.notify.delay";
        String CALLBACK_COMMON_NOTIFY = "callback.common.notify";
        String CALLBACK_COMMON_NOTIFY_DELAY = "callback.common.notify.delay";

        String DUE_BATCH_REPAY_DELAY = "due.batch.repay.delay";
        String DUE_BATCH_REPAY = "due.batch.repay";

        String SMS_SEND_DELAY = "sms.send.delay";

        String SMS_SEND_3H_DELAY = "sms.send.3h.delay";
        String SMS_SEND = "sms.send";
        String NOT_LOAN_APPLY_SMS_SEND = "not.loan.apply.sms.send";
        String NOT_LOAN_APPLY_SMS_SEND_DELAY = "not.loan.apply.sms.send.delay";
        String NOT_AMOUNT_APPLIED_SMS_SEND = "not.amount.applied.sms.send";
        String NOT_AMOUNT_APPLIED_SMS_SEND_DELAY = "not.amount.applied.sms.send.delay";
        String OCR_ID = "ocr.id..apply";
        String OCR_BANK = "ocr.bank..apply";
        String OCR_FACE = "ocr.face..apply";

        String OCR_REGISTER = "ocr.register";
        String OCR_REGISTER_DELAY = "ocr.register.delay";

        String DUE_BATCH_PRE_REPAY_FEE_DELAY = "due.batch.preRepayFee.delay";
        String DUE_BATCH_PRE_REPAY_FEE = "due.batch.preRepayFee";

        String CHANNEL_APPLY = "channel.apply";

        String AGGREGATE_PAY_QUERY = "aggregate.pay.query";
        String AGGREGATE_PAY_QUERY_DELAY = "aggregate.pay.query.delay";

        String OFFLINE_APPLY = "offline.apply";

        String OFFLINE_APPLY_DELAY = "offline.apply.delay";
        // 续借标识查询 风控
        String RENEWED_QUERY = "renewed.query";
        String RENEWED_QUERY_DELAY = "renewed.query.delay";


        String PRE_LOAN_AUDIT_APPLY = "pre.loan.audit.apply";
        String PRE_LOAN_AUDIT_APPLY_DELAY = "pre.loan.audit.apply.delay";

        String PRE_LOAN_AUDIT_RESULT = "pre.loan.audit.result";
        String PRE_LOAN_AUDIT_RESULT_DELAY = "pre.loan.audit.result.delay";

        String SERVICE_RISK_MSG_SUBSCRIBE_SEND = "service.risk.msg.subscribe.send";


        /**
         * 进件队列
         */
        String APPROVAL_APPLY_HUOSHANRONG = "approval.apply.huoshanrong";
        String APPROVAL_APPLY_HUOSHANRONG_DELAY = "approval.apply.huoshanrong.delay";

        String APPROVAL_APPLY_HENGXIOHUA = "approval.apply.hengxiaohua";
        String APPROVAL_APPLY_HENGXIOHUA_DELAY = "approval.apply.hengxiaohua.delay";

        String APPROVAL_APPLY_NIWODAI = "approval.apply.niwodai";
        String APPROVAL_APPLY_NIWODAI_DELAY = "approval.apply.niwodai.delay";

        String APPROVAL_APPLY_YQG = "approval.apply.yqg";
        String APPROVAL_APPLY_YQG_DELAY = "approval.apply.yqg.delay";

        String APPROVAL_APPLY_HELLO = "approval.apply.hello";
        String APPROVAL_APPLY_HELLO_DELAY = "approval.apply.hello.delay";

        String APPROVAL_APPLY_JUZI = "approval.apply.juzi";
        String APPROVAL_APPLY_JUZI_DELAY = "approval.apply.juzi.delay";


        String APPROVAL_APPLY_RENPIN = "approval.apply.renpin";
        String APPROVAL_APPLY_RENPIN_DELAY = "approval.apply.renpin.delay";

        String APPROVAL_APPLY_WLD = "approval.apply.wld";
        String APPROVAL_APPLY_WLD_DELAY = "approval.apply.wld.delay";
        /**
         * 循环额度变更
         */
        String REVOLVING_AMOUNT_UPDATE_APPLY = "revolving.amount.update.apply";
        String REVOLVING_AMOUNT_UPDATE_APPLY_DELAY = "revolving.amount.update.apply.delay";

        String REVOLVING_AMOUNT_UPDATE_RESULT = "revolving.amount.update.result";
        String REVOLVING_AMOUNT_UPDATE_RESULT_DELAY = "revolving.amount.update.result.delay";

        String ORDER_DELAY_AUTO_LOAN = "order.delay.auto.loan";
        String ORDER_DELAY_AUTO_LOAN_DELAY = "order.delay.auto.loan.delay";

        String SHM_PUSH_CUS_INFO = "shm.push.cus.info";

        void queue();
    }

    public interface RoutingKeys {

        String RISK_APPLY = "risk.apply";
        String RISK_APPLY_DELAY = "risk.apply.delay";
        String RISK_LOAN_APPLY = "risk.loan.apply";
        String RISK_LOAN_APPLY_DELAY = "risk.loan.apply.delay";

        String RISK_APPLY_OUT = "risk.apply.out";
        String RISK_APPLY_OUT_DELAY = "risk.apply.out.delay";

        String RISK_QUERY_DELAY = "risk.query.delay";
        String RISK_QUERY = "risk.query";
        String RISK_QUERY_OUT = "risk.query.out";
        String RISK_QUERY_OUT_DELAY = "risk.query.out.delay";
        String RISK_RESULT_NOTICE_OUT = "risk.result.notice.out";
        String RISK_RESULT_NOTICE_OUT_DELAY = "risk.result.notice.out.delay";

        String BW_RISK_QUERY = "bw.risk.query";
        String BW_RISK_NOTIFY = "bw.risk.notify";
        String BW_RISK_LOAN_APPLY = "bw.risk.loan.apply";
        String BW_RISK_LOAN_APPLY_DELAY = "bw.risk.loan.apply.delay";

        String SIGN_APPLY = "sign.apply";
        String ZY_SIGN_APPLY = "zy.sign.apply";
        String SIGN_APPLY_DELAY = "sign.apply.delay";
        String SIGN_QUERY_DELAY = "sign.query.delay";
        String SIGN_QUERY = "sign.query";
        String SIGN_NEW_QUERY_DELAY_RK = "sign.new.query.delay";
        String SIGN_NEW_QUERY_RK = "sign.new.query";

        String CREDIT_ROUTE_APPLY = "credit.route.apply";
        String CREDIT_ROUTE_RESULT = "credit.route.result";

        String CREDIT_APPLY_DELAY = "credit.apply.delay";
        String CREDIT_APPLY = "credit.apply";
        String RECREDIT_APPLY = "recredit.apply";
        String CREDIT_QUERY_DELAY = "credit.query.delay";
        String CREDIT_QUERY = "credit.query";
        String CREDIT_MAYI_ACCESS = "credit.mayi.access";
        String CREDIT_MAYI_ACCESS_DELAY = "credit.mayi.access.delay";
        String CREDIT_USER_FILE_DOWNLOAD = "credit.userfile.download";

        String FQL_CREDIT_USER_FILE_DOWNLOAD = "fql.credit.userfile.download";
        String FQL_CREDIT_USER_FILE_DOWNLOAD_DELAY = "fql.credit.userfile.download.delay";

        String CREDIT_USER_FILE_DOWNLOAD_DELAY = "credit.userfile.download.delay";
        String CREDIT_IMGS_FILE_DOWNLOAD= "imgs.userfile.download";


        String CREDIT_IMGS_FILE_DOWNLOAD_DELAY = "imgs.userfile.download.delay";


        String RECREDIT_QUERY_DELAY = "recredit.query.delay";
        String RECREDIT_QUERY = "recredit.query";

        String LOAN_APPLY_DELAY = "loan.apply.delay";
        String LOAN_APPLY = "loan.apply";
        String LOAN_QUERY_DELAY = "loan.query.delay";
        String LOAN_QUERY = "loan.query";
        String LOAN_INNER_APPLY = "loan.inner.apply";

        String REPAY_PLAN_SYNC = "repay.plan.sync";

        String LOAN_RECORD_APPLY = "loan.record.apply";
        String LOAN_RECORD_APPLY_DELAY = "loan.record.apply.delay";
        String LOAN_RECORD_QUERY = "loan.record.result.query";
        String LOAN_RECORD_QUERY_DELAY = "loan.record.result.query.delay";

        String CHARGE_APPLY = "charge.apply";
        String CHARGE_QUERY_DELAY = "charge.query.delay";
        String CHARGE_QUERY = "charge.query";
        String CHARGE_CHANNEL_APPLY = "charge.channel.apply";
        String CHARGE_CHANNEL_QUERY = "charge.channel.query";
        String CHARGE_CHANNEL_QUERY_DELAY = "charge.channel.query.delay";

        String REPAY_QUERY_DELAY = "repay.query.delay";
        String REPAY_QUERY = "repay.query";

        String REPAY_NOTIFY_DELAY = "repay.notify.delay";
        String REPAY_NOTIFY = "repay.notify";

        String REPAY_NOTIFY_RESULT_DELAY = "repay.notify.result.delay";
        String REPAY_NOTIFY_RESULT = "repay.notify.result";

        String REPAY_APPLY = "repay.apply";

        String RIGHTS_APPLY = "rights.apply";
        String RIGHTS_APPLY_DELAY = "rights.apply.delay";
        String RIGHTS_QUERY = "rights.query";
        String RIGHTS_QUERY_DELAY = "rights.query.delay";
        String RIGHTS_DATA_PUSH = "rights.data.push";
        String RIGHTS_DATA_PUSH_DELAY = "rights.data.push.delay";
        String RIGHTS_DUE_BATCH = "rights.due.batch";
        String RIGHTS_ROUTE_APPLY = "rights.route.apply";
        String RIGHTS_ROUTE_APPLY_DELAY = "rights.route.apply.delay";

        String RIGHTS_REFUND_QUERY = "rights.refund.query";

        String RIGHTS_REFUND_QUERY_DELAY = "rights.refund.query.delay";


        /**
         * 权益相关队列-新
         */
        String RIGHTS_NEW_APPLY = "rights.new.apply";
        String RIGHTS_NEW_APPLY_DELAY = "rights.new.apply.delay";

        String RIGHTS_NEW_RESULT = "rights.new.result";
        String RIGHTS_NEW_RESULT_DELAY = "rights.new.result.delay";

        String RIGHTS_NEW_REFUND_APPLY = "rights.new.refund.apply";
        String RIGHTS_NEW_REFUND_APPLY_DELAY = "rights.new.refund.apply.delay";

        String RIGHTS_NEW_REFUND_RESULT = "rights.new.refund.result";
        String RIGHTS_NEW_REFUND_RESULT_DELAY = "rights.new.refund.result.delay";


        String CALLBACK_NOTIFY = "callback.notify";
        String CALLBACK_NOTIFY_DELAY = "callback.notify.delay";

        String DUE_BATCH_REPAY_DELAY = "due.batch.repay.delay";
        String DUE_BATCH_REPAY = "due.batch.repay";

        String SMS_SEND = "sms.send";
        String SMS_SEND_DELAY = "sms.send.delay";
        String SMS_SEND_3H_DELAY = "sms.send.3h.delay";

        String NOT_LOAN_APPLY_SMS_SEND = "not.loan.apply.sms.send";
        String NOT_LOAN_APPLY_SMS_SEND_DELAY = "not.loan.apply.sms.send.delay";

        String NOT_AMOUNT_APPLIED_SMS_SEND = "not.amount.applied.sms.send";
        String NOT_AMOUNT_APPLIED_SMS_SEND_DELAY = "not.amount.applied.sms.send.delay";

        String CALLBACK_COMMON_NOTIFY = "callback.common.notify";

        String CALLBACK_COMMON_NOTIFY_DELAY = "callback.common.notify.delay";


        String OCR_ID = "ocr.id.apply";

        String OCR_FACE = "ocr.face.apply";

        String OCR_REGISTER = "ocr.register";
        String OCR_REGISTER_DELAY = "ocr.register.delay";


        String DUE_BATCH_PRE_REPAY_FEE_DELAY = "due.batch.preRepayFee.delay";
        String DUE_BATCH_PRE_REPAY_FEE = "due.batch.preRepayFee";

        String CHANNEL_APPLY = "channel.apply";

        String AGGREGATE_PAY_QUERY = "aggregate.pay.query";
        String AGGREGATE_PAY_QUERY_DELAY = "aggregate.pay.query.delay";


        String OFFLINE_APPLY = "offline.apply";
        String OFFLINE_APPLY_DELAY = "offline.apply.delay";

        // 续借标识查询 风控
        String RENEWED_QUERY = "renewed.query";
        String RENEWED_QUERY_DELAY = "renewed.query.delay";

        String PRE_LOAN_AUDIT_APPLY = "pre.loan.audit.apply";
        String PRE_LOAN_AUDIT_APPLY_DELAY = "pre.loan.audit.apply.delay";

        String PRE_LOAN_AUDIT_RESULT = "pre.loan.audit.result";
        String PRE_LOAN_AUDIT_RESULT_DELAY = "pre.loan.audit.result.delay";

        String SERVICE_RISK_MSG_SUBSCRIBE_SEND = "service.risk.msg.subscribe.send";

        /**
         * 进件队列
         */
        String APPROVAL_APPLY_HUOSHANRONG = "approval.apply.huoshanrong";
        String APPROVAL_APPLY_HUOSHANRONG_DELAY = "approval.apply.huoshanrong.delay";

        String APPROVAL_APPLY_HENGXIOHUA = "approval.apply.hengxiaohua";
        String APPROVAL_APPLY_HENGXIOHUA_DELAY = "approval.apply.hengxiaohua.delay";

        String APPROVAL_APPLY_NIWODAI = "approval.apply.niwodai";
        String APPROVAL_APPLY_NIWODAI_DELAY = "approval.apply.niwodai.delay";

        String APPROVAL_APPLY_JUZI = "approval.apply.juzi";
        String APPROVAL_APPLY_JUZI_DELAY = "approval.apply.juzi.delay";

        String APPROVAL_APPLY_YQG = "approval.apply.yqg";
        String APPROVAL_APPLY_YQG_DELAY = "approval.apply.yqg.delay";

        String APPROVAL_APPLY_HELLO = "approval.apply.hello";
        String APPROVAL_APPLY_HELLO_DELAY = "approval.apply.hello.delay";

        String APPROVAL_APPLY_WLD = "approval.apply.wld";
        String APPROVAL_APPLY_WLD_DELAY = "approval.apply.wld.delay";
        String APPROVAL_APPLY_RENPIN = "approval.apply.renpin";
        String APPROVAL_APPLY_RENPIN_DELAY = "approval.apply.renpin.delay";

        /**
         * 循环额度变更
         */
        String REVOLVING_AMOUNT_UPDATE_APPLY = "revolving.amount.update.apply";
        String REVOLVING_AMOUNT_UPDATE_APPLY_DELAY = "revolving.amount.update.apply.delay";

        String REVOLVING_AMOUNT_UPDATE_RESULT = "revolving.amount.update.result";
        String REVOLVING_AMOUNT_UPDATE_RESULT_DELAY = "revolving.amount.update.result.delay";

        String ORDER_DELAY_AUTO_LOAN = "order.delay.auto.loan";
        String ORDER_DELAY_AUTO_LOAN_DELAY = "order.delay.auto.loan.delay";

        String SHM_PUSH_CUS_INFO = "shm.push.cus.info";

        void routing();
    }
}

package com.maguo.loan.cash.flow.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.maguo.loan.cash.flow.remote.manage.ProjectInfoFeign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目信息管理
 *
 * @Author: Lior
 * @CreateTime: 2025/8/21 15:53
 */
@RestController
@RequestMapping("/manage/projectInfo")
public class ManageProjectController {

    private static final Logger logger = LoggerFactory.getLogger(ManageProjectController.class);

    @Autowired
    private ProjectInfoFeign projectInfoFeign;


    /**
     * 获取项目信息
     *
     * @param projectCode
     * @return
     */
    @PostMapping("/query")
    public RestResult<ProjectInfoDto> query(@RequestBody String projectCode) {
        ProjectInfoDto projectInfoDto = projectInfoFeign.queryProjectInfo(projectCode);
        return RestResult.success(projectInfoDto);
    }
}

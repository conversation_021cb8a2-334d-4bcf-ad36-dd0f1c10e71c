package com.maguo.loan.cash.flow.controller;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.ppd.api.dto.RestResult;
import com.maguo.loan.cash.flow.remote.manage.ProjectInfoFeign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 项目信息管理
 *
 * @Author: Lior
 * @CreateTime: 2025/8/21 15:53
 */
@RestController
@RequestMapping("/manage/projectInfo")
public class ManageProjectController {

    private static final Logger logger = LoggerFactory.getLogger(ManageProjectController.class);

    @Autowired
    private ProjectInfoFeign projectInfoFeign;


    /**
     * 获取项目信息
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    @GetMapping("/query/{projectCode}")
    public RestResult<ProjectInfoDto> query(@PathVariable("projectCode") String projectCode) {
        logger.info("查询项目信息，项目编码：{}", projectCode);
        try {
            ProjectInfoDto projectInfoDto = projectInfoFeign.queryProjectInfo(projectCode);
            return RestResult.success(projectInfoDto);
        } catch (Exception e) {
            logger.error("查询项目信息失败，项目编码：{}", projectCode, e);
            return RestResult.fail("查询项目信息失败：" + e.getMessage());
        }
    }
}

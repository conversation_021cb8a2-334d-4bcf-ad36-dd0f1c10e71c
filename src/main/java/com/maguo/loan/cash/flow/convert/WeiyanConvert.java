package com.maguo.loan.cash.flow.convert;


import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;

import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WeiyanConvert {
    WeiyanConvert INSTANCE = Mappers.getMapper(WeiyanConvert.class);
    int GPS_ARRAY_LENGTH = 2;
    int RELATION_PARENT_CODE = 1;
    int RELATION_SPOUSE_CODE = 2;
    int RELATION_CHILD_CODE = 3;
    int RELAITON_FRIEND_CODE = 20;
    int RELATION_COLLEAGUE_CODE = 30;
    int RELATION_RELATIVE_CODE = 10;
    int RELATION_OTHER = 99;



    static Integer toRelationType(Relation relation) {
        return switch (relation) {
            case PARENTS -> RELATION_PARENT_CODE;
            case SPOUSE -> RELATION_SPOUSE_CODE;
            case CHILDREN -> RELATION_CHILD_CODE;
            case SIBLING, RELATIVE -> RELATION_RELATIVE_CODE;
            case FRIEND -> RELAITON_FRIEND_CODE;
            case COLLEAGUE -> RELATION_COLLEAGUE_CODE;
            case CLASSMATE -> RELATION_OTHER;
            case FAMILY -> RELATION_OTHER;
            case UNKNOWN -> RELATION_OTHER;
        };
    }

    @Named("toCertValidEnd")
    static String toCertValidEnd(UserOcr ocr) {
        LocalDate certValidEnd = ocr.getCertValidEnd().isAfter(BaseConstants.DEFAULT_LONG_CERT_END)
            ? BaseConstants.DEFAULT_LONG_CERT_END : ocr.getCertValidEnd();
        return DateUtil.formatLocalDate(certValidEnd);
    }




    @Named("extractLongitude")
    static String toLongitude(String gps) {
        if (gps == null || gps.isEmpty()) {
            return "";
        }
        String[] split = gps.split(",");
        if (split.length < GPS_ARRAY_LENGTH) {
            return "";
        }
        if ("null".equals(split[0])) {
            return "";
        }
        try {
            return new BigDecimal(split[0]).toPlainString();
        } catch (Exception e) {
            return "";
        }
    }


    @Named("extractLatitude")
    static String toLatitude(String gps) {
        if (gps == null || gps.isEmpty()) {
            return "";
        }
        String[] split = gps.split(",");
        if (split.length < GPS_ARRAY_LENGTH) {
            return "";
        }
        if ("null".equals(split[1])) {
            return "";
        }
        try {
            return new BigDecimal(split[1]).toPlainString();
        } catch (Exception e) {
            return "";
        }
    }


    @Named("toFaceResult")
    static Integer toFaceResult(BigDecimal faceScore) {
        if (faceScore == null) {
            return 0;
        }
        return RELATION_PARENT_CODE;
    }

    /**
     * 计算活体认证分数
     *
     * <p>max(min(刷脸分数 / 100, 1), 0)</p>
     *
     * @param faceScore
     * @return
     */
    @Named("toFaceScore")
    static BigDecimal toFaceScore(BigDecimal faceScore) {
        if (faceScore == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal score = faceScore.movePointLeft(RELATION_SPOUSE_CODE);
        if (score.compareTo(BigDecimal.ONE) > 0) {
            score = BigDecimal.ONE;
        }
        if (score.compareTo(BigDecimal.ZERO) < 0) {
            score = BigDecimal.ZERO;
        }
        return score;
    }


    @Named("formatDateTime")
    default String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }


}

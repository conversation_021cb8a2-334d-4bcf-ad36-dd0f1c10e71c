package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "agreement_signature_record")
public class AgreementSignatureRecord extends BaseEntity {
    private String userId;
    private String riskId;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 阶段
     */
    @Enumerated(EnumType.STRING)
    private LoanStage loanStage;
    /**
     * 模板号
     */
    private String templateNo;

 //   @Enumerated(EnumType.STRING)
    private String agreementType;
    /**
     * 公共参数
     */
    private String address;
    /**
     * 公共参数
     */
    private String bankMobilePhone;
    /**
     * 公共参数
     */
    private String identNo;
    /**
     * 公共参数
     */
    private String personName;
    /**
     * 签署状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState signState;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 公共返回userId
     */
    private String commonUserId;
    /**
     * 公共返回taskId
     */
    private String commonTaskId;
    /**
     * 公共返回路径
     */
    private String commonOssKey;
    /**
     * 公共返回地址
     */
    private String commonOssUrl;
    /**
     * 公共返回contract_no
     */
    private String commonContractNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public String getAgreementType() {
        return agreementType;
    }

    public void setAgreementType(String agreementType) {
        this.agreementType = agreementType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBankMobilePhone() {
        return bankMobilePhone;
    }

    public void setBankMobilePhone(String bankMobilePhone) {
        this.bankMobilePhone = bankMobilePhone;
    }

    public String getIdentNo() {
        return identNo;
    }

    public void setIdentNo(String identNo) {
        this.identNo = identNo;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getCommonUserId() {
        return commonUserId;
    }

    public void setCommonUserId(String commonUserId) {
        this.commonUserId = commonUserId;
    }

    public String getCommonTaskId() {
        return commonTaskId;
    }

    public void setCommonTaskId(String commonTaskId) {
        this.commonTaskId = commonTaskId;
    }

    public String getCommonOssKey() {
        return commonOssKey;
    }

    public void setCommonOssKey(String commonOssKey) {
        this.commonOssKey = commonOssKey;
    }

    public String getCommonOssUrl() {
        return commonOssUrl;
    }

    public void setCommonOssUrl(String commonOssUrl) {
        this.commonOssUrl = commonOssUrl;
    }

    public String getCommonContractNo() {
        return commonContractNo;
    }

    public void setCommonContractNo(String commonContractNo) {
        this.commonContractNo = commonContractNo;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public LoanStage getLoanStage() {
        return loanStage;
    }

    public void setLoanStage(LoanStage loanStage) {
        this.loanStage = loanStage;
    }

    public ProcessState getSignState() {
        return signState;
    }

    public void setSignState(ProcessState signState) {
        this.signState = signState;
    }

    public String getRiskId() {
        return riskId;
    }

    public void setRiskId(String riskId) {
        this.riskId = riskId;
    }

    @Override
    protected String prefix() {
        return "UF";
    }
}

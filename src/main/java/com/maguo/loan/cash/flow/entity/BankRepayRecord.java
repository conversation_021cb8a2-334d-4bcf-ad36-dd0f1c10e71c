package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "bank_repay_record")
public class BankRepayRecord extends BaseEntity {
    private String sourceRecordId;
    /**
     * 借据
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;
    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;
    /**
     * 还款类型
     */
    @Enumerated(EnumType.STRING)
    private RepayType repayType;
    /**
     * 总额
     */
    private BigDecimal amount;
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 融担费
     */
    private BigDecimal guarantee;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 平台罚息（对客罚息-对资罚息）
     */
    private BigDecimal platformPenalty;
    /**
     * 咨询费
     */
    private BigDecimal consultFee;
    /**
     * 违约金
     */
    private BigDecimal breach;
    /**
     * 还款|通知状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState state;

    /**
     * 还款时间
     */
    private LocalDateTime repayTime;

    /**
     * 失败原因
     */
    private String failReason;
    /**
     * core流水号
     */
    private String bankRepayNo;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getSourceRecordId() {
        return sourceRecordId;
    }

    public void setSourceRecordId(String sourceRecordId) {
        this.sourceRecordId = sourceRecordId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }


    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getGuarantee() {
        return guarantee;
    }

    public void setGuarantee(BigDecimal guarantee) {
        this.guarantee = guarantee;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getPlatformPenalty() {
        return platformPenalty;
    }

    public void setPlatformPenalty(BigDecimal platformPenalty) {
        this.platformPenalty = platformPenalty;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreach() {
        return breach;
    }

    public void setBreach(BigDecimal breach) {
        this.breach = breach;
    }


    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getBankRepayNo() {
        return bankRepayNo;
    }

    public void setBankRepayNo(String bankRepayNo) {
        this.bankRepayNo = bankRepayNo;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public ProcessState getState() {
        return state;
    }

    public void setState(ProcessState state) {
        this.state = state;
    }

    public LocalDateTime getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    @Override
    protected String prefix() {
        return "BR";
    }
}

package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.OperationSource;
import com.maguo.loan.cash.flow.enums.OuterFlag;
import com.maguo.loan.cash.flow.enums.PaySide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.WhetherDiff;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "custom_repay_record")
public class CustomRepayRecord extends BaseEntity {
    private String outerRepayNo;
    /**
     * 借据
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 申请日期
     */
    private LocalDateTime repayApplyDate;

    @Enumerated(EnumType.STRING)
    private OperationSource operationSource;
    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;
    /**
     * 还款模式,线上线下
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息（银行）
     */
    private BigDecimal interestAmt;
    /**
     * 融担费
     */
    private BigDecimal guaranteeAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 资方罚息
     */
    private BigDecimal capitalPenaltyAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 资方应收融担费
     */
    private BigDecimal capitalGuaranteeAmt;

    /**
     * 额外融担费
     */
    private BigDecimal extraGuaranteeAmt;
    /**
     * 还款总额
     */
    private BigDecimal totalAmt;
    /**
     * 代扣协议号
     */
    private String agreementNo;
    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState repayState;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 还款成功时间
     */
    private LocalDateTime repaidDate;

    @Enumerated(EnumType.STRING)
    private PaySide paySide;


    @Deprecated
    @Enumerated(EnumType.STRING)
    private WhetherState needTwiceState;

    @Deprecated
    private LocalDateTime twiceRepaidDate;

    private BigDecimal consultFee;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 实还平台罚息（对客罚息 - 对资罚息）
     */
    private BigDecimal actPlatformPenaltyAmt;
    /**
     * 代扣内外部标识 OUTER:外部 INNER:内部
     */
    @Enumerated(EnumType.STRING)
    private OuterFlag outerFlag;
    /**
     * 补差账户,是否补差 Y:是 N:否
     */
    @Enumerated(EnumType.STRING)
    private WhetherDiff whetherDiff;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public OuterFlag getOuterFlag() {
        return outerFlag;
    }

    public void setOuterFlag(OuterFlag outerFlag) {
        this.outerFlag = outerFlag;
    }

    public WhetherDiff getWhetherDiff() {
        return whetherDiff;
    }

    public void setWhetherDiff(WhetherDiff whetherDiff) {
        this.whetherDiff = whetherDiff;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public WhetherState getNeedTwiceState() {
        return needTwiceState;
    }

    public void setNeedTwiceState(WhetherState needTwiceState) {
        this.needTwiceState = needTwiceState;
    }

    public LocalDateTime getTwiceRepaidDate() {
        return twiceRepaidDate;
    }

    public void setTwiceRepaidDate(LocalDateTime twiceRepaidDate) {
        this.twiceRepaidDate = twiceRepaidDate;
    }


    public PaySide getPaySide() {
        return paySide;
    }

    public void setPaySide(PaySide paySide) {
        this.paySide = paySide;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDateTime getRepayApplyDate() {
        return repayApplyDate;
    }

    public void setRepayApplyDate(LocalDateTime repayApplyDate) {
        this.repayApplyDate = repayApplyDate;
    }


    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }


    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public LocalDateTime getRepaidDate() {
        return repaidDate;
    }

    public void setRepaidDate(LocalDateTime repaidDate) {
        this.repaidDate = repaidDate;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public OperationSource getOperationSource() {
        return operationSource;
    }

    public void setOperationSource(OperationSource operationSource) {
        this.operationSource = operationSource;
    }

    public BigDecimal getExtraGuaranteeAmt() {
        return extraGuaranteeAmt;
    }

    public void setExtraGuaranteeAmt(BigDecimal extraGuaranteeAmt) {
        this.extraGuaranteeAmt = extraGuaranteeAmt;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public ProcessState getRepayState() {
        return repayState;
    }

    public void setRepayState(ProcessState repayState) {
        this.repayState = repayState;
    }

    @Override
    protected String prefix() {
        return "CRR";
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getCapitalGuaranteeAmt() {
        return capitalGuaranteeAmt;
    }

    public void setCapitalGuaranteeAmt(BigDecimal capitalGuaranteeAmt) {
        this.capitalGuaranteeAmt = capitalGuaranteeAmt;
    }

    public BigDecimal getCapitalPenaltyAmt() {
        return capitalPenaltyAmt;
    }

    public void setCapitalPenaltyAmt(BigDecimal capitalPenaltyAmt) {
        this.capitalPenaltyAmt = capitalPenaltyAmt;
    }

    public BigDecimal getActPlatformPenaltyAmt() {
        return actPlatformPenaltyAmt;
    }

    public void setActPlatformPenaltyAmt(BigDecimal actPlatformPenaltyAmt) {
        this.actPlatformPenaltyAmt = actPlatformPenaltyAmt;
    }
}

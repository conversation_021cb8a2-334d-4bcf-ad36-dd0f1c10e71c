package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "order")
public class Order extends BaseEntity {

    /**
     *渠道标签
     */
    private String applyChannel;
    private String id;
    private String userId;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    @Enumerated(EnumType.STRING)
    private AmountType amountType;

    @Enumerated(EnumType.STRING)
    private ApplicationSource applicationSource;

    @Enumerated(EnumType.STRING)
    private WhetherState renewedFlag;

    /**
     * 外部用户id
     */
    private String openId;
    /**
     * 外部订单号
     */
    private String outerOrderId;

    /**
     * 内部风控id
     */
    private String riskId;


    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 申请金额
     */
    private BigDecimal applyAmount;
    /**
     * 申请期数
     */
    private Integer applyPeriods;
    /**
     * 借款用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;
    /**
     * 订单状态
     */
    @Enumerated(EnumType.STRING)
    private OrderState orderState;

    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;


    /**
     * 放款卡id
     */
    private String loanCardId;
    /**
     * 权益包id
     */
    private String rightsPackageId;

    /**
     * 是否已绑资方卡
     */
    @Enumerated(EnumType.STRING)
    private WhetherState bindCapitalCardState;

    /**
     * 是否已提交放款
     */
    @Enumerated(EnumType.STRING)
    private WhetherState orderSubmitState;
    /**
     * 权益打标记号
     */
    @Enumerated(EnumType.STRING)
    private WhetherState rightsMarking;
    /**
     * 审批金额
     */
    private BigDecimal approveAmount;
    /**
     * 审批权益等级
     */
    @Enumerated(EnumType.STRING)
    private RightsLevel approveRights;

    /**
     * 风控审批利率
     */
    @Enumerated(EnumType.STRING)
    private RateLevel approveRate;

    /**
     * 对客利率（金融）
     */
    private BigDecimal irrRate;

    /**
     * 月供
     */
    private BigDecimal monthPay;


    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证
     */
    private String certNo;
    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;
    /**
     * 是否强制购买权益
     */
    @Enumerated(EnumType.STRING)
    private WhetherState approveRightsForce;
    /**
     * 权益配置id
     */
    private String rightsConfigId;
    /**
     * 外部放款请求流水号
     */
    private String outerLoanNo;

    /**
     * 是否含权益
     * Y:含权益：N:不含权益
     */
    @Enumerated(EnumType.STRING)
    private IsIncludingEquity isIncludingEquity;

    /**
     * 权益收取方
     * O：外部,I：内部（默认O：外部）
     */
    @Enumerated(EnumType.STRING)
    private EquityRecipient equityRecipient;

    /**
     * 项目唯一编码
     */
    private String projectCode;

    public IsIncludingEquity getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(IsIncludingEquity isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public EquityRecipient getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(EquityRecipient equityRecipient) {
        this.equityRecipient = equityRecipient;
    }

    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public AmountType getAmountType() {
        return amountType;
    }

    public void setAmountType(AmountType amountType) {
        this.amountType = amountType;
    }

    public ApplicationSource getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(ApplicationSource applicationSource) {
        this.applicationSource = applicationSource;
    }

    public WhetherState getRenewedFlag() {
        return renewedFlag;
    }

    public void setRenewedFlag(WhetherState renewedFlag) {
        this.renewedFlag = renewedFlag;
    }

    public String getRightsConfigId() {
        return rightsConfigId;
    }

    public void setRightsConfigId(String rightsConfigId) {
        this.rightsConfigId = rightsConfigId;
    }

    /**
     *是否调用中原风控
     */
    @Enumerated(EnumType.STRING)
    private WhetherState callZyRisk;

    public BigDecimal getRightsAmount() {
        return rightsAmount;
    }

    public void setRightsAmount(BigDecimal rightsAmount) {
        this.rightsAmount = rightsAmount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOuterOrderId() {
        return outerOrderId;
    }

    public void setOuterOrderId(String outerOrderId) {
        this.outerOrderId = outerOrderId;
    }

    public String getRiskId() {
        return riskId;
    }

    public void setRiskId(String riskId) {
        this.riskId = riskId;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getApplyPeriods() {
        return applyPeriods;
    }

    public void setApplyPeriods(Integer applyPeriods) {
        this.applyPeriods = applyPeriods;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public OrderState getOrderState() {
        return orderState;
    }

    public void setOrderState(OrderState orderState) {
        this.orderState = orderState;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getLoanCardId() {
        return loanCardId;
    }

    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    public String getRightsPackageId() {
        return rightsPackageId;
    }

    public void setRightsPackageId(String rightsPackageId) {
        this.rightsPackageId = rightsPackageId;
    }

    public WhetherState getBindCapitalCardState() {
        return bindCapitalCardState;
    }

    public void setBindCapitalCardState(WhetherState bindCapitalCardState) {
        this.bindCapitalCardState = bindCapitalCardState;
    }

    public WhetherState getRightsMarking() {
        return rightsMarking;
    }

    public void setRightsMarking(WhetherState rightsMarking) {
        this.rightsMarking = rightsMarking;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public RightsLevel getApproveRights() {
        return approveRights;
    }

    public void setApproveRights(RightsLevel approveRights) {
        this.approveRights = approveRights;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public void setIrrRate(BigDecimal irrRate) {
        this.irrRate = irrRate;
    }

    public BigDecimal getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(BigDecimal monthPay) {
        this.monthPay = monthPay;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public WhetherState getOrderSubmitState() {
        return orderSubmitState;
    }

    public void setOrderSubmitState(WhetherState orderSubmitState) {
        this.orderSubmitState = orderSubmitState;
    }

    @Override
    protected String prefix() {
        return "OR";
    }

    public RateLevel getApproveRate() {
        return approveRate;
    }

    public void setApproveRate(RateLevel approveRate) {
        this.approveRate = approveRate;
    }


    public WhetherState getApproveRightsForce() {
        return approveRightsForce;
    }

    public void setApproveRightsForce(WhetherState approveRightsForce) {
        this.approveRightsForce = approveRightsForce;
    }

    public WhetherState getCallZyRisk() {
        return callZyRisk;
    }

    public void setCallZyRisk(WhetherState callZyRisk) {
        this.callZyRisk = callZyRisk;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getOuterLoanNo() {
        return outerLoanNo;
    }

    public void setOuterLoanNo(String outerLoanNo) {
        this.outerLoanNo = outerLoanNo;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}

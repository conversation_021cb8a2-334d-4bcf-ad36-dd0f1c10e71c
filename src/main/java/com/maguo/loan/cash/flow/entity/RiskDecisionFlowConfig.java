package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ApplyType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "risk_decision_flow_config")
public class RiskDecisionFlowConfig extends BaseEntity {
    //决策流编号
    private String decisionFlowNo;

    //密钥
    private String secretKey;

    //阶段
    @Enumerated(EnumType.STRING)
    private ApplyType stage;

    //项目唯一编码
    private String projectCode;
}

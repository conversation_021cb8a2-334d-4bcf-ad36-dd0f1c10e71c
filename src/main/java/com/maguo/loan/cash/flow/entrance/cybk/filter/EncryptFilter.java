package com.maguo.loan.cash.flow.entrance.cybk.filter;

import com.alibaba.fastjson.JSONObject;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.cybk.config.CYBKConfig;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonData;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonRequest;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonResponse;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKResponseHeader;
import com.maguo.loan.cash.flow.entrance.cybk.enums.CYBKResultCode;
import com.maguo.loan.cash.flow.entrance.cybk.exception.CYBKBizException;
import com.maguo.loan.cash.flow.entrance.cybk.filter.utils.DataCryptoUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class EncryptFilter extends HttpFilter {

    private static final Logger logger = LoggerFactory.getLogger(EncryptFilter.class);

    private static final int MAX_ID_LENGTH = 32;
    private CYBKConfig config;

    public EncryptFilter(CYBKConfig cybkConfig){
        this.config = cybkConfig;
    }


    @Override
    protected void doFilter(HttpServletRequest req, HttpServletResponse res, FilterChain chain) throws IOException {
        String requestStr = IOUtils.toString(req.getInputStream(), StandardCharsets.UTF_8);
        logger.info("长银 入参原始报文: {}, 请求地址: {}，是否跳过验签:{}", requestStr, req.getRequestURL(), config.isSkipSignVerify());

        res.setCharacterEncoding("utf-8");
        res.setContentType("application/json");

        //请求流水号
        String reqNo = IdGen.genId("CY", 32 - 2);
        boolean aTrue = false;
        try {
            CYBKCommonData requestData = JsonUtil.convertToObject(requestStr, CYBKCommonData.class);
            String decrypted;

            aTrue = StringUtils.equals("true", requestData.getSign());
            if (config.isSkipSignVerify() || aTrue) {
                decrypted = requestData.getJson();
                //获取请求流水号
                CYBKCommonRequest convert = JsonUtil.convertToObject(decrypted, CYBKCommonRequest.class);
                reqNo = convert.getHead().getReqNo();
            }else {
                // 解密业务数据
                boolean signResult = DataCryptoUtils.checkSignAndDecrypt(requestData, config);
                if (!signResult) {
                    logger.error("长银验签失败, 原始报文: {}", requestStr);
                    throw new CYBKBizException(CYBKResultCode.VERIFY_SIGN_ERROR);
                }
                logger.info("长银 入参解密后报文: {}", JsonUtil.toJsonString(requestData));

                decrypted = requestData.getJson();
                logger.info("长银 入参解密后 业务数据: {}", decrypted);

                //获取请求流水号
                CYBKCommonRequest convert = JsonUtil.convertToObject(decrypted, CYBKCommonRequest.class);
                reqNo = convert.getHead().getReqNo();
            }

            ReplaceInputHttpRequestWrapper requestWrapper = new ReplaceInputHttpRequestWrapper(req, decrypted.getBytes(StandardCharsets.UTF_8));
            ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);
            chain.doFilter(requestWrapper, responseWrapper);

            byte[] contentBytes = responseWrapper.getContentAsByteArray();
            String responseStr = new String(contentBytes, StandardCharsets.UTF_8);
            logger.info("长银 出参原始报文: {}", responseStr);

            CYBKCommonResponse cybkCommonResponse = JsonUtil.convertToObject(responseStr, CYBKCommonResponse.class);

            //返回结果加签、加密
//            if (config.isSkipSignVerify() || aTrue) {
//                requestData.setJson(JsonUtil.convertToString(cybkCommonResponse));
//                requestData.setSign(IdGen.genId("CYRDM", MAX_ID_LENGTH));
//                requestData.setRandomStr(IdGen.genId("CYRDM", MAX_ID_LENGTH));
//            }else {
//                requestData = FqlDataCryptoUtils.signAndEncrypt(requestData, cybkCommonResponse, config);
//            }

//            String jsonString = JsonUtil.toJsonString(requestData);
            String jsonString = JsonUtil.toJsonString(cybkCommonResponse);
            logger.info("长银 出参加密后报文: {}", jsonString);

            res.setContentLength(jsonString.getBytes().length);
            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(jsonString.getBytes());
            outputStream.flush();

        } catch (CYBKBizException e) {
            logger.error("长银 调用异常", e);
            String jsonString = getResultString(reqNo,e.getResultCode(),String.valueOf(req.getRequestURL()));
            res.setContentLength(jsonString.getBytes().length);

            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(jsonString.getBytes());
            outputStream.flush();
        } catch (Exception e) {
            logger.error("长银 调用异常", e);
            String jsonString = getResultString(reqNo,CYBKResultCode.SYSTEM_ERROR,String.valueOf(req.getRequestURL()));
            res.setContentLength(jsonString.getBytes().length);

            ServletOutputStream outputStream = res.getOutputStream();
            outputStream.write(jsonString.getBytes());
            outputStream.flush();
        }
    }

    public String getResultString(String reqNo, CYBKResultCode resultCode, String url){
        CYBKResponseHeader responseHeader = new CYBKResponseHeader();
        responseHeader.setReqNo(reqNo);
        responseHeader.setRespNo(IdGen.genId("CY", MAX_ID_LENGTH - 2));
        responseHeader.setRespCode(resultCode.getCode());
        responseHeader.setRespMsg(resultCode.getMsg());

        CYBKCommonResponse cybkCommonResponse = new CYBKCommonResponse();
        cybkCommonResponse.setHead(responseHeader);
        JSONObject resJson = new JSONObject();
        if (url.contains("mycjzygs")){
            resJson.put("status", "01");
            resJson.put("statusDesc","接收失败");
        }else {
            resJson.put("resultCode", "02");
            resJson.put("resultDesc","接收成功");
        }
        cybkCommonResponse.setBody(resJson);

//        CYBKCommonData cybkCommonData = new CYBKCommonData();
//        cybkCommonData.setChannel(config.getChannel());
//        cybkCommonData.setKey("1");
//        cybkCommonData = FqlDataCryptoUtils.signAndEncrypt(cybkCommonData, cybkCommonResponse, config);
//
//        String jsonString = JsonUtil.toJsonString(cybkCommonData);
        String jsonString = JsonUtil.toJsonString(cybkCommonResponse);
        logger.info("长银 出参加密后报文: {}", jsonString);
        return jsonString;
    }

}

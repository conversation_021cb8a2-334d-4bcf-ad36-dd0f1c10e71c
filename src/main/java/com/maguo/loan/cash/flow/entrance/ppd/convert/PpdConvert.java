package com.maguo.loan.cash.flow.entrance.ppd.convert;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.convert.ConvertMappings;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.PpdRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdLoanApplyRecord;
import com.maguo.loan.cash.flow.entrance.common.covert.ProjectConfigMapperHelper;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.ContractInfo;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.LoanApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayTrialRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.RepayContext;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdOrderStatus;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import static com.maguo.loan.cash.flow.convert.UserConvert.CERT_NO_CITY_LENGTH;
import static com.maguo.loan.cash.flow.convert.UserConvert.CERT_NO_DISTRICT_LENGTH;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.DECORATION;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.EDUCATION;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.HEALTH;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.MARRIAGE;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.OTHER;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.SHOPPING;
import static com.maguo.loan.cash.flow.enums.LoanPurpose.TOUR;
import static com.maguo.loan.cash.flow.enums.Relation.CHILDREN;
import static com.maguo.loan.cash.flow.enums.Relation.COLLEAGUE;
import static com.maguo.loan.cash.flow.enums.Relation.FRIEND;
import static com.maguo.loan.cash.flow.enums.Relation.PARENTS;
import static com.maguo.loan.cash.flow.enums.Relation.RELATIVE;
import static com.maguo.loan.cash.flow.enums.Relation.SIBLING;
import static com.maguo.loan.cash.flow.enums.Relation.SPOUSE;
import static com.maguo.loan.cash.flow.enums.Relation.UNKNOWN;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,uses = ConvertMappings .class)
public interface PpdConvert {
    PpdConvert INSTANCE = Mappers.getMapper(PpdConvert.class);

    @Mapping(source = "creditApplyRequest.loanReqNo", target = "orderNo")
    @Mapping(source = "creditApplyRequest.custName", target = "name")
    @Mapping(source = "creditApplyRequest.mobileNo", target = "mobile")
    @Mapping(source = "creditApplyRequest.mobileNo", target = "openId")
    @Mapping(source = "creditApplyRequest.idNo", target = "certNo")
    @Mapping(source = "creditApplyRequest.creditApplyTime", target = "applyTime", qualifiedByName = "toCreditApplyTimeLocalDateTime")
    @Mapping(constant = "INIT", target = "preOrderState")
    @Mapping(constant = "N", target = "isReject")
    //TODO 额度类型
    @Mapping(constant = "SINGLE", target = "amountType")
    @Mapping(source = "creditApplyRequest.loanAmt", target = "applyAmount")
    @Mapping(source = "creditApplyRequest.loanTerm", target = "applyPeriods")
    @Mapping(source = "creditApplyRequest", target = "applyChannel", qualifiedByName = "toApplyChannel")
    @Mapping(constant = "Y", target = "isAssignBankChannel")
    PreOrder toPreOrder(@MappingTarget PreOrder preOrder, CreditApplyRequest creditApplyRequest,
                        ProjectConfigMapperHelper helper);


    @AfterMapping
    default void completePreOrderMapping(@MappingTarget PreOrder preOrder, CreditApplyRequest request,
                                         ProjectConfigMapperHelper helper) {
        String projectCode = request.getProjectCode();
        if (projectCode != null && helper != null) {
            preOrder.setBankChannel(helper.getBankChannelFromProject(projectCode));
            preOrder.setFlowChannel(helper.getFlowChannelFromProject(projectCode));
        }
    }

    @Mapping(source = "contactList", target = "contactList", qualifiedByName = "toContactJson")
    PpdCreditApplyRecord toCreditApplyRecord(CreditApplyRequest creditApplyRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "contactList", target = "contactList", qualifiedByName = "toContactJson")
    PpdCreditApplyRecord toCreditApplyRecord(@MappingTarget PpdCreditApplyRecord ppdCreditApplyRecord, CreditApplyRequest creditApplyRequest);


    //TODO
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "preOrder.openId", target = "id")
    @Mapping(source = "preOrder.certNo", target = "certNo")
    @Mapping(source = "preOrder.mobile", target = "mobile")
    @Mapping(source = "preOrder.name", target = "name")
    @Mapping(source = "ppdCreditApplyRecord.marriage", target = "marriage", qualifiedByName="toMarriage")
    @Mapping(source = "ppdCreditApplyRecord.highestDegree", target = "education", qualifiedByName = "toEducation")
    //TODO 和平台定义不对应
   // @Mapping(source = "ppdCreditApplyRecord.occupation", target = "industry" ,qualifiedByName = "toIndustry")
    @Mapping(source = "ppdCreditApplyRecord.occupation", target = "position", qualifiedByName = "toPosition")
    @Mapping(source = "ppdCreditApplyRecord.address", target = "livingAddress")
    @Mapping(source = "ppdCreditApplyRecord.idNo", target = "livingProvinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "ppdCreditApplyRecord.idNo", target = "livingCityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "ppdCreditApplyRecord.idNo", target = "livingDistrictCode", qualifiedByName = "toDistrictCode")
    UserInfo toUserInfo(PreOrder preOrder, PpdCreditApplyRecord ppdCreditApplyRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "idNo", target = "certNo")
    @Mapping(source = "certificationUnit", target = "certSignOrg")
    //TODO 现居地址 和证件地址 不对应
    @Mapping(source = "address", target = "certAddress")
    @Mapping(source = "idNo", target = "provinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "idNo", target = "cityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "idNo", target = "districtCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "sex", target = "gender", qualifiedByName = "toGender")
    @Mapping(source = "nation", target = "nation")
    UserOcr toUserOcr(PpdCreditApplyRecord ppdCreditApplyRecord);

    @Mapping(source = "contactList", target = "contactList", qualifiedByName = "toContactJson")
    PpdLoanApplyRecord toLoanApplyRecord(LoanApplyRequest loanApplyRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "contactList", target = "contactList", qualifiedByName = "toContactJson")
    PpdLoanApplyRecord toLoanApplyRecord(@MappingTarget PpdLoanApplyRecord ppdLoanApplyRecord, LoanApplyRequest loanApplyRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "loanReqNo", target = "outLoanId")
    @Mapping(source = "repayNo", target = "outRepayId")
    @Mapping(source = "bankAcct", target = "repayCardId")
    @Mapping(source = "repayTerm", target = "periods")
    PpdRepayApplyRecord toRepayApplyRecord(RepayContext request);

    @Mapping(source = "loanId", target = "loanId")
    @Mapping(source = "period", target = "period")
    @Mapping(source = "repayPurpose", target = "repayPurpose")
    @Mapping(constant = "ONLINE", target = "repayMode")
    @Mapping(constant = "REPAY", target = "repayType")
    @Mapping(constant = "CAPITAL", target = "paySide")
    @Mapping(constant = "USER", target = "operationSource")
    OnlineRepayApplyRequest toOnlineApplyRequest(
        RepayContext request, String loanId, Integer period, RepayPurpose repayPurpose);


    @Mappings({
        @Mapping(source = "amount", target = "repayAmount", qualifiedByName = "safeAmount"),
        @Mapping(source = "principal", target = "repayPrincipal", qualifiedByName = "safeAmount"),
        @Mapping(source = "interest", target = "repayInterest", qualifiedByName = "safeAmount"),
        @Mapping(source = "penalty", target = "repayOverdue", qualifiedByName = "safeAmount"),
        @Mapping(source = "breachFee", target = "repayLateFee", qualifiedByName = "safeAmount"),
        @Mapping(source = "consultFee", target = "repayPoundage", qualifiedByName = "safeAmount")
    })
    RepayTrialRespDTO toRepayTrailRes(TrialResultVo trialResultVo);

    @Named("toPpdOrderStatusEnum")
    default PpdOrderStatus toPpdOrderStatusEnum(PreOrderState preOrderState) {
        return switch (preOrderState) {
            case INIT -> PpdOrderStatus.PROCESSING;
            case AUDITING -> PpdOrderStatus.PROCESSING;
            case AUDIT_PASS -> PpdOrderStatus.SUCCESS;
            case AUDIT_REJECT -> PpdOrderStatus.FAIL;
            default -> PpdOrderStatus.SYS_ERROR;
        };
    }


    @Named("toGender")
    static Gender toGender(String idSex) {
        if (StringUtils.isBlank(idSex)) {
            return Gender.UNKNOWN;
        }
        return switch (idSex) {
            case "1" -> Gender.MALE;
            case "0" -> Gender.FEMALE;
            default -> Gender.UNKNOWN;
        };
    }

    @Named("toRelationsCode")
    default Relation toPpdRelationsEnum(String relationsCode) {
        return switch (relationsCode) {
            /**父母*/
            case "03" -> PARENTS;
            /**配偶*/
            case "01" -> SPOUSE;
            /**兄弟姐妹*/
            case "04" -> SIBLING;
            /**朋友*/
            case "05", "00" -> FRIEND;
            /**同事*/
            case "06" -> COLLEAGUE;
            /**子女*/
            case "02" -> CHILDREN;
            /**亲戚*/
            case "07", "08" -> RELATIVE;
            /**未知*/
            default -> UNKNOWN;
        };
    }


    @Named("toProvinceCode")
    default String toProvinceCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, 2) + "0000";
    }

    @Named("toCityCode")
    default String toCityCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_CITY_LENGTH) + "00";
    }

    @Named("toDistrictCode")
    default String toDistrictCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_DISTRICT_LENGTH);
    }

    @Named("toEducation")
    static Education toEducation(String education) {
        if (StringUtils.isBlank(education)) {
            return Education.UNKNOWN;
        }
        return switch (education) {
            case "1" -> Education.MASTER;
            case "2" -> Education.COLLEGE;
            case "3" -> Education.JUNIOR_COLLEGE;
            case "4","5" -> Education.HIGH_SCHOOL;
//            case "5" -> Education.HIGH_SCHOOL;
            case "6" -> Education.JUNIOR_HIGH_SCHOOL;
            default -> Education.UNKNOWN;
        };
    }

    /**
     * 1：工薪族
     * 2：私营业主
     * 3：网店卖家
     * 4：学生
     * 5：公务员
     * 6：无工作/其他
     * 7：国企或事业单位员工
     * 8：民营或外企员工
     * 9：务农
     * 10：自由职业
     * 11：制造业蓝领
     * 12：服务业蓝领（餐饮住宿出行美发等)
     * 13：技术型蓝领（建筑装修货车司机等)
     * 14：民企或外企白领
     * 15：国企或事业单位白领:
     * 16：农民或养殖户
     * 00：其他
     * @param occupation
     * @return
     */

    @Named("toPosition")
    default Position toPosition(String occupation) {

        return switch (occupation) {
            case "5","7","15"-> Position.NINETEEN;
            case "13"-> Position.THIRTEEN;
            case "12"-> Position.EIGHTEEN;
            case "2"-> Position.SIXTEEN;
            case "8","14"-> Position.TWENTY_THREE;
            case "9","16"-> Position.TWENTY_FOUR;
            default -> Position.ELEVEN;
        };
    }

    @Named("toContactJson")
    default String toContactJson(List<ContractInfo> contactList) {
        if (CollectionUtils.isEmpty(contactList)) {
            return "[{}]";
        }
        return JSON.toJSONString(contactList);
    }


    @Named("toPpdLoanStatus")
    static String toPpdLoanStatus(OrderState orderState) {
        return switch (orderState) {
            case INIT, AUDIT_PASS, LOANING, CREDITING, CREDIT_PASS, SUSPENDED -> "99";
            case LOAN_FAIL, LOAN_CANCEL, CREDIT_FAIL -> "01";
            case LOAN_PASS, CLEAR -> "00";
            default -> null;
        };
    }



    @Named("toCreditApplyTimeLocalDateTime")
    static LocalDateTime toCreditApplyTimeLocalDateTime(String creditApplyTime) {
        return LocalDateTime.parse(creditApplyTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }



    @Named("toApplicationSource")
    static ApplicationSource toApplicationSource(String sourceCode) {

        return switch (sourceCode) {
            case "CJCYDL_PPD2" -> ApplicationSource.CJCYDL_PPD2;
            case "CJCYDL_PPD3" -> ApplicationSource.CJCYDL_PPD3;
            default -> null;
        };
    }

    @Named("toBankChannel")
    default BankChannel toBankChannel(String productType) {
        return switch (productType) {
            case "CJCYDL_PPD2" -> BankChannel.CYBK;
            case "CJCYDL_PPD3" -> BankChannel.CYBK;
            case "CJHBXJ_PPD2" -> BankChannel.HXBK;
            case "CJHBXJ_PPD3" -> BankChannel.HXBK;
            default -> null;
        };
    }

    @Named("toMarriage")
    static Marriage toMarriage(String marriage) {

        return switch (marriage) {
            case "2" -> Marriage.MARRIED;
            case "1" -> Marriage.UNMARRIED;
            default -> Marriage.UNKNOWN;
        };
    }


    @Named("toFaceSupplier")
    static String toFaceSupplier(String faceSupplier) {

        return switch (faceSupplier) {
            case "01" -> "拍拍信";
            case "02" -> "linkface";
            case "03" -> "大数据";
            case "04" -> "Face++";
            case "05" -> "腾讯";
            case "06" -> "抖音";
            default -> "";
        };
    }


    @Named("toLoanPurpose")
    static LoanPurpose toLoanPurpose(String loanPurpose) {

        return switch (loanPurpose) {
            case "01" -> SHOPPING;
            case "04" -> HEALTH;
            case "02" -> TOUR;
            case "07" -> MARRIAGE;
            case "03" -> DECORATION;
            case "05" -> EDUCATION;
            default -> OTHER;
        };
    }

    @Named("toActPrincipalAmt")
    static BigDecimal toActPrincipalAmt(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActPrincipalAmt(), BigDecimal.ZERO);
    }

    @Named("toActInterestAmt")
    static BigDecimal toActInterestAmt(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActInterestAmt(), BigDecimal.ZERO);
    }

    @Named("toActPenaltyAmt")
    static BigDecimal toActPenaltyAmt(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActPenaltyAmt(), BigDecimal.ZERO);
    }

    @Named("toActGuaranteeAmt")
    static BigDecimal toActGuaranteeAmt(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActGuaranteeAmt(), BigDecimal.ZERO);
    }

    @Named("toActConsultFee")
    static BigDecimal toActConsultFee(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActConsultFee(), BigDecimal.ZERO);
    }

    @Named("toActAmount")
    static BigDecimal toActAmount(RepayPlan repayPlan) {
        return Objects.requireNonNullElse(repayPlan.getActAmount(), BigDecimal.ZERO);
    }

    @Named("toRemainAmount")
    static BigDecimal toRemainAmount(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actAmount = repayPlan.getActAmount();
        BigDecimal amount = repayPlan.getAmount();
        BigDecimal subtract = AmountUtil.subtract(amount, actAmount);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }

    @Named("toRemainConsultFee")
    static BigDecimal toRemainConsultFee(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actConsultFee = repayPlan.getActConsultFee();
        BigDecimal consultFee = repayPlan.getConsultFee();
        BigDecimal subtract = AmountUtil.subtract(consultFee, actConsultFee);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }


    @Named("toRemainGuaranteeAmt")
    static BigDecimal toRemainGuaranteeAmt(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actGuaranteeAmt = repayPlan.getActGuaranteeAmt();
        BigDecimal guaranteeAmt = repayPlan.getGuaranteeAmt();
        BigDecimal subtract = AmountUtil.subtract(guaranteeAmt, actGuaranteeAmt);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }

    @Named("toRemainPrincipalAmt")
    static BigDecimal toRemainPrincipalAmt(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actPrincipalAmt = repayPlan.getActPrincipalAmt();
        BigDecimal principalAmt = repayPlan.getPrincipalAmt();
        BigDecimal subtract = AmountUtil.subtract(principalAmt, actPrincipalAmt);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }

    @Named("toRemainInterestAmt")
    static BigDecimal toRemainInterestAmt(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actInterestAmt = repayPlan.getActInterestAmt();
        BigDecimal interestAmt = repayPlan.getInterestAmt();
        BigDecimal subtract = AmountUtil.subtract(interestAmt, actInterestAmt);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }

    @Named("toRemainPenaltyAmt")
    static BigDecimal toRemainPenaltyAmt(RepayPlan repayPlan) {
        if (repayPlan == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal actPenaltyAmt = repayPlan.getActPenaltyAmt();
        BigDecimal penaltyAmt = repayPlan.getPenaltyAmt();
        BigDecimal subtract = AmountUtil.subtract(penaltyAmt, actPenaltyAmt);
        return subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
    }

    @Named("safeAmount")
    default BigDecimal safeAmount(BigDecimal amount) {
        return AmountUtil.safeAmount(amount);
    }

    @Named("toApplyChannel")
    static String toApplyChannel(CreditApplyRequest creditApplyRequest) {
        String result = "";
        String sourceCode = creditApplyRequest.getSourceCode();
        if (sourceCode.equals(ApplyChannel.CJCYDL_PPD2.name())) {
            switch (creditApplyRequest.getAccessType()) {
                case "1" -> result = "p004";
                case "2" -> result = "p006";
                default -> result = ApplyChannel.getApplyChannel(sourceCode).getCode();
            }
        } else if (sourceCode.equals(ApplyChannel.CJCYDL_PPD3.name())) {
            switch (creditApplyRequest.getAccessType()) {
                case "1" -> result = "p003";
                case "2" -> result = "p005";
                default -> result = ApplyChannel.getApplyChannel(sourceCode).getCode();
            }
        }
        if (sourceCode.equals(ApplyChannel.CJHBXJ_PPD2.name())) {
            switch (creditApplyRequest.getAccessType()) {
                case "1" -> result = "p004";
                case "2" -> result = "p006";
                default -> result = ApplyChannel.getApplyChannel(sourceCode).getCode();
            }
        } else if (sourceCode.equals(ApplyChannel.CJHBXJ_PPD3.name())) {
            switch (creditApplyRequest.getAccessType()) {
                case "1" -> result = "p003";
                case "2" -> result = "p005";
                default -> result = ApplyChannel.getApplyChannel(sourceCode).getCode();
            }
        }
        return result;
    }

    public static BankChannel toBankChannelPub(String productType) {
        return switch (productType) {
            case "CJCYDL_PPD2" -> BankChannel.CYBK;
            case "CJCYDL_PPD3" -> BankChannel.CYBK;
            case "CJHBXJ_PPD2" -> BankChannel.HXBK;
            case "CJHBXJ_PPD3" -> BankChannel.HXBK;
            default -> null;
        };
    }

}

package com.maguo.loan.cash.flow.entrance.ppd.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.collection.PpdReduceApplyResponse;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OfflineRepayReduce;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PpdRebindRecord;
import com.maguo.loan.cash.flow.entity.PpdRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.ppd.PpdVoucherRecord;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.entrance.ppd.convert.PpdConvert;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.PpdCommonResponse;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.PpdReduceApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.PpdReduceApplyDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.DeductApplyReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.DeductQueryRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.ReductionApplyReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.ReductionApplyRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.ReductionResultReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.ReductionResultRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayApplyRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayNoticeReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayQueryReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayTrialReqDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.RepayTrialRespDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.VoucherRequestDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.VoucherResponseDTO;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.BaseRepayRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.RepayContext;
import com.maguo.loan.cash.flow.entrance.ppd.dto.res.ReduceApplyResponse;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdErrorCode;
import com.maguo.loan.cash.flow.entrance.ppd.enums.PpdRepayStatus;
import com.maguo.loan.cash.flow.entrance.ppd.enums.VoucherType;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.utils.PpdRsaUtil;
import com.maguo.loan.cash.flow.enums.AuditStatus;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.UseState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OfflineRepayReduceRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PpdRebindRecordRepository;
import com.maguo.loan.cash.flow.repository.PpdRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.PpdVoucherRecordRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.service.bound.CapitalCardService;
import com.maguo.loan.cash.flow.service.bound.PlatformCardService;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import com.netflix.discovery.converters.Auto;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 拍拍业务服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PpdRepayService {
    private static final Logger logger = LoggerFactory.getLogger(PpdRepayService.class);

    public static final int LOCK_WAIT_SECOND = 2;
    public static final int LOCK_RELEASE_SECOND = 8;
    @Autowired
    private LockService lockService;
    @Autowired
    private RepayService repayService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CapitalCardService capitalCardService;
    @Autowired
    private PlatformCardService platformCardService;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private UserBankCardRepository userBankCardRepository;
    @Autowired
    private PpdRebindRecordRepository ppdRebindRecordRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private PpdRepayApplyRecordRepository ppdRepayApplyRecordRepository;
    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;
    @Autowired
    private PpdConfig ppdConfig;
    @Autowired
    private TrialService trialService;
    @Autowired
    private OfflineRepayReduceRepository offlineRepayReduceRepository;
    @Autowired
    private PpdVoucherRecordRepository ppdVoucherRecordRepository;
    @Autowired
    private PreOrderRepository preOrderRepository;

    /**
     * 还款试算
     *
     * @param request
     * @return
     */
    public RepayTrialRespDTO trail(RepayTrialReqDTO request) {
        logger.info("拍拍还款试算请求参数:{}", JsonUtil.toJsonString(request));
        Loan loan = loanRepository.findByOuterLoanId(request.getLoanReqNo());
        String loanId = loan.getId();
        if (loan == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        // 还款时间段校验
        repayTrailCheck(loan);
        RepayPurpose repayPurpose = convertToRepayPurpose(request.getRepayType());
        int repayPeriod = request.getRepayTerm();
        // 如果是结清,传入最小未还的期次
        if (RepayPurpose.CLEAR == repayPurpose) {
            if (CollectionUtils.isEmpty(repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL))) {
                throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
            }
        }
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            loanId, repayPeriod, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("拍拍还款试算失败，本期已还款成功,loanId:{},periods:{}", loanId, repayPeriod);
            RepayTrialRespDTO result = new RepayTrialRespDTO();
            result.setStatus(PpdRepayStatus.CLEARED.getCode());
            result.setMsg(PpdRepayStatus.CLEARED.getDesc());
            result.setErrCode(PpdErrorCode.ER00.name());
            return result;
        }

        try {
            TrialResultVo resultVo = trialService.repayTrial(loanId, repayPurpose, repayPeriod, request.getRepayDate());
            RepayTrialRespDTO result = PpdConvert.INSTANCE.toRepayTrailRes(resultVo);
            result.setStatus(PpdRepayStatus.SUCCESS.getCode());
            result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
            logger.info("拍拍还款试算返回:{}", JsonUtil.toJsonString(result));
            return result;
        } catch (Exception e) {
            logger.error("拍拍还款试算异常", e);
            throw new PpdBizException(e.getMessage(), PpdResultCode.SYSTEM_ERROR);
        }
    }

    public RepayApplyRespDTO processRepay(BaseRepayRequest request) {
        // 参数转换
        RepayContext context = convertToContext(request);
        // 还款
        return repay(context);
    }

    public RepayApplyRespDTO repay(RepayContext request) {
        RepayApplyRespDTO response = new RepayApplyRespDTO();

        String outerLoanId = request.getLoanReqNo();
        String repaymentGid = request.getRepayNo();
        //检查重复提交
        boolean existsRecord = ppdRepayApplyRecordRepository.existsByOutRepayId(repaymentGid);
        if (existsRecord) {
            logger.error("拍拍还款申请，重复提交，outerLoanId:{},outRepayId:{}", outerLoanId, repaymentGid);
            CustomRepayRecord repayRecord = customRepayRecordRepository.findByOuterRepayNo(repaymentGid);
            if (Objects.isNull(repayRecord)) {
                response.setStatus(PpdRepayStatus.FAIL.getCode());
                response.setMsg(PpdRepayStatus.FAIL.getDesc());
            } else {
                response.setStatus(PpdRepayStatus.SUCCESS.getCode());
                response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
            }
            return response;
        }
        Loan loan = loanRepository.findByOuterLoanId(outerLoanId);
        if (loan == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        String loanId = loan.getId();
        // 判断是否提前结清
        List<Integer> repayList = new ArrayList<>(Collections.singletonList(request.getRepayTerm()));
        RepayPurpose repayPurpose = RepayPurpose.toPpdRepayType(request.getRepayType());
        int period = getMinPeriod(repayList, repayPurpose);
        Optional<CustomRepayRecord> successRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(loanId, period, ProcessState.SUCCEED);
        if (successRepayRecord.isPresent()) {
            logger.error("拍拍还款申请失败，本期已还款成功,loanId:{},periods:{}", loanId, repayList);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }

        response.setStatus(PpdRepayStatus.SUCCESS.getCode());
        response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        //保存还款记录
        PpdRepayApplyRecord repayApplyRecord = ppdRepayApplyRecordRepository.findByOutRepayId(request.getRepayNo()).orElse(null);
        if (repayApplyRecord == null) {
            repayApplyRecord = PpdConvert.INSTANCE.toRepayApplyRecord(request);
            repayApplyRecord.setLoanId(loan.getId());
            repayApplyRecord.setNeedSmsCode(WhetherState.N);
            repayApplyRecord.setRepayType(request.getRepayType());
            ppdRepayApplyRecordRepository.save(repayApplyRecord);
        }
        Order order = orderRepository.findOrderById(loan.getOrderId());
        //查询还款卡信息
        UserBankCard bankCardList = userBankCardRepository.findById(loan.getRepayCardId()).orElseThrow();
        String channelRepayId = request.getChannelRepayId();
        //如果是没有更换银行卡，就直接发起还款
        if (StringUtil.isBlank(channelRepayId)
            || (StringUtil.isNotBlank(channelRepayId) && channelRepayId.equals(bankCardList.getAgreeNo()))) {
            // 没有贷后换绑卡, 直接发起还款
            ppdRepay(request);
            return response;
        }

        //存在贷后换绑卡
        String lockKey = FlowChannel.PPCJDL.name() + "_bind_apply_" + request.getBankAcct();
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("拍拍还款申请，重复提交，orderNo:{}", order.getId());
                throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
            }

            return exchangeBindApply(request, loan, order.getOuterOrderId());
        } catch (Exception e) {
            logger.error("拍拍还款异常: 存在换绑,资方绑卡申请处理失败", e);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    private void ppdRepay(RepayContext request) {
        Loan loan = loanRepository.findByOuterLoanId(request.getLoanReqNo());
        if (loan == null) {
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        String loanId = loan.getId();
        // 判断是否提前结清
        RepayPurpose repayPurpose = RepayPurpose.toPpdRepayType(request.getRepayType());
        if (repayPurpose == null) {
            throw new PpdBizException("不支持的还款类型");
        }
        //当期还款只传1期
        int repayPeriod = request.getRepayTerm();

        //线上线下逻辑
        if (RepayMode.ONLINE.name().equals(request.getRepayMode())) {
            OnlineRepayApplyRequest repayApplyRequest = PpdConvert.INSTANCE.toOnlineApplyRequest(request, loanId, repayPeriod, repayPurpose);
            repayApplyRequest.setOtuRepayNo(request.getRepayNo());
            repayService.online(repayApplyRequest);
            logger.info("拍拍 还款响应,loan:{}", loanId);
        } else {
            RepayApplyDto repayApplyReq = new RepayApplyDto();
            repayApplyReq.setAmount(request.getRepayAmount());
            repayApplyReq.setOrderId(loan.getOrderId());
            repayApplyReq.setRepayPurpose(com.jinghang.ppd.api.enums.RepayPurpose.valueOf(repayPurpose.toString()));
            repayApplyReq.setPeriod(repayPeriod);
            OfflineRepayApplyRequest offlineRepayApply = ManageConvert.INSTANCE.toRepayApplyRequest(repayApplyReq);
            offlineRepayApply.setWriteOffType(WriteOffTypeEnum.DIRECT);
            offlineRepayApply.setOuterRepayNo(request.getRepayNo());
            offlineRepayApply.setRepayDate(request.getRepayTime());
            offlineRepayApply.setReductNo(request.getApplyReductNo());
            repayService.offline(offlineRepayApply);
        }
    }

    private RepayApplyRespDTO exchangeBindApply(RepayContext request, Loan loan, String orderNo) {
        RepayApplyRespDTO response = new RepayApplyRespDTO();
        response.setStatus(PpdRepayStatus.SUCCESS.getCode());
        response.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        //先将之前处理中的置为失败
        PpdRebindRecord rebindRecordCapital = ppdRebindRecordRepository.
            findByCreditIdAndStateAndBoundSide(orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
        if (rebindRecordCapital != null) {
            rebindRecordCapital.setState(ProcessState.FAILED);
            ppdRebindRecordRepository.save(rebindRecordCapital);
        }

        BindCardRecord capitalBindResult = capitalBindApply(loan, orderNo, request);
        //已签约,直接发起还款
        if (ProcessState.SUCCEED == capitalBindResult.getState()) {
            //绑卡状态变更
            PpdRebindRecord rebindRecord = ppdRebindRecordRepository.findByCreditIdAndStateAndBoundSide(
                orderNo, ProcessState.PROCESSING, BoundSide.CAPITAL);
            Loan loanCard = loanRepository.findById(loan.getId()).orElseThrow();
            rebindRecord.setState(ProcessState.SUCCEED);
            rebindRecord.setBindCardRecordId(loanCard.getRepayCardId());
            ppdRebindRecordRepository.save(rebindRecord);
            //还款
            ppdRepay(request);
            return response;
        }
        logger.error("拍拍还款异常: 存在换绑,资方绑卡申请返回失败,reason:{}", capitalBindResult.getFailReason());
        throw new PpdBizException("还款失败");
    }

    private BindCardRecord capitalBindApply(Loan loan, String orderNo, RepayContext request) {
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getLoanCardId()).orElseThrow();
        ExchangeCardApplyReq exchangeCardApplyReq = new ExchangeCardApplyReq();
        exchangeCardApplyReq.setLoanId(loan.getId());
        exchangeCardApplyReq.setPhone(request.getBankMobile());
        exchangeCardApplyReq.setCardNo(request.getBankAcct());
        exchangeCardApplyReq.setBoundSide(BoundSide.CAPITAL);
        exchangeCardApplyReq.setCardName(request.getAcctName());
        exchangeCardApplyReq.setAgreeNo(request.getChannelRepayId());
        exchangeCardApplyReq.setIdNo(userBankCard.getCertNo());
        CardBin cardBin = platformCardService.queryCardBin(request.getBankAcct());
        if (cardBin == null) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }
        String bankAbbr = cardBin.getBankAbbr();
        exchangeCardApplyReq.setBankCode(bankAbbr);
        exchangeCardApplyReq.setBankName(cardBin.getShortName());
        //资方换绑卡
        BindCardRecord bindCardRecord = capitalCardService.bindExchangeApply(loan, exchangeCardApplyReq);
        PpdRebindRecord rebindRecord = buildPpdRebindRecord(loan, bindCardRecord, orderNo);
        ppdRebindRecordRepository.save(rebindRecord);
        return bindCardRecord;
    }

    //根据还款id查询》对客还款记录表
    public DeductQueryRespDTO repayQuery(RepayQueryReqDTO request) {
        DeductQueryRespDTO result = new DeductQueryRespDTO();

        PpdRepayApplyRecord ppdRepayApplyRecord = ppdRepayApplyRecordRepository.findByOutRepayId(request.getRepayNo()).orElseThrow();

        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByOuterRepayNo(ppdRepayApplyRecord.getOutRepayId());
        if (customRepayRecord == null) {
            result.setStatus(PpdRepayStatus.ERROR.getCode());
            result.setMsg(CommonApiResultCode.QUERY_ERROR_REPAY.getMsg());
            return result;
        }
        // 还款结果
        ProcessState customRepayState = customRepayRecord.getRepayState();
        if (ProcessState.SUCCEED == customRepayState) {
            result.setStatus(PpdRepayStatus.SUCCESS.getCode());
            result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        } else if (ProcessState.FAILED == customRepayState) {
            result.setStatus(PpdRepayStatus.FAIL.getCode());
            result.setMsg(PpdRepayStatus.FAIL.getDesc());
        } else if (ProcessState.PROCESSING == customRepayState) {
            result.setStatus(PpdRepayStatus.PROCESSING.getCode());
            result.setMsg(PpdRepayStatus.PROCESSING.getDesc());
        } else {
            result.setStatus(PpdRepayStatus.SUCCESS.getCode());
            result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        }
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findBySourceRecordId(customRepayRecord.getId());
        if (Objects.isNull(bankRepayRecord)) {
            result.setResultTime(customRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            result.setChannelOrderNo("");
            result.setChannelMsg(customRepayRecord.getFailReason());
            return result;
        }
        result.setResultTime(bankRepayRecord.getUpdatedTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        result.setChannelOrderNo(bankRepayRecord.getBankRepayNo());
        result.setChannelMsg(bankRepayRecord.getFailReason());
        return result;
    }

    public VoucherResponseDTO voucherApply(VoucherRequestDTO request) {
        if (!request.getVoucherType().equals(VoucherType.SETTLEMENT_PROOF.getCode())) {
            throw new PpdBizException("凭证类型不正确");
        }
        VoucherResponseDTO result = new VoucherResponseDTO();
        Loan loan = loanRepository.findByOuterLoanIdAndFlowChannel(request.getLoanReqNo(), FlowChannel.PPCJDL)
            .orElseThrow(() -> new PpdBizException("借款订单不存在"));
        PpdVoucherRecord voucherRecords = ppdVoucherRecordRepository.findByLoanId(loan.getId());
        if (Objects.isNull(voucherRecords)) {
            throw new PpdBizException("凭证不存在");
        }
        String content = voucherRecords.getFilePath() + voucherRecords.getFileName();
        result.setStatus(PpdRepayStatus.SUCCESS.getCode());
        result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        result.setContent(content);
        return result;
    }

    public ReductionApplyRespDTO repayReductionApply(ReductionApplyReqDTO request) {
        ReductionApplyRespDTO result = new ReductionApplyRespDTO();
        Loan loan = loanRepository.findByOuterLoanIdAndFlowChannel(request.getLoanReqNo(), FlowChannel.PPCJDL)
            .orElseThrow(() -> new PpdBizException("借款订单不存在"));
        String loanId = loan.getId();
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, request.getApplyTerm());
        if (repayPlan.getCustRepayState() == RepayState.REPAID) {
            throw new PpdBizException("申请期次已结清");
        }
        TrialResultVo trialResultVo;
        try {
            //还款试算,获取最新的资方罚息
            trialResultVo = trialService.repayTrial(loanId, RepayPurpose.CURRENT, request.getApplyTerm(), request.getApplyDate());
        } catch (Exception e) {
            logger.error("还款试算异常,loanId:{}", loanId, e);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        BigDecimal applyReductAmount = request.getApplyReductAmount();
        //可减免总额=36对客罚息-24资方罚息+12咨询费
        BigDecimal guaranteeAmt = repayPlan.getPenaltyAmt().subtract(trialResultVo.getCapitalPenalty()).add(repayPlan.getConsultFee());
        if (applyReductAmount.compareTo(guaranteeAmt) > 0) {
            throw new PpdBizException("申请减免金额大于可减免总额");
        }
        OfflineRepayReduce repayReduce = new OfflineRepayReduce();
        repayReduce.setOrderId(request.getApplyNo());
        repayReduce.setLoanId(loanId);
        repayReduce.setPeriod(request.getApplyTerm());
        repayReduce.setRepayPurpose(RepayPurpose.CURRENT);
        repayReduce.setReduceAmount(applyReductAmount);
        repayReduce.setConsultFee(repayPlan.getConsultFee());
        repayReduce.setGuaranteeAmt(repayPlan.getGuaranteeAmt());
        repayReduce.setPenaltyAmt(repayPlan.getPenaltyAmt());
        repayReduce.setInterestAmt(repayPlan.getInterestAmt());
        repayReduce.setPrincipalAmt(repayPlan.getPrincipalAmt());
        repayReduce.setAmount(repayPlan.getAmount());
        repayReduce.setActAmount(repayPlan.getAmount().subtract(applyReductAmount));
        repayReduce.setAuditState(AuditStatus.PASS);
        repayReduce.setUseState(UseState.WAIT);
        offlineRepayReduceRepository.save(repayReduce);
        result.setStatus(PpdRepayStatus.SUCCESS.getCode());
        result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        return result;
    }

    public ReductionResultRespDTO repayReductionApplyQuery(ReductionResultReqDTO request) {
        ReductionResultRespDTO result = new ReductionResultRespDTO();
        OfflineRepayReduce repayReduce = offlineRepayReduceRepository.findByOrderId(request.getApplyNo());
        if (repayReduce == null) {
            throw new PpdBizException("减免申请记录不存在");
        }
        result.setStatus(PpdRepayStatus.SUCCESS.getCode());
        result.setMsg(PpdRepayStatus.SUCCESS.getDesc());
        result.setReductAmount(AmountUtil.safeAmount(repayReduce.getReduceAmount()));
        LocalDateTime endOfDay = repayReduce.getCreatedTime()
            .with(LocalTime.MAX);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        result.setEffectiveDate(endOfDay.format(formatter));
        return result;
    }

    public ReduceApplyResponse instReductionApply(PpdReduceApplyRequest applyRequest) {
        try {
            String content = JsonUtil.convertToString(applyRequest);
            PpdCommonResponse request = buildBaseRequest(content);

            logger.info("资方入催加密前请求参数：{}", JsonUtil.toJsonString(request));
            // 加密和签名
            PpdRsaUtil.encryptResponse(request, ppdConfig.getClientPublicKey(), ppdConfig.getServerPrivateKey());

            logger.info("资方入催加密后请求参数：{}", JsonUtil.toJsonString(request));
            String responseBody = HttpUtil.post(ppdConfig.getPpdRequestUrl() + "instReductionApply", JsonUtil.toJsonString(request));

            logger.info("资方入催解密前响应参数：{}", JsonUtil.toJsonString(responseBody));
            PpdCommonRequest response = JsonUtil.convertToObject(responseBody, PpdCommonRequest.class);

            if (!PpdRsaUtil.verify(response, ppdConfig.getClientPublicKey())) {
                logger.error("{} 验签失败, instReductionApply", response.getChannelB());
                throw new PpdBizException(PpdResultCode.SIGN_VERIFY_FAIL);
            }

            String decrypted = PpdRsaUtil.decryptRequest(response.getData(), ppdConfig.getServerPrivateKey());

            logger.info("资方入催解密后响应参数：{}", JsonUtil.toJsonString(decrypted));
            return JsonUtil.convertToObject(decrypted, ReduceApplyResponse.class);

        } catch (JsonProcessingException e) {
            throw new PpdBizException("JSON处理异常");
        } catch (HttpException e) {
            throw new PpdBizException("HTTP通信异常");
        }
    }

    private PpdCommonResponse buildBaseRequest(String content) {
        PpdCommonResponse request = new PpdCommonResponse();
        request.setChannelA("CJCYDL");
        request.setChannelB(FlowChannel.PPCJDL.name());
        request.setServiceId("instReductionApply");
        request.setSeqNo(IdGen.genId());
        request.setTransDate(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        request.setTransTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmssSSS")));
        request.setData(content);
        return request;
    }

    private static PpdRebindRecord buildPpdRebindRecord(Loan byOuterLoanId, BindCardRecord bindCardRecordCapitalRe, String orderNo) {
        PpdRebindRecord rebindRecord = new PpdRebindRecord();
        rebindRecord.setState(ProcessState.PROCESSING);
        rebindRecord.setCreditId(orderNo);
        rebindRecord.setUserId(byOuterLoanId.getUserId());
        rebindRecord.setLoanStage(LoanStage.REPAY.name());
        rebindRecord.setBindCardRecordId(bindCardRecordCapitalRe.getId());
        rebindRecord.setBoundSide(BoundSide.CAPITAL);
        return rebindRecord;
    }

    private int getMinPeriod(List<Integer> repayList, RepayPurpose repayPurpose) {
        //当期还款只传1期
        int repayPeriod = repayList.get(0);
        if (RepayPurpose.CLEAR.equals(repayPurpose)) {
            //取最早的一期
            repayList = repayList.stream().sorted().toList();
            repayPeriod = repayList.get(0);
        }
        return repayPeriod;
    }

    private RepayContext convertToContext(BaseRepayRequest request) {
        RepayContext context = new RepayContext();
        // 设置公共字段
        context.setLoanReqNo(request.getLoanReqNo());
        context.setSourceCode(request.getSourceCode());
        context.setRepayNo(request.getRepayNo());
        context.setRepayType(request.getRepayType());
        context.setRepayTime(request.getRepayTime());
        context.setRepayTerm(request.getRepayTerm());
        context.setRepayAmount(request.getRepayAmount());
        context.setRepayPrincipal(request.getRepayPrincipal());
        context.setRepayInterest(request.getRepayInterest());
        context.setRepayOverdue(request.getRepayOverdue());
        context.setRepayPoundage(request.getRepayPoundage());
        context.setRepayLateFee(request.getRepayLateFee());

        // 设置特有字段
        if (request instanceof DeductApplyReqDTO t) {
            context.setRepayMode(RepayMode.ONLINE.name());
            context.setBankCode(t.getBankCode());
            context.setBankAcct(t.getBankAcct());
            context.setAcctName(t.getAcctName());
            context.setBankMobile(t.getBankMobile());
            context.setBankChannel(t.getBankChannel());
            context.setChannelRepayId(t.getChannelRepayId());
            context.setMainMemberId(t.getMainMemberId());
            context.setPayOrderNo(t.getPayOrderNo());
        } else if (request instanceof RepayNoticeReqDTO t) {
            context.setRepayMode(RepayMode.OFFLINE.name());
            context.setApplyReductNo(t.getApplyReductNo());
            context.setReductAmount(t.getReductAmount());
            context.setReductPrincipal(t.getReductPrincipal());
            context.setReductInterest(t.getReductInterest());
            context.setReductOverdue(t.getReductOverdue());
            context.setReductPoundage(t.getReductPoundage());
        }

        return context;
    }

    private void repayTrailCheck(Loan loan) {
        if (loan.getLoanTime().toLocalDate().isEqual(LocalDate.now())) {
            throw new PpdBizException(ResultCode.REPAY_NOT_SUPPORTED_LOAN_DATE.getMsg());
        }
    }

    private RepayPurpose convertToRepayPurpose(String repayType) {
        return switch (repayType) {
            case "02" -> RepayPurpose.CURRENT;
            case "03" -> RepayPurpose.CLEAR;
            default -> null;
        };
    }

    public PpdReduceApplyResponse reduction(PpdReduceApplyDTO applyRequest) {
        try {
            // sourceCode注入
            PreOrder preOrder = preOrderRepository
                .findByOrderNoAndFlowChannel(applyRequest.getLoanReqNo(), FlowChannel.PPCJDL).orElseThrow((() -> new BizException(ResultCode.ORDER_NOT_EXIST)));
            logger.info("开始注入preOrder.sourceCode:{}",JsonUtil.toJsonString(preOrder));
            applyRequest.setSourceCode(preOrder.getSourceCode());

            // 校验减免金额
            checkAmount(applyRequest);

            String content = JsonUtil.convertToString(applyRequest);
            PpdCommonResponse request = buildBaseRequest(content);

            logger.info("资方入催加密前请求参数：{}", JsonUtil.toJsonString(request));
            // 加密和签名
            PpdRsaUtil.encryptResponse(request, ppdConfig.getClientPublicKey(), ppdConfig.getServerPrivateKey());
            logger.info("资方入催加密后请求参数：{}", JsonUtil.toJsonString(request));


            String responseBody = HttpUtil.post(ppdConfig.getPpdRequestUrl() + "instReductionApply", JsonUtil.toJsonString(request));

            logger.info("资方入催解密前响应参数：{}", JsonUtil.toJsonString(responseBody));
            PpdCommonRequest response = JsonUtil.convertToObject(responseBody, PpdCommonRequest.class);


            String decrypted = PpdRsaUtil.decryptRequest(response.getData(), ppdConfig.getServerPrivateKey());

            logger.info("资方入催解密后响应参数：{}", JsonUtil.toJsonString(decrypted));
            return JsonUtil.convertToObject(decrypted, PpdReduceApplyResponse.class);

        } catch (JsonProcessingException e) {
            throw new PpdBizException("JSON处理异常");
        } catch (HttpException e) {
            throw new PpdBizException("HTTP通信异常");
        }
    }

    private void checkAmount(PpdReduceApplyDTO request) {

        Loan loan = loanRepository.findByOuterLoanIdAndFlowChannel(request.getLoanReqNo(), FlowChannel.PPCJDL)
            .orElseThrow(() -> new PpdBizException("借款订单不存在"));
        String loanId = loan.getId();
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, request.getApplyTerm());
        if (repayPlan.getCustRepayState() == RepayState.REPAID) {
            throw new PpdBizException("申请期次已结清");
        }
        TrialResultVo trialResultVo;
        try {
            // 还款试算, 获取最新的资方罚息
            trialResultVo = trialService.repayTrial(loanId, RepayPurpose.CURRENT, request.getApplyTerm(),
                ppdConfig.isMockTime(LocalDateTime.now()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        } catch (Exception e) {
            logger.error("还款试算异常,loanId:{}", loanId, e);
            throw new PpdBizException(PpdResultCode.SYSTEM_ERROR);
        }
        BigDecimal applyReductAmount = request.getApplyReductAmount();
        //可减免总额=36对客罚息-24资方罚息+12咨询费
        BigDecimal guaranteeAmt = repayPlan.getPenaltyAmt().subtract(trialResultVo.getCapitalPenalty()).add(repayPlan.getConsultFee());
        if (applyReductAmount.compareTo(guaranteeAmt) > 0) {
            throw new PpdBizException("申请减免金额大于可减免总额");
        }

    }


}

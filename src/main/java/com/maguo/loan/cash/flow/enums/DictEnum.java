package com.maguo.loan.cash.flow.enums;


import com.maguo.loan.cash.flow.dto.DictDto;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DictEnum {

    private static final String DIC_EDU = "education";
    private static final String DIC_MARRIAGE = "maritalStatus";
    private static final String DIC_INCOME = "monthlyIncomeType";
    private static final String DIC_HOUSE = "housingType";
    private static final String DIC_CAR = "carType";
    private static final String DIC_INDUSTRY = "industryType";
    private static final String DIC_JOB = "jobCategoryType";
    private static final String DIC_RELATION = "relationshipType";

    private static final String BORROW_PURPOSE = "borrowPurpose";

    private static final String[] ALL_DICTIONARY = {DIC_EDU, DIC_MARRIAGE, DIC_INCOME, DIC_HOUSE, DIC_CAR, DIC_INDUSTRY, DIC_JOB, DIC_RELATION, BORROW_PURPOSE};


    public enum BorrowPurpose {
        TRAVEL("01", "旅游"),
        SHOPPING("02", "购物"),
        EDUCATION("03", "教育"),
        HOME_APPLIANCES("04", "家电"),
        MEDICAL("05", "医疗");

        private String code;
        private String name;

        BorrowPurpose(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static BorrowPurpose getByCode(String code) {
            for (BorrowPurpose borrowPurpose : BorrowPurpose.values()) {
                if (borrowPurpose.getCode().equals(code)) {
                    return borrowPurpose;
                }
            }
            return null;
        }

        public static BorrowPurpose getByName(String name) {
            for (BorrowPurpose borrowPurpose : BorrowPurpose.values()) {
                if (borrowPurpose.getName().equals(name)) {
                    return borrowPurpose;
                }
            }
            return null;
        }


    }


    public enum Education {
        HIGH_SCHOOL("01", "高中及以下"),
        COLLEGE("02", "大专"),
        UNDERGRADUATE("03", "本科"),
        POSTGRADUATE("04", "硕士及以上");

        private String code;
        private String name;

        Education(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Education getByCode(String code) {
            for (Education education : Education.values()) {
                if (education.getCode().equals(code)) {
                    return education;
                }
            }
            return null;
        }

        public static Education getByName(String name) {
            for (Education education : Education.values()) {
                if (education.getName().equals(name)) {
                    return education;
                }
            }
            return null;
        }

    }

    public enum MaritalStatusType {
        UNMARRIED("01", "未婚"),
        MARRIED("02", "已婚"),
        DIVORCED("03", "离异"),
        WIDOWED("05", "丧偶"),
        OTHER("05", "其他");

        private String code;
        private String name;

        MaritalStatusType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static MaritalStatusType getByCode(String code) {
            for (MaritalStatusType maritalStatusType : MaritalStatusType.values()) {
                if (maritalStatusType.getCode().equals(code)) {
                    return maritalStatusType;
                }
            }
            return null;
        }

        public static MaritalStatusType getByName(String name) {
            for (MaritalStatusType maritalStatusType : MaritalStatusType.values()) {
                if (maritalStatusType.getName().equals(name)) {
                    return maritalStatusType;
                }
            }
            return null;
        }

    }

    public enum MonthlyIncomeType {
        BELOW_3000("01", "3000元以下", 3000),
        BETWEEN_3000_AND_5000("02", "3000-5000元", 5000),
        BETWEEN_5000_AND_10000("03", "5000-10000元", 10000),
        BETWEEN_10000_AND_20000("04", "10000-20000元", 20000),
        ABOVE_20000("05", "20000元以上", 25000);

        private String code;

        private Integer money;
        private String name;

        MonthlyIncomeType(String code, String name, Integer money) {
            this.code = code;
            this.name = name;
            this.money = money;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getMoney() {
            return money;
        }

        public static MonthlyIncomeType getByCode(String code) {
            for (MonthlyIncomeType monthlyIncomeType : MonthlyIncomeType.values()) {
                if (monthlyIncomeType.getCode().equals(code)) {
                    return monthlyIncomeType;
                }
            }
            return null;
        }

        public static MonthlyIncomeType getByName(String name) {
            for (MonthlyIncomeType monthlyIncomeType : MonthlyIncomeType.values()) {
                if (monthlyIncomeType.getName().equals(name)) {
                    return monthlyIncomeType;
                }
            }
            return null;
        }

        public static MonthlyIncomeType getByMoney(Integer money) {
            for (MonthlyIncomeType monthlyIncomeType : MonthlyIncomeType.values()) {
                if (monthlyIncomeType.getMoney().equals(money)) {
                    return monthlyIncomeType;
                }
            }
            return MonthlyIncomeType.BELOW_3000;
        }


    }

    public enum HousingType {
        OWNED_HOUSING("01", "自有住房"),
        MORTGAGED_HOUSING("02", "按揭购买住房"),
        RENTED_HOUSING("03", "租房"),
        SELF_BUILT_HOUSING("04", "自建住房");

        private String code;
        private String name;

        HousingType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static HousingType getByCode(String code) {
            for (HousingType housingType : HousingType.values()) {
                if (housingType.getCode().equals(code)) {
                    return housingType;
                }
            }
            return null;
        }

        public static HousingType getByName(String name) {
            for (HousingType housingType : HousingType.values()) {
                if (housingType.getName().equals(name)) {
                    return housingType;
                }
            }
            return null;
        }
    }

    public enum CarType {
        NO_CAR("01", "无车"),
        OWNED_CAR("02", "自有车辆"),
        MORTGAGED_CAR("03", "按揭购买车辆");

        private String code;
        private String name;

        CarType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static CarType getByCode(String code) {
            for (CarType carType : CarType.values()) {
                if (carType.getCode().equals(code)) {
                    return carType;
                }
            }
            return null;
        }

        public static CarType getByName(String name) {
            for (CarType carType : CarType.values()) {
                if (carType.getName().equals(name)) {
                    return carType;
                }
            }
            return null;
        }
    }

    public enum IndustryType {
        BUILDING("01", "建筑业"),
        POWER("02", "电力、热力、燃气及水生产和供应业"),
        TRANSPORTATION("03", "交通运输、仓储和邮政业"),
        EDUCATION("04", "教育"),
        REAL_ESTATE("05", "房地产业"),
        ACCOMMODATION("06", "住宿和餐饮业"),
        WHOLESALE_RETAIL("07", "批发和零售业"),
        RENTAL_BUSINESS("08", "租赁和商务服务业"),
        INFORMATION("09", "信息传输、软件和信息技术服务业"),
        FINANCE("10", "金融业"),
        CULTURE("11", "文化、体育和娱乐业"),
        MANUFACTURING("12", "制造业"),
        WATER_ENVIRONMENT("13", "水利、环境和公共设施管理业"),
        MINING("14", "采矿业"),
        AGRICULTURE("15", "农、林、牧、渔业"),
        RESIDENTIAL_REPAIR("16", "居民服务、修理和其他服务业"),
        HEALTHCARE("17", "卫生和社会工作"),
        RESEARCH_TECHNOLOGY("18", "科学研究和技术服务业"),
        PUBLIC_MANAGEMENT("19", "公共管理、社会保障和社会组织"),
        OTHER("20", "其他");

        private String code;
        private String name;

        IndustryType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static IndustryType getByCode(String code) {
            for (IndustryType industryType : IndustryType.values()) {
                if (industryType.getCode().equals(code)) {
                    return industryType;
                }
            }
            return null;
        }

        public static IndustryType getByName(String name) {
            for (IndustryType industryType : IndustryType.values()) {
                if (industryType.getName().equals(name)) {
                    return industryType;
                }
            }
            return null;
        }
    }

    /**
     *
     *
     */
    public enum JobCategoryType {
        OTHER("01", "其他"),
        FRONT_LINE_WORKER("02", "一线生产、建筑、运输、配送人员"),
        EDUCATOR("03", "学校、培训机构等教育人员"),
        SALES_PERSON("04", "一般销售人员，如保险、房产、汽车等"),
        SELF_EMPLOYED("05", "个体经营及企业法人"),
        PROPERTY_STAFF("06", "物业、保安、保洁人员"),
        SERVICE_STAFF("07", "一般服务人员，如KTV、酒吧、餐饮、网咖等"),
        GOVERNMENT_EMPLOYEE("08", "国家机关、国企央企、事业单位人员"),
        MEDICAL_PROFESSIONAL("09", "医生、医护等医疗人员"),
        TECHNICAL_PROFESSIONAL("10", "专业技术人员"),
        MILITARY("11", "军人");
        private String code;
        private String name;

        JobCategoryType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static JobCategoryType getByCode(String code) {
            for (JobCategoryType jobCategoryType : JobCategoryType.values()) {
                if (jobCategoryType.getCode().equals(code)) {
                    return jobCategoryType;
                }
            }
            return null;
        }

        public static JobCategoryType getByName(String name) {
            for (JobCategoryType jobCategoryType : JobCategoryType.values()) {
                if (jobCategoryType.getName().equals(name)) {
                    return jobCategoryType;
                }
            }
            return null;
        }
    }

    public enum RelationshipType {
        PARENTS("01", "父母"),
        RELATIVES("02", "亲戚"),
        COLLEAGUE("03", "同事"),
        FRIEND("04", "朋友"),
        OTHER("05", "其他"),
        FAMILY("06", "家人"),
        CLASSMATE("07", "同学");

        private String code;
        private String name;

        RelationshipType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public static RelationshipType getByCode(String code) {
            for (RelationshipType relationshipType : RelationshipType.values()) {
                if (relationshipType.getCode().equals(code)) {
                    return relationshipType;
                }
            }
            return null;
        }

        public static RelationshipType getByName(String name) {
            for (RelationshipType relationshipType : RelationshipType.values()) {
                if (relationshipType.getName().equals(name)) {
                    return relationshipType;
                }
            }
            return null;
        }
    }

    public static List<DictDto> getAllDictionaries() {
        return Arrays.stream(ALL_DICTIONARY).map(d -> {
            return new DictDto(d, getEnums(d));
        }).toList();
    }

    public static Map<String, String> getEnums(String enumType) {
        Map<String, String> map = new HashMap<>();
        switch (enumType) {
            case DIC_EDU:
                for (Education enumValue : Education.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_MARRIAGE:
                for (MaritalStatusType enumValue : MaritalStatusType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_INCOME:
                for (MonthlyIncomeType enumValue : MonthlyIncomeType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_HOUSE:
                for (HousingType enumValue : HousingType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_CAR:
                for (CarType enumValue : CarType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_INDUSTRY:
                for (IndustryType enumValue : IndustryType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_JOB:
                for (JobCategoryType enumValue : JobCategoryType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case DIC_RELATION:
                for (RelationshipType enumValue : RelationshipType.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;
            case BORROW_PURPOSE:
                for (BorrowPurpose enumValue : BorrowPurpose.values()) {
                    map.put(enumValue.getCode(), enumValue.getName());
                }
                break;

            // 添加其他枚举类的case
            default:
                throw new IllegalArgumentException("Invalid enum type: " + enumType);
        }
        return map;
    }

}

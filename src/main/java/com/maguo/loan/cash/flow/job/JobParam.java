package com.maguo.loan.cash.flow.job;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public class JobParam {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    private BankChannel bankChannel;

    private FlowChannel flowChannel;

    private List<String> loanIds;

    private String orderId;

    private String userId;

    private List<String> userFileIds;

    private List<String> memberIds;

    private List<BankChannel> bankChannels;

    private String channel;

    public String getChannel() {
        return channel;
    }

    public void setChannel( String channel ) {
        this.channel = channel;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public List<String> getUserFileIds() {
        return userFileIds;
    }

    public void setUserFileIds(List<String> userFileIds) {
        this.userFileIds = userFileIds;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public List<String> getLoanIds() {
        return loanIds;
    }

    public void setLoanIds(List<String> loanIds) {
        this.loanIds = loanIds;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getMemberIds() {
        return memberIds;
    }

    public void setMemberIds(List<String> memberIds) {
        this.memberIds = memberIds;
    }

    public List<BankChannel> getBankChannels() {
        return bankChannels;
    }

    public void setBankChannels(List<BankChannel> bankChannels) {
        this.bankChannels = bankChannels;
    }
}

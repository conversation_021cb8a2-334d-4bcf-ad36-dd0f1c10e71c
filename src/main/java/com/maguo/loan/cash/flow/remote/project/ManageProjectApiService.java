package com.maguo.loan.cash.flow.remote.project;

import com.jinghang.cash.api.ProjectInfoApiService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * HXBK回调服务Feign客户端
 * 用于Flow模块调用Capital模块的HXBK回调处理
 *
 * @Author: Lior
 * @CreateTime: 2025/7/10 22:30
 */
@FeignClient(name = "cash-manage", contextId = "hxbkCallback", path = "/hxbk/callback")
public interface ManageProjectApiService extends ProjectInfoApiService {
}

package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserRiskRecordRepository extends JpaRepository<UserRiskRecord, String> {
    UserRiskRecord findTopByUserIdOrderByCreatedTimeDesc(String userId);

    UserRiskRecord findTopByUserIdAndFlowChannelOrderByCreatedTimeDesc(String userId, FlowChannel flowChannel);

    @Query("select count(*) from UserRiskRecord urr "
        + "where urr.approveResult = 'AUDITING' "
        + "and urr.createdTime > curdate()"
        + "and timestampdiff(minute ,urr.createdTime, now())>5")
    Integer countRiskTimeout();

    @Query(value = "WITH RankedRows AS (  "
        + "SELECT   "
        + "o.apply_periods as apprvTnr,  "
        + "zcar.platform_apply_code as cooppfApplCde,  "
        + "temp.created_time as applyDt,  "
        + "coalesce(o.apply_amount,'0') as realDownAmt,  "
        + "if(c.state = 'SUCCEED', '01','02') as endMark,  "
        + "concat(if(temp.risk_final_result is null,'', '风控综合评分不足'),coalesce(c.fail_reason,'')) as rejectReson,  "
        + "coalesce(o.irr_rate,'0') as priceIntRat,  "
        + "zcar.apply_seq as ysxApplSeq,  "
        + "ROW_NUMBER() OVER (PARTITION BY zcar.platform_apply_code ORDER BY temp.created_time DESC) as rn    "
        + "FROM   "
        + "(SELECT   "
        + "id, created_time, risk_final_result   "
        + "FROM user_risk_record urr   "
        + "WHERE urr.flow_channel = 'ZYCFC'   "
        + "AND urr.call_zy_risk = 'Y'   "
        + "AND urr.created_time >= ?1   "
        + "AND urr.created_time < ?2 ) temp   "
        + "LEFT JOIN `order` o ON o.risk_id = temp.id   "
        + "LEFT JOIN credit c ON o.id = c.order_id   "
        + "LEFT JOIN zycfc_credit_apply_record zcar ON zcar.risk_id = temp.id  "
        + ")  "
        + "SELECT   "
        + "apprvTnr,cooppfApplCde,applyDt,realDownAmt,  "
        + "endMark,rejectReson,priceIntRat,ysxApplSeq  "
        + "FROM  RankedRows WHERE   rn = 1;", nativeQuery = true)
    List<Map<String, String>> findLoanApplyFileUploadData(LocalDateTime startTime, LocalDateTime endTime);

    List<UserRiskRecord> findByApproveResultAndCreatedTimeBetween(AuditState approveResult, LocalDateTime beginTime, LocalDateTime endTime);

    List<UserRiskRecord> findAllByUserId(String userId);

    @Query("select urr from UserRiskRecord urr where urr.userId = ?1 AND (urr.amountType != ?2 or urr.amountType is null)")
    List<UserRiskRecord> findAllByUserIdAndAmountTypeNot(String userId, AmountType amountType);

    List<UserRiskRecord> findAllByApproveResult(AuditState approveResult);

    List<UserRiskRecord> findAllByApproveResultAndCreatedTimeBetween(AuditState approveResult, LocalDateTime beginTime, LocalDateTime endTime);

    int countByUserIdAndFlowChannelAndApproveResultIn(String userId,FlowChannel flowChannel, List<AuditState> approveResult);
    @Query("select count(*) from UserRiskRecord urr "
        + "where urr.approveResult = 'INIT' "
        + "and urr.createdTime > curdate()"
        + "and timestampdiff(minute ,urr.createdTime, now())>5")
    Integer countRiskInitTimeout();

    Optional<UserRiskRecord> findTopByUserIdAndAmountTypeAndApproveResultOrderByCreatedTimeDesc(String id,
                                                                                                AmountType amountType,
                                                                                                AuditState auditState);
    @Query("select u from UserRiskRecord u "
        + "where u.userId = ?1 and (u.amountType != ?2 or u.amountType is null) and u.flowChannel =?3 order by u.createdTime desc limit 1")
    UserRiskRecord findTopByUserIdAndAmountTypeNotAndFlowChannelOrderByCreatedTimeDesc(String userId, AmountType amountType, FlowChannel flowChannel);

    Optional<UserRiskRecord> findTopByUserIdAndAmountTypeAndApplyTypeAndApproveResultOrderByCreatedTimeDesc(String userId,
                                                                                                    AmountType amountType,
                                                                                                    ApplyType applyType,
                                                                                                    AuditState auditState);

    Optional<UserRiskRecord> findByPipelineId(String pipelineId);

    Optional<UserRiskRecord> findByUserIdAndFlowChannelAndApplyChannelAndApplyType(String userId, FlowChannel flowChannel, String applyChannel, ApplyType applyType);

    @Query("""
            select r from UserRiskRecord r
            where r.userId = ?1
            and r.approveResult = ?3
            and r.createdTime > ?2
            and r.flowChannel = ?4
        """)
    List<UserRiskRecord> queryThirtyDayRiskRejectRecord(String userId, LocalDateTime failCreditDate, AuditState reject, FlowChannel flowChannel);
}

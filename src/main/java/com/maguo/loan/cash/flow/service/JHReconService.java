package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.vo.LoanVo;
import com.maguo.loan.cash.flow.entity.vo.RepayAfterVo;
import com.maguo.loan.cash.flow.entity.vo.RepayDetailVo;
import com.maguo.loan.cash.flow.entity.vo.RepayPlanVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 处理鲸航-绿信对账文件
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/05/29 11:31
 */
@Service
public class JHReconService {

    private static final Logger logger = LoggerFactory.getLogger(JHReconService.class);

    private static final int REPAY_DAY_LIMIT = 28;
    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private RepayPlanRepository repayPlanRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;


    /**
     * 获取借款明细对账文件
     * @return loanVoList
     */
    public List<LoanVo> getLoanDetailReconFile( LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel , IsIncludingEquity includingEquity) {
        List<LoanVo> loanVoList = new ArrayList<>();
        // 首先查询当天的放款状态为SUCCEED的记录
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<Loan> loanList = loanRepository.findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(yesterdayStart, yesterdayEnd,
                                                                                             ProcessState.SUCCEED, flowChannel, bankChannel);
        if (loanList != null && loanList.size() > 0) {
            // 遍历这个列表
            for (Loan loan : loanList) {
                if(!includingEquity.equals(loan.getIsIncludingEquity())){
                    continue;
                }
                LoanVo loanVo = new LoanVo();
                // 将loanVo的所有属性都set出来带上注释 不要赋值
                loanVo.setOutAppSeq("");
                loanVo.setCustId(loan.getCreditId());
                loanVo.setApplyDt(loan.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                loanVo.setApplSeq(loan.getOrderId());
                loanVo.setContNo(loan.getLoanContractNo());
                loanVo.setLoanNo(loan.getId());
                loanVo.setDnAmt(loan.getAmount());
                loanVo.setApplyTnr(loan.getPeriods());
                loanVo.setBasicIntRat(loan.getIrrRate());
                loanVo.setLoanActvDt(loan.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                loanVo.setContEndDt(getContEndDt(loan.getLoanTime(), loan.getPeriods()));
                // 通过这个列表的loanCardId 查询这个列表的绑卡记录
                if (loan.getLoanCardId() != null) {
                    Optional<BindCardRecord> bindCardRecord = bindCardRecordRepository.findById(loan.getLoanCardId());
                    if (bindCardRecord.isPresent()) {
                        BindCardRecord bindCardRecord1 = bindCardRecord.get();
                        loanVo.setAcctName(bindCardRecord1.getName());
                        loanVo.setAcctBank(bindCardRecord1.getBankName());
                        loanVo.setAcctNo(bindCardRecord1.getBankCardNo());

                    }
                }
                loanVoList.add(loanVo);
            }
        }
        logger.info("鲸航-绿信借款明细对账文件 : {}", JsonUtil.toJsonString(loanVoList));
        return loanVoList;
    }

    // 获取合同到期日
    public String getContEndDt(LocalDateTime loanTime, int periods) {
        // 贷款起始日期
        LocalDate loanStartDate = loanTime.toLocalDate();
        LocalDate loanEndDate = loanStartDate.plusMonths(periods);
        if (loanEndDate.getDayOfMonth() > REPAY_DAY_LIMIT) {
            loanEndDate = loanEndDate.minusDays(loanEndDate.getDayOfMonth() - REPAY_DAY_LIMIT);
            return loanEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        return loanEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }


    // 获取还款计划对账文件
    public List<RepayPlanVo> getRepayPlanReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel,IsIncludingEquity includingEquity) {
        List<RepayPlanVo> repayPlanVoList = new ArrayList<>();
        // 首先查询前一天的所有还款计划
        // 首先查询当天的放款状态为SUCCEED的记录
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<Loan> loanList = loanRepository.findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(yesterdayStart, yesterdayEnd, ProcessState.SUCCEED,
                                                                                                           flowChannel,bankChannel);
        if (loanList != null && loanList.size() > 0) {
            for (Loan loan : loanList) {
                if(!includingEquity.equals(loan.getIsIncludingEquity())){
                    continue;
                }
                List<RepayPlan> repayPlanList = repayPlanRepository.findByLoanId(loan.getId());
                if (repayPlanList != null && repayPlanList.size() > 0) {
                    for (RepayPlan repayPlan : repayPlanList) {
                        RepayPlanVo repayPlanVo = new RepayPlanVo();
                        repayPlanVo.setOutApplSeq("");
                        repayPlanVo.setLoanNo(repayPlan.getLoanId());
                        repayPlanVo.setThr(repayPlan.getPeriod().toString());
                        repayPlanVo.setDueDt(repayPlan.getPlanRepayDate().toString());
                        repayPlanVo.setInstmAmt(repayPlan.getAmount());
                        repayPlanVo.setPrcpAmt(repayPlan.getPrincipalAmt());
                        repayPlanVo.setIntAmt(repayPlan.getInterestAmt());
                        repayPlanVo.setOdIntAmt(repayPlan.getPenaltyAmt());
                        repayPlanVo.setGuaranteeFeeAmt(repayPlan.getGuaranteeAmt());
                        // 复利金额 担保费逾期费用 暂时都给0
                        repayPlanVo.setCommIntAmt(BigDecimal.valueOf(0));
                        repayPlanVo.setGuaranteeFeeOdAmt(BigDecimal.valueOf(0));
                        repayPlanVo.setConsultFee(repayPlan.getConsultFee());
                        repayPlanVoList.add(repayPlanVo);
                    }
                }
            }
        }
        logger.info("鲸航-绿信还款计划对账文件 : {}", JsonUtil.toJsonString(repayPlanVoList));
        return repayPlanVoList;
    }


    // 获取还款明细对账文件
    public List<RepayDetailVo> getRepayDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel,IsIncludingEquity includingEquity) {
        List<RepayDetailVo> repayDetailVoList = new ArrayList<>();
        // 首先查询前一天的所有还款明细计划
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<CustomRepayRecord> repayRecords = customRepayRecordRepository.findByRepaidDateBetweenAndRepayState(yesterdayStart, yesterdayEnd,  ProcessState.SUCCEED);
        if (repayRecords != null && repayRecords.size() > 0) {
            for (CustomRepayRecord repayRecord : repayRecords) {
                Loan channel = loanRepository.findByIdAndFlowChannelAndBankChannel(repayRecord.getLoanId(), flowChannel, bankChannel).orElse(null);
                if ( channel == null ) {
                    continue;
                }
                if(!includingEquity.equals(channel.getIsIncludingEquity())){
                    continue;
                }
                RepayDetailVo repayDetailVo = new RepayDetailVo();
                repayDetailVo.setLoanNo(repayRecord.getLoanId());
                repayDetailVo.setRepaymentSeq(repayRecord.getOuterRepayNo());
                repayDetailVo.setSetlSeq(repayRecord.getId());
                repayDetailVo.setOutApplSeq("");
                Optional<Loan> loan = loanRepository.findById(repayRecord.getLoanId());
                if (loan.isPresent()) {
                    repayDetailVo.setCustId(loan.get().getUserId());
                    repayDetailVo.setCurrPrincipal(loan.get().getAmount().subtract(repayRecord.getPrincipalAmt()));
                } else {
                    repayDetailVo.setCustId("");
                    repayDetailVo.setCurrPrincipal(BigDecimal.ZERO);
                }
                if ( repayRecord.getRepayPurpose().equals(RepayPurpose.CURRENT) ) {
                    repayDetailVo.setPayMode("01");
                } else if ( repayRecord.getRepayPurpose().equals(RepayPurpose.CLEAR) ) {
                    repayDetailVo.setPayMode("02");
                }
                repayDetailVo.setSetlDt(repayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                repayDetailVo.setSetlTime(repayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")));
                repayDetailVo.setIsCy("Y");
                repayDetailVo.setTotalAmt(repayRecord.getTotalAmt());
                repayDetailVo.setPrcpAmt(repayRecord.getPrincipalAmt());
                repayDetailVo.setIntAmt(repayRecord.getInterestAmt());
                repayDetailVo.setOdIntAmt(repayRecord.getPenaltyAmt());
                repayDetailVo.setCommOdIntAmt(BigDecimal.ZERO);
                repayDetailVo.setConsultFee(repayRecord.getConsultFee());
                repayDetailVo.setBreachAmt(repayRecord.getBreachAmt());
                repayDetailVo.setIsDc("N");
                repayDetailVo.setGuaranteeFeeAmt(BigDecimal.ZERO);
                repayDetailVo.setGuaranteeFeeOdAmt(BigDecimal.ZERO);
                repayDetailVo.setPlatformFlowNo("");
                repayDetailVoList.add(repayDetailVo);

            }
        }
        logger.info("鲸航-绿信还款明细对账文件 : {}", JsonUtil.toJsonString(repayDetailVoList));
        return repayDetailVoList;

    }


    // 获取还款后还款计划对账文件
    public List<RepayAfterVo> getRepayAfterReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel,IsIncludingEquity includingEquity) {
        List<RepayAfterVo> repayAfterVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<RepayPlan> repayPlanList = repayPlanRepository.findByActRepayTimeBetween(yesterdayStart, yesterdayEnd);
        if ( repayPlanList != null && repayPlanList.size() > 0 ) {
            for (RepayPlan repayPlan : repayPlanList) {
                Loan channel = loanRepository.findByIdAndFlowChannelAndBankChannel(repayPlan.getLoanId(), flowChannel, bankChannel).orElse(null);
                if ( channel == null ) {
                    continue;
                }
                if(!includingEquity.equals(channel.getIsIncludingEquity())){
                    continue;
                }
                RepayAfterVo repayAfterVo = new RepayAfterVo();
                repayAfterVo.setOutApplSeq("");
                repayAfterVo.setLoanNo(repayPlan.getLoanId());
                repayAfterVo.setTnr(BigDecimal.valueOf(repayPlan.getPeriod()));
                repayAfterVo.setDueDt(repayPlan.getPlanRepayDate().toString());
                repayAfterVo.setShTotalAmt(repayPlan.getAmount());
                repayAfterVo.setShPrcpAmt(repayPlan.getPrincipalAmt());
                repayAfterVo.setShIntAmt(repayPlan.getInterestAmt());
                repayAfterVo.setShOdIntAmt(repayPlan.getPenaltyAmt());
                repayAfterVo.setShCommIntAmt(BigDecimal.ZERO);
                if ( repayPlan.getActBreachAmt() != null && repayPlan.getConsultFee() != null ) {
                    repayAfterVo.setShFeeAmt(repayPlan.getActBreachAmt().add(repayPlan.getConsultFee()));
                } else if ( repayPlan.getActBreachAmt() != null ) {
                    repayAfterVo.setShFeeAmt(repayPlan.getActBreachAmt());
                } else if ( repayPlan.getConsultFee() != null ) {
                    repayAfterVo.setShFeeAmt(repayPlan.getConsultFee());
                } else {
                    repayAfterVo.setShFeeAmt(BigDecimal.ZERO);
                }
                repayAfterVo.setShGuarAmt(BigDecimal.ZERO);
                repayAfterVo.setShGuarOdAmt(BigDecimal.ZERO);
                repayAfterVo.setAcTotalAmt(repayPlan.getActAmount());
                repayAfterVo.setAcPrcpAmt(repayPlan.getActPrincipalAmt());
                repayAfterVo.setAcIntAmt(repayPlan.getActInterestAmt());
                repayAfterVo.setAcOdIntAmt(repayPlan.getActPenaltyAmt());
                repayAfterVo.setAcCommIntAmt(BigDecimal.ZERO);
                if ( repayPlan.getActBreachAmt() != null && repayPlan.getActConsultFee() != null ) {
                    repayAfterVo.setAcFeeAmt(repayPlan.getActBreachAmt().add(repayPlan.getActConsultFee()));
                } else if ( repayPlan.getActBreachAmt() != null ) {
                    repayAfterVo.setAcFeeAmt(repayPlan.getActBreachAmt());
                } else if ( repayPlan.getActConsultFee() != null ) {
                    repayAfterVo.setAcFeeAmt(repayPlan.getActConsultFee());
                } else {
                    repayAfterVo.setAcFeeAmt(BigDecimal.ZERO);
                }
                repayAfterVo.setAcGuarAmt(BigDecimal.ZERO);
                repayAfterVo.setAcGuarOdAmt(BigDecimal.ZERO);
                if ( repayPlan.getCustRepayState().equals(RepayState.REPAID) ) {
                    repayAfterVo.setIsSetl("Y");
                } else {
                    repayAfterVo.setIsSetl("N");
                }
                if ( repayPlan.getActRepayTime() != null ) {
                    repayAfterVo.setSetlDt(repayPlan.getActRepayTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } else {
                    repayAfterVo.setSetlDt("");
                }
                repayAfterVoList.add(repayAfterVo);
            }
        }
        logger.info("鲸航-绿信还款后还款计划对账文件 : {}", JsonUtil.toJsonString(repayAfterVoList));
        return repayAfterVoList;
    }

}

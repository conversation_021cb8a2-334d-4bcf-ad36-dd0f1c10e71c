package com.maguo.loan.cash.flow.service;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static com.maguo.loan.cash.flow.config.RabbitConfig.RoutingKeys.SIGN_QUERY_DELAY;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public class MqService {
    private static final int DEFAULT_TTL = 60000;
    private static final int DEFAULT_SIGN_TTL = 10000; //10秒
    private static final int DEFAULT_SIGN_NEW_TTL = 5000; // 签署结果查询

    private static final int DEFAULT_RISK_TTL = 10000; //10秒


    private static final int DEFAULT_SMS_NOTIFICATION_DELAY_TTL = ********;

    private static final int DEFAULT_ORDER_LOAN_DELAY_TTL = ********;


    private final RabbitTemplate rabbitTemplate;

    public MqService(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    public void submitSmsSend(String smsSendReq) {
        submit(smsSendReq, null, RabbitConfig.Exchanges.SMS,
                RabbitConfig.RoutingKeys.SMS_SEND, null);
    }

    public void submitSmsSendDelay(String smsSendReq, Map<String, Object> headers) {
        submit(smsSendReq, headers, RabbitConfig.Exchanges.SMS,
                RabbitConfig.RoutingKeys.SMS_SEND_DELAY, DEFAULT_TTL);
    }

    public void submitSmsSend3HDelay(String smsSendReq) {
        submit(smsSendReq, null, RabbitConfig.Exchanges.SMS,
                RabbitConfig.RoutingKeys.SMS_SEND_3H_DELAY, DEFAULT_SMS_NOTIFICATION_DELAY_TTL);
    }

    public void submitRiskApply(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
                RabbitConfig.RoutingKeys.RISK_APPLY, null);
    }

    public void submitRiskApplyOut(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_APPLY_OUT, null);
    }

    public void submitRiskApplyOutDelay(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_APPLY_OUT_DELAY, DEFAULT_SIGN_NEW_TTL);
    }
    public void submitRiskApplyOutDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_APPLY_OUT_DELAY, DEFAULT_TTL);
    }

    public void submitRiskLoanApply(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_LOAN_APPLY, null);
    }

    public void submitBaiWeiRiskLoanApply(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.BW_RISK_LOAN_APPLY, null);
    }

    public void submitMayiAccess(String orderId) {
        submit(orderId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_MAYI_ACCESS, null);
    }

    public void submitCreditUserFileDownload(String orderId) {
        submit(orderId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_USER_FILE_DOWNLOAD, null);
    }
    public void submitFqlCreditUserFileDownload(String orderId) {
        submit(orderId, null, RabbitConfig.Exchanges.FQL_CREDIT,
            RabbitConfig.RoutingKeys.FQL_CREDIT_USER_FILE_DOWNLOAD, null);
    }

    public void submitChannelApply(String channelApplyMessage) {
        submit(channelApplyMessage, null, RabbitConfig.Exchanges.CHANNEL,
                RabbitConfig.RoutingKeys.CHANNEL_APPLY, null);
    }

    public void submitRiskApplyDelay(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
                RabbitConfig.RoutingKeys.RISK_APPLY_DELAY, DEFAULT_SIGN_NEW_TTL);
    }

    public void submitRiskApplyDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
                RabbitConfig.RoutingKeys.RISK_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitRiskLoanApplyDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_LOAN_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitBaiWeiRiskLoanApplyDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.BW_RISK_LOAN_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitCreditUserFileDownloadDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.CREDIT,
            RabbitConfig.RoutingKeys.CREDIT_USER_FILE_DOWNLOAD_DELAY, DEFAULT_TTL);
    }

    public void submitImgFileDownload(String orderId) {
        submit(orderId, null, RabbitConfig.Exchanges.CREDIT,
            RabbitConfig.RoutingKeys.CREDIT_IMGS_FILE_DOWNLOAD, null);
    }
    public void submitImgFileDownloadDelay(String repayId) {
        submitImgFileDownloadDelay(repayId, null);
    }
    public void submitImgFileDownloadDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.CREDIT,
            RabbitConfig.RoutingKeys.CREDIT_IMGS_FILE_DOWNLOAD_DELAY, DEFAULT_TTL);
    }
    public void submitRiskQuery(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
                RabbitConfig.RoutingKeys.RISK_QUERY, null);
    }

    public void submitBaiWeiRiskQuery(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.BW_RISK_QUERY, null);
    }

    public void submitBaiWeiRiskNotify(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.BW_RISK_NOTIFY, null);
    }

    public void submitRiskQueryDelay(String riskId) {
        submitRiskQueryDelay(riskId, null);
    }

    public void submitRiskQueryDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
                RabbitConfig.RoutingKeys.RISK_QUERY_DELAY, DEFAULT_RISK_TTL);
    }
    public void submitRiskOutQuery(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_QUERY_OUT, null);
    }
    public void submitRiskQueryOutDelay(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_QUERY_OUT_DELAY, DEFAULT_RISK_TTL);
    }
    public void submitRiskQueryOutDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_QUERY_OUT_DELAY, DEFAULT_RISK_TTL);
    }

    public void submitRiskResultNoticeOut(String riskId) {
        submit(riskId, null, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_RESULT_NOTICE_OUT, null);
    }


    public void submitRiskResultNoticeOutDelay(String riskId, Map<String, Object> headers) {
        submit(riskId, headers, RabbitConfig.Exchanges.RISK,
            RabbitConfig.RoutingKeys.RISK_RESULT_NOTICE_OUT_DELAY, null);
    }




    public void submitSignApply(String signId) {
        submit(signId, null, RabbitConfig.Exchanges.SIGN,
                RabbitConfig.RoutingKeys.SIGN_APPLY, null);
    }

    public void submitSignApplyDelay(String signId) {
        submit(signId, null, RabbitConfig.Exchanges.SIGN,
                RabbitConfig.RoutingKeys.SIGN_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitSignApplyDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGN,
                RabbitConfig.RoutingKeys.SIGN_APPLY_DELAY, DEFAULT_TTL);
    }


    public void submitSignResultQueryDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGN,
                SIGN_QUERY_DELAY, DEFAULT_SIGN_TTL);
    }

    public void submitNewSignResultQueryDelay(String signId) {
        submitNewSignResultQueryDelay(signId, null);
    }

    public void submitSignResultQueryDelay(String signId) {
        submitSignResultQueryDelay(signId, null);
    }

    public void submitNewSignResultQueryDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGN,
                RabbitConfig.RoutingKeys.SIGN_NEW_QUERY_DELAY_RK, DEFAULT_SIGN_NEW_TTL);
    }


    public void submitCreditRouteApply(String orderId) {
        submit(orderId, null, RabbitConfig.Exchanges.CREDIT, RabbitConfig.RoutingKeys.CREDIT_ROUTE_APPLY, null);
    }

    public void submitCreditRouteResult(String creditId) {
        submit(creditId, null, RabbitConfig.Exchanges.CREDIT, RabbitConfig.RoutingKeys.CREDIT_ROUTE_RESULT, null);
    }


    public void submitCreditApply(String creditId) {
        submit(creditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_APPLY, null);
    }


    public void submitCreditApplyDelay(String creditId) {
        submitCreditApplyDelay(creditId, null);
    }

    public void submitCreditApplyDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitCreditResultQueryDelay(String creditId) {
        submitCreditResultQueryDelay(creditId, null);
    }

    public void submitCreditResultQueryDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitRecreditResultQueryDelay(String creditId) {
        submitRecreditResultQueryDelay(creditId, null);
    }

    public void submitRecreditResultQueryDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.RECREDIT_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanApply(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_APPLY, DEFAULT_TTL);
    }

    public void submitLoanInnerApply(String loanId){
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
            RabbitConfig.RoutingKeys.LOAN_INNER_APPLY, DEFAULT_TTL);
    }

    public void submitLoanApplyDelay(String loanId) {
        submitLoanApplyDelay(loanId, null);
    }

    public void submitLoanApplyDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanResultQueryDelay(String loanId) {
        submitLoanResultQueryDelay(loanId, null);
    }

    public void submitLoanResultQueryDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanRecordApply(String loanRecordId) {
        submit(loanRecordId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_RECORD_APPLY, null);
    }

    public void submitLoanRecordApplyDelay(String loanRecordId) {
        submitLoanRecordApplyDelay(loanRecordId, null);
    }

    public void submitLoanRecordApplyDelay(String loanRecordId, Map<String, Object> headers) {
        submit(loanRecordId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_RECORD_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanRecordResultQueryDelay(String loanRecordId) {
        submitLoanRecordResultQueryDelay(loanRecordId, null);
    }

    public void submitLoanRecordResultQueryDelay(String loanRecordId, Map<String, Object> headers) {
        submit(loanRecordId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_RECORD_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 还款计划同步
     *
     * @param loanId
     */
    public void submitRepayPlanSync(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.REPAY_PLAN_SYNC, null);
    }

    /**
     * 公共（三方）支付扣款
     *
     * @param chargeId
     */
    public void submitChargeApply(String chargeId) {
        submit(chargeId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CHARGE_APPLY, null);
    }

    public void submitChargeQueryDelay(String chargeId) {
        submitChargeQueryDelay(chargeId, null);
    }

    public void submitChargeQueryDelay(String chargeId, Map<String, Object> headers) {
        submit(chargeId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CHARGE_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 还款通知
     *
     * @param repayId
     */
    public void submitRepayNotify(String repayId) {
        submit(repayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_NOTIFY, null);
    }

    public void submitRepayNotifyDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_NOTIFY_DELAY, DEFAULT_TTL);
    }

    public void submitRepayNotifyResultDelay(String repayId) {
        submitRepayNotifyResultDelay(repayId, null);
    }

    public void submitRepayNotifyResultDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_NOTIFY_RESULT_DELAY, DEFAULT_TTL);
    }

    /**
     * 还款申请
     *
     * @param repayId
     */
    public void submitRepay(String repayId) {
        submit(repayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_APPLY, null);
    }

    public void submitRepayQueryDelay(String repayId) {
        submitRepayQueryDelay(repayId, null);
    }

    public void submitRepayQueryDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_QUERY_DELAY, DEFAULT_TTL);
    }


    public void submitPreRepayFee(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.DUE_BATCH_PRE_REPAY_FEE, null);
    }


    public void submitCallbackCommonNotify(String callbackMessage) {
        submit(callbackMessage, null, RabbitConfig.Exchanges.CALLBACK, RabbitConfig.RoutingKeys.CALLBACK_COMMON_NOTIFY, null);
    }

    public void submitCallbackCommonNotifyDelay(String callbackMessage, Map<String, Object> headers) {
        submit(callbackMessage, headers, RabbitConfig.Exchanges.CALLBACK, RabbitConfig.RoutingKeys.CALLBACK_COMMON_NOTIFY_DELAY, DEFAULT_TTL);
    }

    public void submitDueBatchRepay(String loanId, String queue) {
        submit(loanId, null, RabbitConfig.Exchanges.REPAY, queue, null);
    }


    private void submit(String body, Map<String, Object> headers, String exchange, String routingKey, Integer delay) {
        MessageBuilder builder = MessageBuilder.withBody(body.getBytes(StandardCharsets.UTF_8));
        if (delay != null) {
            builder.setExpiration(String.valueOf(delay));
        }
        if (headers != null) {
            headers.forEach(builder::setHeader);
        }
        rabbitTemplate.convertAndSend(exchange, routingKey, builder.build());
    }


    public void submitPreLoanAuditApplyDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.PRE_LOAN_AUDIT_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitPreLoanAuditApply(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.PRE_LOAN_AUDIT_APPLY, null);
    }

    public void submitPreLoanAuditResultDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.PRE_LOAN_AUDIT_RESULT_DELAY, DEFAULT_TTL);
    }

    public void submitRenewedQuery(String recordId) {
        submit(recordId, null, RabbitConfig.Exchanges.RENEWED,
                RabbitConfig.RoutingKeys.RENEWED_QUERY, null);
    }

    public void submitRenewedQueryDelay(String recordId) {
        submit(recordId, null, RabbitConfig.Exchanges.RENEWED,
                RabbitConfig.RoutingKeys.RENEWED_QUERY_DELAY, DEFAULT_TTL);
    }


    public void submitRenewedQueryDelay(String recordId, Map<String, Object> headers) {
        submit(recordId, headers, RabbitConfig.Exchanges.RENEWED,
                RabbitConfig.RoutingKeys.RENEWED_QUERY_DELAY, DEFAULT_TTL);
    }


    public void submitRevolvingAmountUpdateApply(String revolvingAmountId) {
        submit(revolvingAmountId, null, RabbitConfig.Exchanges.REVOLVING,
                RabbitConfig.RoutingKeys.REVOLVING_AMOUNT_UPDATE_APPLY, null);
    }

    public void submitRevolvingAmountUpdateApplyDelay(String revolvingAmountId, Map<String, Object> headers) {
        submit(revolvingAmountId, headers, RabbitConfig.Exchanges.REVOLVING,
                RabbitConfig.RoutingKeys.REVOLVING_AMOUNT_UPDATE_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitOrderDelayAutoLoanDelay(String orderId, Map<String, Object> headers) {
        submit(orderId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.ORDER_DELAY_AUTO_LOAN_DELAY, DEFAULT_ORDER_LOAN_DELAY_TTL);
    }

    public void submitShmPush(List<String> loanIds) {
        String messageBody = String.join(",", loanIds);
        submit(messageBody, null, RabbitConfig.Exchanges.SHM_PUSH,
            RabbitConfig.RoutingKeys.SHM_PUSH_CUS_INFO, null);
    }

    public void submitShmPush(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.SHM_PUSH,
            RabbitConfig.RoutingKeys.SHM_PUSH_CUS_INFO, null);
    }
}

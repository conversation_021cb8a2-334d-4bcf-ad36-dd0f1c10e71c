package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.file.FileDownloadReqDTO;
import com.jinghang.ppd.api.dto.file.FileDownloadRespDTO;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.FinDownloadFileRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.ppd.PpdVoucherRecord;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectContractFlowService;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinClearVoucherFileService;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.FinDownloanFileRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PpdVoucherRecordRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.util.SftpUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Service
public class UserFileService {
    private static final Logger logger = LoggerFactory.getLogger(UserFileService.class);
    private static final long THREE = 3L;
    private static final int FIFTY = 50;
    private UserFileRepository userFileRepository;
    private AgreementSignRelationRepository relationRepository;
    @Autowired
    private PpdConfig ppdConfig;
    @Autowired
    private FinDownloanFileRecordRepository finDownloanFileRecordRepository;
    @Autowired
    private FinClearVoucherFileService finClearVoucherFileService;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private FileService fileService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private PpdVoucherRecordRepository ppdVoucherRecordRepository;

    @Autowired
    private ProjectContractFlowService projectContractFlowService;
    /**
     * 申请资方前，必须签署签署完成的协议
     *
     * @param relateId 关联id
     * @param stage    阶段
     * @return 全部匹配返回所有, 任意一个不匹配 返回null
     */
    public List<UserFile> checkMustCompleted(String userId, String relateId, LoanStage stage,String projectCode) {
        List<UserFile> userFiles = relationRepository.getUserFiles(relateId, stage);
        //根据项目唯一编码获取该用户需要检测签章的文件
        List<ProjectContractFlow> projectFlows = projectContractFlowService.getProjectFlowsByLoanStage(projectCode,stage);
        if (CollectionUtil.isNotEmpty(projectFlows)) {
            for (ProjectContractFlow projectFlow : projectFlows) {
                // 如果agreementTypes中存在任意一个 不在loanFileList列表中
                if (userFiles.stream().noneMatch(l -> projectFlow.getFileType().equals(l.getFileType()))) {
                    logger.warn("{}检测协议文件不存在,{},{}", stage.name(), relateId, projectFlow.getFileType().name());
                    return null;
                }
            }
        }
        if (LoanStage.RISK.equals(stage)) {
            // 如果是授信阶段, 检查影像
            List<FileType> requiredTypes = List.of(FileType.ID_HEAD, FileType.ID_NATION, FileType.ID_FACE);
            List<UserFile> imageFiles = userFileRepository.findByUserIdAndFileTypeIn(userId, requiredTypes);
            for (FileType type : requiredTypes) {
                if (imageFiles.stream().noneMatch(l -> type.equals(l.getFileType()))) {
                    logger.error("检测授信影像文件不存在,{},{}", relateId, type.name());
                    return null;
                }
            }
            userFiles.addAll(imageFiles);
        }

        return userFiles;
    }

    /**
     * 保存身份证人脸面
     *
     * @param userId    用户id
     * @param ossBucket bucket
     * @param ossKey    文件key
     */
    public void saveIdCardFace(String userId, String ossBucket, String ossKey) {
        UserFile uf = checkUserFile(userId, LoanStage.RISK, FileType.ID_HEAD);
        uf.setOssBucket(ossBucket);
        uf.setOssKey(ossKey);
        userFileRepository.save(uf);
    }

    /**
     * 保存身份证国徽面
     *
     * @param userId    用户id
     * @param ossBucket bucket
     * @param ossKey    文件key
     */
    public void saveIdCardNation(String userId, String ossBucket, String ossKey) {
        UserFile uf = checkUserFile(userId, LoanStage.RISK, FileType.ID_NATION);
        uf.setOssBucket(ossBucket);
        uf.setOssKey(ossKey);
        userFileRepository.save(uf);
    }


    /**
     * 保存人脸ocr
     *
     * @param userId    用户id
     * @param ossBucket bucket
     * @param ossKey    文件key
     */
    public void saveFaceOcr(String userId, String ossBucket, String ossKey) {
        UserFile uf = checkUserFile(userId, LoanStage.RISK, FileType.ID_FACE);
        uf.setOssBucket(ossBucket);
        uf.setOssKey(ossKey);
        userFileRepository.save(uf);
    }


    private UserFile checkUserFile(String userId, LoanStage stage, FileType type) {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, stage, type);
        if (uf == null) {
            uf = new UserFile();
            uf.setUserId(userId);
            uf.setLoanStage(stage);
            uf.setFileType(type);
            uf.setFileName(type.getDesc());
        }
        return uf;
    }


    public void downloadByFileType(Loan loan, FileType fileType) {
        FinDownloadFileRecord record = null;
        try {

            List<UserFile> userFiles = relationRepository.findAllByUserFileBySignApplyId(List.of(loan.getId()), LoanStage.LOAN, List.of(fileType));
            if (CollectionUtil.isNotEmpty(userFiles) && userFiles.get(0).getSignFinal() == WhetherState.Y) {
                logger.info("结清证明已存在,userId:{}", loan.getUserId());
                return;
            }
            record = finDownloanFileRecordRepository.findTopByBizIdAndFileTypeOrderByCreatedTimeDesc(
                loan.getId(), fileType);
            if (Objects.isNull(record) || ProcessStatus.FAIL == record.getStatus()) {
                record = new FinDownloadFileRecord();
                record.setBizId(loan.getId());
                record.setFileType(fileType);
                record.setStatus(ProcessStatus.INIT);
                record.setFlowChannel(loan.getFlowChannel());
                record.setBankChannel(loan.getBankChannel());
                record = finDownloanFileRecordRepository.save(record);
            }
            //去下载、查询结清证明
            RestResult<FileDownloadResultDto> result = finDownloadRemote(loan, fileType);
            if (!result.isSuccess()) {
                logger.error("clearVoucherApplyJob 结清证明申请失败, userId:{}", loan.getUserId());
                //下载失败
                handleFailure(result.getMsg(), record);
                return;
            }
            FileDownloadResultDto resultData = result.getData();
            if (resultData == null || resultData.getFileStatus() == null) {
                logger.error("clearVoucherApplyJob 下载结清证明申请失败:返回结果为null, userId:" + loan.getUserId());
                //下载失败
                handleFailure("fin-core返回结果为空", record);
                return;
            }

            //处理中、成功
            handleSuccess(resultData, record, loan);
        } catch (Exception e) {
            logger.error("clearVoucherApplyJob 下载结清证明申请失败, userId:" + loan.getUserId(), e);
            String errMsg = e.getMessage();
            //下载失败
            handleFailure(errMsg, record);
        }
    }

    private void handleSuccess(FileDownloadResultDto resultData, FinDownloadFileRecord record, Loan loan) {
        if (ProcessStatus.PROCESSING == resultData.getFileStatus()) {
            record.setStatus(ProcessStatus.PROCESSING);
            record = finDownloanFileRecordRepository.save(record);
        }
        if (ProcessStatus.SUCCESS == resultData.getFileStatus()) {
            record.setStatus(ProcessStatus.SUCCESS);
            record.setFileName(resultData.getFileName());
            record.setOssBucket(resultData.getOssBucket());
            record.setOssKey(resultData.getOssPath());
            record = finDownloanFileRecordRepository.save(record);
            //保存用户文件
            saveUserFile(loan, resultData);

            Order order = orderRepository.findOrderById(loan.getOrderId());
            FinDownloadFileRecord finalRecord = record;
            fileService.getOssFile(record.getOssBucket(), resultData.getOssPath(), inputStream -> {
                try {
                    String fileName = "";
                    String remoteDir = "";
                    if (FlowChannel.LVXIN.equals(loan.getFlowChannel()) && finalRecord.getFileType().equals(FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        fileName = "creditSettleVoucherFile.pdf";
                        // 新增根据资方渠道区分不同的sftp账号
                        if ( loan.getBankChannel() == BankChannel.CYBK ) {
                            if(IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())){
                                sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getIncludingEquityClearVoucherSftpPath(order.getOuterOrderId()));
                            }else {
                                sftpUtils.uploadStreamToLvXinSftp(inputStream, fileName, lvXinConfig.getClearVoucherSftpPath(order.getOuterOrderId()));
                            }
                        } else if ( loan.getBankChannel() == BankChannel.HXBK ) {
                            sftpUtils.uploadStreamToLvXinNewSftp(inputStream, fileName, lvXinNewSFTPConfig.getClearVoucherSftpPath(order.getOuterOrderId()));
                        }

                    } else if (FlowChannel.PPCJDL.equals(loan.getFlowChannel()) && finalRecord.getFileType().equals(FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                        remoteDir = generateLoanFilePath(loan);
                        fileName = loan.getOuterLoanId() + "_jqpz_zf.pdf";
                        if (loan.getBankChannel() == null) {
                            logger.error("资方渠道值为空");
                            throw new BizException("资方渠道值为空", ResultCode.BIZ_ERROR);
                        } else if (loan.getBankChannel() == BankChannel.CYBK) {
                            logger.info("上传结清证明文件, 资方渠道: CYBK, 文件名: {}, 路径: {}", fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                            sftpUtils.uploadStreamToPPCJDLSftp(inputStream, fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                        } else {
                            logger.info("上传结清证明文件, 资方渠道: HXBK, 文件名: {}, 路径: {}", fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                            sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                        }
                        logger.info("上传结清证明文件完成，资方渠道：{}", loan.getBankChannel());
                        PpdVoucherRecord ppdVoucherRecord = new PpdVoucherRecord();
                        ppdVoucherRecord.setLoanId(loan.getId());
                        ppdVoucherRecord.setFileName(fileName);
                        ppdVoucherRecord.setFilePath(ppdConfig.getSftpDownloadPath() + remoteDir);
                        ppdVoucherRecordRepository.save(ppdVoucherRecord);
                    }

                } catch (Exception e) {
                    logger.error("结清证明文件上传绿信sftp失败:", e);
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private String generateLoanFilePath(Loan loan) {
        if (loan == null || loan.getApplyTime() == null || StringUtils.isBlank(loan.getOuterLoanId())) {
            return "";
        }
        String formattedDate = formatLocalDateTime(loan.getLoanTime());
        String outerLoanId = loan.getOuterLoanId();
        logger.info("generateLoanTimeFilePath:{}", formattedDate + "/" + outerLoanId + "/");
        return formattedDate + "/" + outerLoanId + "/";
    }

    /**
     * 将 LocalDateTime 转换为格式为 "yyyyMMdd" 的字符串
     *
     * @param dateTime LocalDateTime 对象
     * @return 格式化后的字符串
     */
    private static String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }

    private void handleFailure(String errMsg, FinDownloadFileRecord record) {
        if (record != null) {
            if (errMsg.length() > FIFTY) {
                errMsg = errMsg.substring(0, FIFTY);
            }
            record.setStatus(ProcessStatus.FAIL);
            record.setFailReason(errMsg);
            record = finDownloanFileRecordRepository.save(record);
        }
    }

    private RestResult<FileDownloadResultDto> finDownloadRemote(Loan loan, FileType fileType) {
        FileDownloadDto request = new FileDownloadDto();
        request.setLoanId(loan.getLoanNo());
        request.setBankChannel(loan.getBankChannel());
        request.setType(EnumConvert.INSTANCE.toCoreApi(fileType));
        logger.info("下载结清证明,fin-core 请求参数:{}", JsonUtil.toJsonString(request));
        RestResult<FileDownloadResultDto> result = finClearVoucherFileService.download(request);
        logger.info("下载结清证明,fin-core 返回结果:{}", JsonUtil.toJsonString(result));
        return result;
    }

    private void saveUserFile(Loan loan, FileDownloadResultDto resultData) {

        FileType cashFileType = FileType.CREDIT_SETTLE_VOUCHER_FILE;

        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        userFile.setLoanStage(LoanStage.LOAN);
        userFile.setLoanNo(loan.getId());
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : cashFileType.getDesc();
        userFile.setFileName(fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        //需要保存协议关系表
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(LoanStage.LOAN);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        relationRepository.save(agreementSignRelation);
    }


    @Autowired
    public void setUserFileRepository(UserFileRepository userFileRepository) {
        this.userFileRepository = userFileRepository;
    }

    @Autowired
    public void setRelationRepository(AgreementSignRelationRepository relationRepository) {
        this.relationRepository = relationRepository;
    }

    public FileDownloadRespDTO download(FileDownloadReqDTO req) {
        FileType fileType = FileType.valueOf(req.getType());
        Loan byOrderId = loanRepository.findByOrderId(req.getOrderId());
        Loan loan = Optional.of(byOrderId).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));

        List<UserFile> userFiles = relationRepository.findAllByUserFileBySignApplyId(List.of(loan.getId()), LoanStage.LOAN, List.of(fileType));

        if (CollectionUtil.isNotEmpty(userFiles) && userFiles.get(0).getSignFinal() == WhetherState.Y) {
            UserFile userFile = userFiles.get(0);
            FileDownloadRespDTO result = new FileDownloadRespDTO();
            result.setFileStatus(ProcessStatus.SUCCESS.name());
            result.setFileName(userFile.getFileName());
            result.setOssBucket(userFile.getOssBucket());
            result.setOssPath(userFile.getOssKey());
            //有效期3天
            String ossUrl = fileService.getOssUrl(userFile.getOssBucket(), userFile.getOssKey(), THREE);
            result.setFileUrl(ossUrl);
            return result;
        }
        //去fin-core下载
        RestResult<FileDownloadResultDto> finResult = finDownloadRemote(loan, fileType);
        if (finResult.isSuccess()) {
            FileDownloadResultDto resultData = finResult.getData();
            if (ProcessStatus.SUCCESS == finResult.getData().getFileStatus()) {
                //如果给了Oss路径,没有给Url,这里获取一下Url
                if (StringUtil.isBlank(resultData.getFileUrl()) && StringUtil.isNotBlank(resultData.getOssPath())) {
                    String ossUrl = fileService.getOssUrl(resultData.getOssBucket(), resultData.getOssPath());
                    resultData.setFileUrl(ossUrl);
                    resultData.setFileStatus(ProcessStatus.SUCCESS);
                }
                //保存用户文件
                saveUserFile(loan, resultData);
                return ManageConvert.INSTANCE.toFileDownloadResp(resultData);
            } else {
                FileDownloadRespDTO resp = new FileDownloadRespDTO();
                resp.setFileStatus(resultData.getFileStatus().name());
                return resp;
            }
        } else {
            throw new BizException("下载失败,请联系管理员", ResultCode.BIZ_ERROR);
        }
    }
}

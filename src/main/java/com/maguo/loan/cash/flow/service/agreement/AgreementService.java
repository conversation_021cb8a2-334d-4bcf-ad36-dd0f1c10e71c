package com.maguo.loan.cash.flow.service.agreement;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.common.util.CurrencyUtil;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectContractFlowService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.AgreementSignRsp;
import com.maguo.loan.cash.flow.remote.sign.req.AuthDto;
import com.maguo.loan.cash.flow.remote.sign.req.Face;
import com.maguo.loan.cash.flow.remote.sign.req.SignApplyReq;
import com.maguo.loan.cash.flow.remote.sign.res.ResultMsg;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Service
public class AgreementService {
    private static final Logger logger = LoggerFactory.getLogger(AgreementService.class);

    @Autowired
    private SignatureManager signatureManager;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private MqService mqService;

    @Autowired
    private AgreementSignatureRecordRepository signatureRecordRepository;

    @Autowired
    private AgreementSignRelationRepository signRelationRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private UserRiskRecordExternalRepository userRiskRecordExternalRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Value(value = "${agreement.oss.key.path}")
    private String ossPath;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private ProjectContractFlowService projectContractFlowService;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private UserFileRepository userFileRepository;

    private static final int REPAY_DAY_LIMIT = 28;

    private static final int CONTRACT_LENGTH = 22;



    /**
     * 阶段
     *
     * @param orderId
     * @param stage
     * @param projectCode  项目唯一编码
     */
    public void applySign(String orderId, LoanStage stage, String projectCode) {
        logger.info("业务协议签署 - 订单阶段: orderId={}, stage={}, projectCode={}", orderId, stage, projectCode);
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));

        processSigning(projectCode,stage, projectContractFlow ->
            createSign(order, stage, projectContractFlow));
    }

    /**
     * 风控前
     *
     * @param riskId 风控id
     */
    public void applyRegisterSign(String riskId, String projectCode) {
        logger.info("业务协议签署 - 注册阶段: riskId={}, projectCode={}", riskId, projectCode);

        processSigning(projectCode,LoanStage.RISK, projectContractFlow ->
            createSignRisk(riskId, projectContractFlow));
    }

    /**
     * 风控前-外部风控
     *
     * @param riskId 风控id
     */
    public void applyRegisterSignExternal(final String riskId,String projectCode) {

        logger.info("业务协议签署 - 注册阶段: riskId={}, projectCode={}", riskId, projectCode);
        processSigning(projectCode,LoanStage.RISK, projectContractFlow ->
            createSignRiskExternal(riskId, projectContractFlow));


    }

    /**
     * 处理签署流程的公共方法
     * @param projectCode 项目编码
     * @param  stage 流量方贷款阶段
     * @param signCreator 签署记录创建函数
     */
    private void processSigning(String projectCode, LoanStage stage,Function<ProjectContractFlow, AgreementSignatureRecord> signCreator) {
        // 获取项目合同流程配置
        //todo 查询后管系统提供接口 需要新增目前系统标识 （资产 or 资金）
        List<ProjectContractFlow> flows = projectContractFlowService.getProjectFlowsByLoanStage(projectCode,stage);
        if (CollectionUtils.isEmpty(flows)) {
            logger.warn("项目[{}]未配置合同流程", projectCode);
            throw new BizException(ResultCode.AGREEMENT_NOT_CONFIG);
        }

        // 处理每个流程配置
        flows.stream()
            .map(signCreator)
            .filter(Objects::nonNull)
            .forEach(agreementSignature -> {
                try {
                    mqService.submitSignApply(agreementSignature.getId());
                } catch (Exception e) {
                    logger.error("提交签署申请失败, agreementId={}", agreementSignature.getId(), e);
                    throw new BizException(ResultCode.SUBMIT_SIGN_FAILED);
                }
            });
    }


    /**
     * 外部风控
     * @param riskId
     * @param flow
     * @return
     */
    private AgreementSignatureRecord createSignRiskExternal(String riskId, ProjectContractFlow flow) {
        UserRiskRecordExternal riskRecord = userRiskRecordExternalRepository.findById(riskId).orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST));
        String userId = riskRecord.getUserId();
        AgreementSignatureRecord signatureRecord = new AgreementSignatureRecord();
        signatureRecord.setUserId(userId);
        signatureRecord.setRiskId(riskRecord.getId());
        //todo 替换为配置表中contract_template_type
        signatureRecord.setFileType(flow.getFileType());
        signatureRecord.setLoanStage(flow.getLoanStage());
        //todo 替换为配置表中template_no
        signatureRecord.setTemplateNo(flow.getTemplateCode());
        //signatureRecord.setAgreementType(agreementType);
        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow();
        signatureRecord.setBankMobilePhone(userInfo.getMobile());
        signatureRecord.setPersonName(userInfo.getName());
        signatureRecord.setSignState(ProcessState.INIT);
        signatureRecord.setIdentNo(userInfo.getCertNo());
        signatureRecord.setAddress(userInfo.getLivingAddress());
        signatureRecord = signatureRecordRepository.save(signatureRecord);
        // signatureRelation
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setRelatedId(riskId);
        agreementSignRelation.setLoanStage(flow.getLoanStage());

        agreementSignRelation.setSignApplyId(signatureRecord.getId());
        agreementSignRelation.setUserId(userId);
        agreementSignRelation.setOrderId(null);
        signRelationRepository.save(agreementSignRelation);
        return signatureRecord;
    }

    private AgreementSignatureRecord createSignRisk(String riskId, ProjectContractFlow projectContractFlow) {
        UserRiskRecord riskRecord = userRiskRecordRepository.findById(riskId).orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST));
        String userId = riskRecord.getUserId();
        AgreementSignatureRecord signatureRecord = new AgreementSignatureRecord();
        signatureRecord.setUserId(userId);
        signatureRecord.setRiskId(riskRecord.getId());
        //todo 替换为配置表中contract_template_type
        signatureRecord.setFileType(projectContractFlow.getFileType());
        signatureRecord.setLoanStage(projectContractFlow.getLoanStage());
        //todo 替换为配置表中template_no
        signatureRecord.setTemplateNo(projectContractFlow.getTemplateCode());
        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow();
        signatureRecord.setBankMobilePhone(userInfo.getMobile());
        signatureRecord.setPersonName(userInfo.getName());
        signatureRecord.setSignState(ProcessState.INIT);
        signatureRecord.setIdentNo(userInfo.getCertNo());
        signatureRecord.setAddress(userInfo.getLivingAddress());
        signatureRecord = signatureRecordRepository.save(signatureRecord);
        // signatureRelation
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setRelatedId(riskId);
        agreementSignRelation.setLoanStage(projectContractFlow.getLoanStage());

        agreementSignRelation.setSignApplyId(signatureRecord.getId());
        agreementSignRelation.setUserId(userId);
        agreementSignRelation.setOrderId(null);
        signRelationRepository.save(agreementSignRelation);
        return signatureRecord;
    }


    private AgreementSignatureRecord createSign(Order order, LoanStage stage, ProjectContractFlow projectContractFlow) {
        AgreementSignatureRecord signatureRecord = new AgreementSignatureRecord();
        //权益客户需要排除 {咨询服务合同(超捷).pdf}
        if(IsIncludingEquity.Y.equals(order.getIsIncludingEquity())){
            //todo 替换为配置表中contract_template_type
            if(FileType.CONSULTING_SERVICE_CONTRACT.name().equals(projectContractFlow.getFileType().name())){
                return null;
            }
        }
        signatureRecord.setUserId(order.getUserId());
        signatureRecord.setRiskId(order.getRiskId());
        //todo 替换为配置表中contract_template_type
        signatureRecord.setFileType(projectContractFlow.getFileType());
        signatureRecord.setLoanStage(projectContractFlow.getLoanStage());
        //todo 替换为配置表中template_no
        signatureRecord.setTemplateNo(projectContractFlow.getTemplateCode());
        UserInfo userInfo = userInfoRepository.findById(order.getUserId()).orElseThrow();
        signatureRecord.setBankMobilePhone(userInfo.getMobile());
        signatureRecord.setPersonName(userInfo.getName());
        signatureRecord.setSignState(ProcessState.INIT);
        signatureRecord.setIdentNo(userInfo.getCertNo());
        signatureRecord.setAddress(userInfo.getLivingAddress());
        signatureRecord = signatureRecordRepository.save(signatureRecord);
        // signatureRelation
        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        //
        if (LoanStage.CREDIT.equals(stage)) {
            Credit credit = creditRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow();
            agreementSignRelation.setRelatedId(credit.getId());
        }
        if (LoanStage.LOAN.equals(stage) || LoanStage.REPAY.equals(stage)) {
            Loan loan = loanRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }

        agreementSignRelation.setLoanStage(stage);
        agreementSignRelation.setSignApplyId(signatureRecord.getId());
        agreementSignRelation.setUserId(order.getUserId());
        agreementSignRelation.setOrderId(order.getId());
        signRelationRepository.save(agreementSignRelation);

        return signatureRecord;
    }


    /**
     * 开始签章，监听方法入口
     *
     * @param signId agreementSignRecord Id
     */
    public void signApply(String signId) {
        logger.info("开始签章{}", JsonUtil.toJsonString(signId));
        AgreementSignatureRecord agreementSignature = signatureRecordRepository.findById(signId).orElseThrow();
        AgreementSignRelation signRelation = signRelationRepository.findBySignApplyId(agreementSignature.getId()).orElseThrow();
        // 请求签署
        SignApplyReq signReq = new SignApplyReq();
        buildReq(signRelation, agreementSignature, signReq);
        logger.info("调用签章接口请求参数{}", JsonUtil.toJsonString(signReq));
        ResultMsg stringRestResult = signatureManager.signatureApply(signReq);
        logger.info("调用签章接口返回结果{}", JsonUtil.toJsonString(stringRestResult));
        AgreementSignRsp agreementSignRsp = new AgreementSignRsp();
        agreementSignRsp.setTaskId(stringRestResult.getObject() == null ? null : stringRestResult.getObject().toString());
        agreementSignRsp.setFailMsg(stringRestResult.getMessage());
        agreementSignRsp.setState(ProcessState.PROCESSING);
        agreementSignRsp.setUserId(agreementSignRsp.getUserId());
        agreementSignature.setSignState(ProcessState.PROCESSING);
        agreementSignature.setCommonTaskId(agreementSignRsp.getTaskId());
        agreementSignature = signatureRecordRepository.save(agreementSignature);
        // 查询提交
        mqService.submitSignResultQueryDelay(agreementSignature.getId());
    }

    private Map<String, String> buildReq(AgreementSignRelation signRelation, AgreementSignatureRecord agreementSignature, SignApplyReq signReq) {
        UserInfo userInfo = userInfoRepository.findById(signRelation.getUserId()).orElseThrow();
        UserOcr userOcr = userOcrRepository.findByUserId(signRelation.getUserId());
        UserFace userFace = userFaceRepository.findByUserId(signRelation.getUserId());
        agreementSignature.setCommonUserId(signRelation.getUserId());
        LoanStage stage = agreementSignature.getLoanStage();
        signReq.setAcctName(userInfo.getName());
        signReq.setAddress(userInfo.getLivingAddress());
        signReq.setPhone(userInfo.getMobile());

        //获取流量渠道和资金渠道
        String riskId = agreementSignature.getRiskId();//风控id
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
        String projectCode = preOrder.getProjectCode();
        //todo 通过后管配置查询对应的项目信息 找到该配置是否属于分期乐
        ProjectInfoVO projectInfoVO = projectInfoService.queryProjectInfo(projectCode);
        if (projectInfoVO == null) {
            logger.error("项目编码{}未配置", projectCode);
            throw new BizException(ResultCode.PROJECT_NOT_CONFIG);
        }
        //只有风控阶段再进行判断是否为分期乐
        if (stage.equals(LoanStage.RISK)){
            if (FlowChannel.FQLQY001.name().equals(projectInfoVO.getFlowChannel())){
                signReq.setGuaranteeCompany( projectInfoVO.getGuaranteeCode());
            }
        }

        AuthDto authDto = new AuthDto();

        //国徽面
        authDto.setCardNationalEmblem(userOcr.getNationOssKey());
        //人脸
        authDto.setCardPortrait(userOcr.getHeadOssKey());
        authDto.setCardNo(userInfo.getCertNo());
        authDto.setCardType("CRED_PSN_CH_IDCARD");
        authDto.setEviName(IdGen.genId());
        Face face = new Face();

        face.setFaceImage(userFace.getOssKey());
        face.setVerifySerialNumber(userFace.getId());
        if (StringUtils.isNotEmpty(signReq.getGuaranteeCompany()) && GuaranteeCompany.HYRD.name().equals(signReq.getGuaranteeCompany())){
            //HYRD担保
            face.setFaceImageSimilarScore("95");
            face.setFaceLiveDetectScore("95");
            face.setInfoVerifySupplier("Face++");
        }else {
            face.setFaceImageSimilarScore(userFace.getFaceScore() == null ? "0" : userFace.getFaceScore().toString());
            face.setFaceLiveDetectScore(userFace.getFaceScore() == null ? "0" : userFace.getFaceScore().toString());
            face.setInfoVerifySupplier(userFace.getFacialSupplier());
        }
        long epochMilli = Instant.now().toEpochMilli();
        face.setVerifyTime(String.valueOf(epochMilli));
        authDto.setFace(face);
        authDto.setName(userInfo.getName());
        authDto.setRealNameMethod("FACE");
        authDto.setTimeStamp(epochMilli);
        //同步的用户的id
        signReq.setAuthDto(authDto);

        if (FlowChannel.FQLQY001.name().equals(projectInfoVO.getFlowChannel())){
            UserRiskRecordExternal userRiskRecordExternal = userRiskRecordExternalRepository.findTopByUserIdOrderByCreatedTimeDesc(userInfo.getId());
            signReq.setTrafficCode(userRiskRecordExternal.getFlowChannel().name());
        }else {
            UserRiskRecord userRiskRecord = userRiskRecordRepository.findTopByUserIdOrderByCreatedTimeDesc(userInfo.getId());
            signReq.setTrafficCode(userRiskRecord.getFlowChannel().name());
        }
        if (!stage.equals(LoanStage.RISK)) {
            Order order = orderRepository.findById(signRelation.getOrderId()).orElseThrow();
            Credit credit = creditRepository.findTopByOrderIdOrderByCreatedTimeDesc(order.getId()).orElseThrow();
            signReq.setCreditId(credit.getId());
            if (StringUtils.isNotBlank(order.getLoanCardId())) {
                UserBankCard userBankCard = userBankCardRepository.findById(order.getLoanCardId())
                    .orElseThrow(() -> new BizException(ResultCode.BANK_CARD_NOT_FOUND));
                signReq.setBankCardNo(userBankCard.getCardNo());
                signReq.setBankName(userBankCard.getBankName());
            }
            if (stage.equals(LoanStage.LOAN)) {
                Loan loan = loanRepository.findByOrderId(order.getId());
                signReq.setAmount(loan.getAmount().toPlainString());
                signReq.setCapitalRmb(CurrencyUtil.number2Chinese(loan.getAmount()));
                //  signReq.setCreditId(credit.getId());
                // 贷款起始日期
                LocalDate loanStartDate = loan.getLoanTime().toLocalDate();
                LocalDate loanEndDate = loanStartDate.plusMonths(credit.getPeriods().longValue());
                if (loanEndDate.getDayOfMonth() > REPAY_DAY_LIMIT) {
                    loanEndDate = loanEndDate.minusDays(loanEndDate.getDayOfMonth() - REPAY_DAY_LIMIT);
                }
                signReq.setDueDay(String.format("%02d", loanEndDate.getDayOfMonth()));
                signReq.setDueMonth(String.format("%02d", loanEndDate.getMonthValue()));
                signReq.setDueYear(String.valueOf(loanEndDate.getYear()));
                signReq.setLoanActvDay(String.format("%02d", loanStartDate.getDayOfMonth()));
                signReq.setLoanActvMonth(String.format("%02d", loanStartDate.getMonthValue()));
                signReq.setLoanActvYear(String.valueOf(loanStartDate.getYear()));
                signReq.setAmountNo(loan.getLoanContractNo());
                signReq.setStagesNumber(loan.getPeriods().toString());

                List<RepayPlan> byLoanId = repayPlanRepository.findByLoanId(loan.getId());
                BigDecimal serviceFee = AmountUtil.calcSumAmount(byLoanId, item -> AmountUtil.safeAmount(item.getConsultFee()));
                signReq.setServiceFee(serviceFee.toPlainString());
            }
        }
        ProjectContractFlow projectContractFlowsByAgreementCode = projectContractFlowService.getProjectContractFlowsByAgreementCode(agreementSignature.getAgreementType());
        signReq.setContractCode(projectContractFlowsByAgreementCode.getTemplateCode());
        String jsonString = JsonUtil.toJsonString(signReq);
        logger.info("buildParams:{}", jsonString);
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(signReq, Map.class);
    }

    /**
     * 查询签章入口
     *
     * @param signId 签章记录
     */
    public void querySign(String signId) {
        AgreementSignatureRecord agreementSignature = signatureRecordRepository.findById(signId).orElseThrow();
        logger.info("调用签章查询接口请求参数{}", signId);
        // 查询协议签署结果
        // 查询协议签署结果
        ResultMsg resultMsg = signatureManager.signatureResultQuery(agreementSignature.getCommonTaskId());
        if ("200".equals(resultMsg.getCode())) {
            //签章类型唯一 根据类型找到对应的签章
            String agreementType = agreementSignature.getAgreementType();
            ProjectContractFlow projectContractFlow = projectContractFlowService.getProjectContractFlowsByAgreementCode(agreementType);
            // agreement
            agreementSignature.setSignState(ProcessState.SUCCEED);
            agreementSignature.setCommonOssUrl(resultMsg.getObject() == null ? null : resultMsg.getObject().toString());
            agreementSignature = signatureRecordRepository.save(agreementSignature);
            String userId = agreementSignature.getUserId();
            // loanFile
            UserFile userFile = userFileRepository.findById(agreementSignature.getId()).orElse(new UserFile());
            userFile.setId(agreementSignature.getId());
            userFile.setUserId(userId);
            userFile.setFileType(agreementSignature.getFileType());
            userFile.setFileName(agreementSignature.getFileType().getDesc());

            userFile.setOssBucket(ossBucket);
            //直接使用接口返回的oss-key
            userFile.setOssKey(agreementSignature.getCommonOssUrl());
            userFile.setSignFinal(projectContractFlow.getNeedSignAgain() ? WhetherState.N : WhetherState.Y);
            userFile.setLoanStage(projectContractFlow.getLoanStage());
            logger.info("上传成功后,保存userFile:{}", JSON.toJSONString(userFile));
            String ossFile = fileService.getOssUrl(ossBucket, agreementSignature.getCommonOssUrl());
            logger.info("协议文件：{}", ossFile);
            userFileRepository.save(userFile);
        } else {
            warningService.warn("签章申请,业务失败:" + resultMsg.getMessage());
        }

    }
    /**
     * 查询已签章协议
     */
    public List<AgreementShow> queryAgreementByRelatedId(String relatedId, LoanStage loanStage) {
        List<UserFile> userFiles = signRelationRepository.getUserFiles(relatedId, loanStage);
        if (CollectionUtils.isEmpty(userFiles)) {
            return new ArrayList<>();
        }
        List<AgreementShow> result = new ArrayList<>(userFiles.size());
        userFiles.stream()
            // 去重
            .collect(Collectors.toMap(UserFile::getFileType, value -> value, (v1, v2)
                -> {
                if (v1.getCreatedTime().isAfter(v2.getCreatedTime())) {
                    return v1;
                }
                return v2;
            })).values().forEach(file -> {
                AgreementShow response = new AgreementShow();
                response.setAgreementName(file.getFileName());
                //获取Url，1小时有效期
                String ossUrl = fileService.getOssUrl(file.getOssBucket(), file.getOssKey(), DateUtil.toDate(LocalDateTime.now().plusHours(1)));
                response.setAgreementUrl(ossUrl);
                result.add(response);
            });
        return result;
    }


}

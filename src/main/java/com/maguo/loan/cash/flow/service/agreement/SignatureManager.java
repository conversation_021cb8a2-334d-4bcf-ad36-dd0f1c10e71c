package com.maguo.loan.cash.flow.service.agreement;


import com.maguo.loan.cash.flow.remote.sign.SignService;
import com.maguo.loan.cash.flow.remote.sign.req.SignApplyReq;
import com.maguo.loan.cash.flow.remote.sign.res.ResultMsg;
import com.maguo.loan.cash.flow.service.MqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2024/6/29
 */
@Service
public class SignatureManager {

    private static final Logger logger = LoggerFactory.getLogger(SignatureManager.class);

    @Autowired
    private SignService signService;

    private MqService mqService;

    /**
     * 协议签署申请
     *
     * @param signReq
     * @return
     */
    public ResultMsg signatureApply(SignApplyReq signReq) {
        return signService.sign(signReq);
    }

    /**
     * 协议签署结果查询
     *
     * @param taskId
     * @return
     */
    public ResultMsg signatureResultQuery(String taskId) {
        return signService.getSignUrl(taskId);
    }


    @Autowired
    public void setMqService(MqService mqService) {
        this.mqService = mqService;
    }
}

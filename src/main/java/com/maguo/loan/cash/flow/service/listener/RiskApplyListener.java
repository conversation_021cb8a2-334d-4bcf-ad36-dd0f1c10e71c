package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 内部风控监听
 */
@Component
public class RiskApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RiskApplyListener.class);

    private RiskService riskService;


    @Value("${qh.risk.suspend:false}")
    private Boolean riskSuspend;

    @Autowired
    private UserRiskRecordRepository riskRecordRepository;
    @Autowired
    private UserRiskRecordExternalRepository recordExternalRepository;

    @Autowired
    private UserRiskRecordExternalRepository RiskRecordExternalRepository;


    public RiskApplyListener(MqService mqService, WarningService mqWarningService, RiskService riskService) {
        super(mqService, mqWarningService);
        this.riskService = riskService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_APPLY)
    public void listenRiskApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请风控, riskId: [{}]", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                riskRecordRepository.save(record);
                return;
            }

            riskService.platformRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "内部风控异常", getMqService()::submitRiskApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }


    @RabbitListener(queues = RabbitConfig.Queues.RISK_APPLY_OUT)
    public void listenRiskApplyOut(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请外部风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("外部风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("外部风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                recordExternalRepository.save(record);
                return;
            }

            riskService.platformRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "外部风控异常", getMqService()::submitRiskApplyOutDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_LOAN_APPLY)
    public void listenRiskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请支用风控, riskId: [{}]", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                riskRecordRepository.save(record);
                return;
            }
              riskService.platformLoanRisk(record);

        } catch (Exception e) {
            processException(riskId, message, e, "内部风控异常", getMqService()::submitRiskLoanApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    @RabbitListener(queues = RabbitConfig.Queues.BW_RISK_LOAN_APPLY)
    public void listenBaiWeiRiskLoanApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("百维：申请支用风控, riskId: [{}]", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (AuditState.REJECT == record.getApproveResult()) {
                logger.warn("风控记录,riskId: [{}],状态为已拒绝,忽略此消息", riskId);
                return;
            }

            //风控挂起
            if (riskSuspend) {
                record.setApproveResult(AuditState.SUSPEND);
                RiskRecordExternalRepository.save(record);
                return;
            }
            if (FlowChannel.FQLQY001.equals(record.getFlowChannel())){
                riskService.baiWeiPlatformLoanRisk(record);
            }



        } catch (Exception e) {
            processException(riskId, message, e, "百维：申请支用风控异常", getMqService()::submitBaiWeiRiskLoanApplyDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }


    @RabbitListener(queues = RabbitConfig.Queues.ARTIFICIAL_RISK_APPLY)
    public void listenArtificialRiskApply(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("人工申请风控:{}", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);

            riskService.platformRisk(record);
        } catch (Exception e) {
            logger.error("人工申请风控异常:", e);
            getMqWarningService().warn("人工申请风控异常,riskId" + riskId + ":" + e.getMessage());
        } finally {
            ackMsg(riskId, message, channel);
        }
    }
}

package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecordExternal;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordExternalRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 内部风控结果查询监听
 */
@Component
public class RiskQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RiskQueryListener.class);

    private RiskService riskService;
    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private UserRiskRecordExternalRepository RiskRecordExternalRepository;

    public RiskQueryListener(MqService mqService, WarningService mqWarningService,
                             AgreementService agreementService, RiskService riskService) {
        super(mqService, mqWarningService);
        this.riskService = riskService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.RISK_QUERY)
    public void listenRiskQuery(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请风控查询:{}", riskId);
            UserRiskRecord record = riskService.findPlatformRiskRecord(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            riskService.platformRiskQuery(record);
        } catch (Exception e) {
            processException(riskId, message, e, "内部风控查询异常", getMqService()::submitRiskQueryDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }


    @RabbitListener(queues = RabbitConfig.Queues.RISK_QUERY_OUT)
    public void listenRiskQueryOut(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请外部风控查询:{}", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("外部风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            riskService.platformRiskQuery(record);
        } catch (Exception e) {
            processException(riskId, message, e, "外部风控查询异常", getMqService()::submitRiskQueryOutDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    /**
     *
     *  百维：支用风控审核结果查询
     *
     * */
    @RabbitListener(queues = RabbitConfig.Queues.BW_RISK_QUERY)
    public void listenBaiWeiRiskQuery(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("申请风控查询:{}", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (FlowChannel.FQLQY001.equals(record.getFlowChannel())){
                riskService.baiWeiPlatformRiskQuery(record);
            }
        } catch (Exception e) {
            processException(riskId, message, e, "内部风控查询异常", getMqService()::submitRiskQueryDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

    /**
     *
     *  百维：支用风控审核结果通知
     *
     * */
    @RabbitListener(queues = RabbitConfig.Queues.BW_RISK_NOTIFY)
    public void listenBaiWeiRiskNotify(Message message, Channel channel) {
        String riskId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("风控审核结果通知:{}", riskId);
            UserRiskRecordExternal record = riskService.findPlatformRiskRecordExternal(riskId);
            if (record == null || record.getUserId() == null) {
                getMqWarningService().warn("风控记录, riskId: " + riskId + ", 用户id 缺失");
                return;
            }
            if (FlowChannel.FQLQY001.equals(record.getFlowChannel())){
                riskService.baiWeiPlatformRiskNotify(record);
            }
        } catch (Exception e) {
            processException(riskId, message, e, "风控审核结果通知异常", getMqService()::submitRiskQueryDelay);
        } finally {
            ackMsg(riskId, message, channel);
        }
    }

}

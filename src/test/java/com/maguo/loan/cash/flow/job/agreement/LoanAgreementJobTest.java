package com.maguo.loan.cash.flow.job.agreement;

import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.job.repayment.RepaymentBenefitSplitJob;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> gale
 * @Classname LoanAgreementJobTest
 * @Description TODO
 * @Date 2025/6/4 20:26
 */
@SpringBootTest
class LoanAgreementJobTest {

    @Autowired
    private LoanAgreementJob loanAgreementJob;


    /**
     *
     * COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE
     *
     *
     */
    @Test
    void doJob() {
        JobParam jobParam = new JobParam();
        jobParam.setLoanIds(List.of("LO250604103909933584443546405765"));
        loanAgreementJob.doJob(jobParam);


    }
    @Autowired
    private RepaymentBenefitSplitJob repaymentBenefitSplitJob;

    @Test
    void doJobRepayBenefitSplit() {
        JobParam jobParam = new JobParam();

        repaymentBenefitSplitJob.doJob(jobParam);


    }
}

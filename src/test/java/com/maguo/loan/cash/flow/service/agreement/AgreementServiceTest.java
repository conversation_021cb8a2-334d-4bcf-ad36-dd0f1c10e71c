package com.maguo.loan.cash.flow.service.agreement;

import com.alibaba.fastjson2.JSON;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> gale
 * @Classname AgreementServiceTest
 * @Description TODO
 * @Date 2025/5/30 10:40
 */
@SpringBootTest
class AgreementServiceTest {

    @Autowired
    private AgreementService agreementService;
    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;

    public static void main(String[] args) throws JSchException {
        String type = LXProtocolType.toProtocolType(String.valueOf("AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE"));
        System.out.println("start");
        JSch jsch = new JSch();
        Session session = jsch.getSession("ftp", "**************", 22);
        session.setPassword("JingHang!@123");
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect(10000);
        System.out.println("final");
    }
    @Test
    void upload() {
//        String ossFile = fileService.getOssUrl(ossBucket, "ossKey10d1dcfa483f434c96ec769950f54b6c.pdf");
//        System.out.println(ossFile);
        System.out.println("查询");
        Order order = orderRepository.findByRiskId("RR250602095829270536151658704786");
        System.out.println("准备上传");
        fileService.getOssFile(ossBucket, "ossKey10d1dcfa483f434c96ec769950f54b6c.pdf", inputStream -> {
            try {
                System.out.println("开始上传");
                String type = LXProtocolType.toProtocolType(String.valueOf("AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE"));

                sftpUtils.uploadStreamToLvXinSftp(inputStream,type , lvXinConfig.getAgreementSftpPath(order.getOuterOrderId()));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Test
    void applySign() {
        agreementService.signApply("UF250530133940486198646471791359");
    }

    @Test
    void applyRegisterSign() {
        agreementService.applyRegisterSign("RR250528172906115829112476309538", "FlowChannel.LVXIN, BankChannel.HXBK");
    }

    @Test
    void signApply() {

    }

    @Test
    void querySign() {
        agreementService.querySign("UF250530133940486198646471791359");
    }

    @Test
    void queryAgreementByRelatedId() {
    }
}

package com.jinghang.ppd.api;

import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.SmsSendReq;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调用cash-business发送短信
 */
public interface SmsSendService {

    @PostMapping("sendMessage")
    RestResult<String> sendMessage(@RequestBody SmsSendReq smsSendReq);

}
